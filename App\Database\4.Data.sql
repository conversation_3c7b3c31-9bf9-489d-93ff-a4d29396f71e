-- Insert Tags (mimicking AWS resource types and common tags)
INSERT INTO Tags (TagName) VALUES
('EC2'), ('Lambda'), ('S3'), ('RDS'), ('DynamoDB'), ('EKS'), ('IAM'), ('VPC'), ('CloudFormation'), ('Serverless'), ('Networking'), ('Databases'), ('Security'), ('Beginner'), ('Intermediate'), ('Advanced');

-- Insert Courses and associate with Tags
INSERT INTO Courses (CourseName, Description) VALUES
('AWS EC2 Fundamentals', 'Learn the basics of Amazon EC2 and its core features.'),
('AWS Lambda for Serverless Applications', 'Build and deploy serverless applications using AWS Lambda.'),
('Amazon S3 Storage and Data Management', 'Master Amazon S3 for efficient storage and data management.'),
('Amazon RDS for Relational Databases', 'Manage and administer relational databases with Amazon RDS.'),
('Amazon DynamoDB for NoSQL Databases', 'Work with NoSQL databases using Amazon DynamoDB.'),
('Amazon EKS for Kubernetes', 'Deploy and manage Kubernetes clusters on AWS using Amazon EKS.'),
('AWS IAM for Identity and Access Management', 'Secure your AWS environment with Identity and Access Management (IAM).'),
('AWS VPC Networking Fundamentals', 'Design and manage virtual private clouds (VPCs) for secure networking.'),
('AWS CloudFormation Infrastructure as Code', 'Automate infrastructure provisioning with AWS CloudFormation.'),
('Advanced Serverless Architectures on AWS', 'Design and implement complex serverless architectures.'),
('AWS Networking Best Practices', 'Learn advanced networking techniques and best practices on AWS.'),
('Advanced Amazon RDS Management', 'Master advanced techniques for managing Amazon RDS databases.'),
('Securing AWS Workloads', 'Implement robust security measures for your AWS workloads.');


-- Associate Courses with Tags
INSERT INTO CoursesTags (CourseId, TagId) VALUES
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS EC2 Fundamentals'), (SELECT TagId FROM Tags WHERE TagName = 'EC2')),
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS EC2 Fundamentals'), (SELECT TagId FROM Tags WHERE TagName = 'Beginner')),
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS Lambda for Serverless Applications'), (SELECT TagId FROM Tags WHERE TagName = 'Lambda')),
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS Lambda for Serverless Applications'), (SELECT TagId FROM Tags WHERE TagName = 'Serverless')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Amazon S3 Storage and Data Management'), (SELECT TagId FROM Tags WHERE TagName = 'S3')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Amazon S3 Storage and Data Management'), (SELECT TagId FROM Tags WHERE TagName = 'Beginner')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Amazon RDS for Relational Databases'), (SELECT TagId FROM Tags WHERE TagName = 'RDS')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Amazon RDS for Relational Databases'), (SELECT TagId FROM Tags WHERE TagName = 'Databases')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Amazon DynamoDB for NoSQL Databases'), (SELECT TagId FROM Tags WHERE TagName = 'DynamoDB')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Amazon DynamoDB for NoSQL Databases'), (SELECT TagId FROM Tags WHERE TagName = 'Databases')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Amazon EKS for Kubernetes'), (SELECT TagId FROM Tags WHERE TagName = 'EKS')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Amazon EKS for Kubernetes'), (SELECT TagId FROM Tags WHERE TagName = 'Advanced')),
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS IAM for Identity and Access Management'), (SELECT TagId FROM Tags WHERE TagName = 'IAM')),
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS IAM for Identity and Access Management'), (SELECT TagId FROM Tags WHERE TagName = 'Security')),
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS VPC Networking Fundamentals'), (SELECT TagId FROM Tags WHERE TagName = 'VPC')),
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS VPC Networking Fundamentals'), (SELECT TagId FROM Tags WHERE TagName = 'Networking')),
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS CloudFormation Infrastructure as Code'), (SELECT TagId FROM Tags WHERE TagName = 'CloudFormation')),
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS CloudFormation Infrastructure as Code'), (SELECT TagId FROM Tags WHERE TagName = 'Intermediate')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Advanced Serverless Architectures on AWS'), (SELECT TagId FROM Tags WHERE TagName = 'Serverless')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Advanced Serverless Architectures on AWS'), (SELECT TagId FROM Tags WHERE TagName = 'Advanced')),
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS Networking Best Practices'), (SELECT TagId FROM Tags WHERE TagName = 'Networking')),
((SELECT CourseId FROM Courses WHERE CourseName = 'AWS Networking Best Practices'), (SELECT TagId FROM Tags WHERE TagName = 'Advanced')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Advanced Amazon RDS Management'), (SELECT TagId FROM Tags WHERE TagName = 'RDS')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Advanced Amazon RDS Management'), (SELECT TagId FROM Tags WHERE TagName = 'Databases')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Advanced Amazon RDS Management'), (SELECT TagId FROM Tags WHERE TagName = 'Advanced')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Securing AWS Workloads'), (SELECT TagId FROM Tags WHERE TagName = 'Security')),
((SELECT CourseId FROM Courses WHERE CourseName = 'Securing AWS Workloads'), (SELECT TagId FROM Tags WHERE TagName = 'Advanced'));

