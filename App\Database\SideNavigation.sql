CREATE TABLE NavigationItems (
Id INT PRIMARY KEY IDENTITY(1,1), -- Auto-incrementing primary key
ParentId INT NULL, -- Self-referencing foreign key for sub-items (NULL for top-level items)
Title NVARCHAR(255) NOT NULL, -- Display name of the item
Url NVARCHAR(1000) NULL, -- URL associated with the item (optional)
OrderIndex INT NOT NULL, -- Order of the item within its parent
CreatedBy NVARCHAR(255) NOT NULL, -- User who created the item
CreatedAt DATETIME NOT NULL DEFAULT GETUTCDATE(), -- Timestamp of creation
ModifiedBy NVARCHAR(255) NULL, -- User who last modified the item
ModifiedAt DATETIME NULL, -- Timestamp of last modification
CONSTRAINT FK_NavigationItems_Parent FOREIGN KEY (ParentId) REFERENCES NavigationItems(Id)
);



ALTER TABLE NavigationItems
ADD CourseId INT NULL; -- Allow NULL for global navigation items

ALTER TABLE NavigationItems
ADD CONSTRAINT FK_NavigationItems_Courses FOREIGN KEY (CourseId) REFERENCES Courses(CourseId);

-- Example: Assuming CourseId 1 has its own navigation
UPDATE NavigationItems SET CourseId = 1 WHERE Id IN (SELECT Id FROM NavigationItems WHERE ParentId IS NULL); -- Update all top-level items for Course 1



-- Add a new course
INSERT INTO Courses (CourseName, Description) VALUES ('New Course', 'Description of the new course');

-- Get the ID of the new course
DECLARE @NewCourseId INT;
SELECT @NewCourseId = SCOPE_IDENTITY();

-- Add navigation items for the new course
INSERT INTO NavigationItems (ParentId, Title, Url, OrderIndex, CreatedBy, CourseId) VALUES
(@NewCourseId, 'Introduction', '/new-course/introduction', 1, 'Admin', @NewCourseId),
(@NewCourseId, 'Module 1', '/new-course/module1', 2, 'Admin', @NewCourseId),
(@NewCourseId, 'Module 2', '/new-course/module2', 3, 'Admin', @NewCourseId);




-- Insert top-level navigation items
INSERT INTO NavigationItems (ParentId, Title, Url, OrderIndex, CreatedBy)
VALUES 
(NULL, 'Introduction', '/introduction', 1, 'Admin'),
(NULL, '1. Getting Started', '/getting-started', 2, 'Admin'),
(NULL, '2. Core Concepts', '/core-concepts', 3, 'Admin'),
(NULL, '3. Advanced Topics', '/advanced-topics', 4, 'Admin'),
(NULL, '4. Hands-On Labs', '/hands-on-labs', 5, 'Admin'),
(NULL, 'Conclusion', '/conclusion', 6, 'Admin');

-- Insert sub-items for "Introduction"
INSERT INTO NavigationItems (ParentId, Title, Url, OrderIndex, CreatedBy)
VALUES 
((SELECT Id FROM NavigationItems WHERE Title = 'Introduction'), 'Welcome', '/introduction/welcome', 1, 'Admin'),
((SELECT Id FROM NavigationItems WHERE Title = 'Introduction'), 'About this Workshop', '/introduction/about', 2, 'Admin');

-- Insert sub-items for "1. Getting Started"
INSERT INTO NavigationItems (ParentId, Title, Url, OrderIndex, CreatedBy)
VALUES 
((SELECT Id FROM NavigationItems WHERE Title = '1. Getting Started'), 'Overview', '/getting-started/overview', 1, 'Admin'),
((SELECT Id FROM NavigationItems WHERE Title = '1. Getting Started'), 'Prerequisites', '/getting-started/prerequisites', 2, 'Admin'),
((SELECT Id FROM NavigationItems WHERE Title = '1. Getting Started'), 'Setting Up Your Environment', '/getting-started/environment', 3, 'Admin');

-- Insert sub-items for "2. Core Concepts"
INSERT INTO NavigationItems (ParentId, Title, Url, OrderIndex, CreatedBy)
VALUES 
((SELECT Id FROM NavigationItems WHERE Title = '2. Core Concepts'), 'What is SQS?', '/core-concepts/what-is-sqs', 1, 'Admin'),
((SELECT Id FROM NavigationItems WHERE Title = '2. Core Concepts'), 'SQS Queue Types', '/core-concepts/queue-types', 2, 'Admin'),
((SELECT Id FROM NavigationItems WHERE Title = '2. Core Concepts'), 'SQS Key Features', '/core-concepts/key-features', 3, 'Admin');

-- Insert sub-items for "3. Advanced Topics"
INSERT INTO NavigationItems (ParentId, Title, Url, OrderIndex, CreatedBy)
VALUES 
((SELECT Id FROM NavigationItems WHERE Title = '3. Advanced Topics'), 'Dead Letter Queues', '/advanced-topics/dead-letter-queues', 1, 'Admin'),
((SELECT Id FROM NavigationItems WHERE Title = '3. Advanced Topics'), 'FIFO Queues', '/advanced-topics/fifo-queues', 2, 'Admin'),
((SELECT Id FROM NavigationItems WHERE Title = '3. Advanced Topics'), 'CloudWatch Integration', '/advanced-topics/cloudwatch-integration', 3, 'Admin');

-- Insert sub-items for "4. Hands-On Labs"
INSERT INTO NavigationItems (ParentId, Title, Url, OrderIndex, CreatedBy)
VALUES 
((SELECT Id FROM NavigationItems WHERE Title = '4. Hands-On Labs'), 'Lab 1: Creating an SQS Queue', '/hands-on-labs/lab1-create-sqs', 1, 'Admin'),
((SELECT Id FROM NavigationItems WHERE Title = '4. Hands-On Labs'), 'Lab 2: Sending and Receiving Messages', '/hands-on-labs/lab2-send-receive-messages', 2, 'Admin'),
((SELECT Id FROM NavigationItems WHERE Title = '4. Hands-On Labs'), 'Lab 3: Monitoring with CloudWatch', '/hands-on-labs/lab3-monitor-cloudwatch', 3, 'Admin');

-- Insert sub-items for "Conclusion"
INSERT INTO NavigationItems (ParentId, Title, Url, OrderIndex, CreatedBy)
VALUES 
((SELECT Id FROM NavigationItems WHERE Title = 'Conclusion'), 'Recap', '/conclusion/recap', 1, 'Admin'),
((SELECT Id FROM NavigationItems WHERE Title = 'Conclusion'), 'Next Steps', '/conclusion/next-steps', 2, 'Admin'),
((SELECT Id FROM NavigationItems WHERE Title = 'Conclusion'), 'Feedback', '/conclusion/feedback', 3, 'Admin');