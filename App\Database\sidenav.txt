sidenav

import React, { useState, useEffect } from 'react';
import SideNavigation from '@cloudscape-design/components/side-navigation';
import {
  LinkProps,
  ExpandableLinkGroupProps,
} from '@cloudscape-design/components/side-navigation';
import { Item } from '@cloudscape-design/components/side-navigation/interfaces';

/**
 * Interface representing a navigation item from the API.
 */
interface ApiNavItem {
  id: number;
  parentId: number | null;
  text: string;
  href: string;
  orderIndex: number;
  children?: ApiNavItem[]; // Optional children property for hierarchy
}

/**
 * Builds a hierarchical structure from a flat array of ApiNavItem objects.
 *
 * @param {ApiNavItem[]} items - The flat array of ApiNavItem objects.
 * @returns {ApiNavItem[]} - The hierarchical array of ApiNavItem objects.
 */
function buildHierarchy(items: ApiNavItem[]): ApiNavItem[] {
  const itemMap: Record<number, ApiNavItem> = {};
  const rootItems: ApiNavItem[] = [];

  // Create a map of items by their ID for efficient lookup.
  items.forEach((item) => {
    itemMap[item.id] = { ...item, children: [] };
  });

  // Build the hierarchy by assigning children to their parents.
  items.forEach((item) => {
    if (item.parentId === null) {
      // Root-level item
      rootItems.push(itemMap[item.id]);
    } else {
      // Child item
      const parent = itemMap[item.parentId];
      if (parent) {
        parent.children = parent.children || []; // Ensure children array exists
        parent.children.push(itemMap[item.id]);
      }
    }
  });

  /**
   * Recursively sorts items by their orderIndex.
   *
   * @param {ApiNavItem[]} items - The array of ApiNavItem objects to sort.
   */
  const sortItems = (items: ApiNavItem[]) => {
    items.sort((a, b) => a.orderIndex - b.orderIndex);
    items.forEach((item) => {
      if (item.children) {
        sortItems(item.children);
      }
    });
  };

  sortItems(rootItems);

  return rootItems;
}

/**
 * Recursively transforms an array of ApiNavItem objects into CloudscapeNavItem objects.
 *
 * @param {ApiNavItem[]} items - The array of ApiNavItem objects.
 * @returns {Item[]} - The array of CloudscapeNavItem objects.
 */
function mapToCloudscapeFormat(items: ApiNavItem[]): Item[] {
  return items.map((item): Item => {
    if (item.children && item.children.length > 0) {
      return {
        type: 'expandable-link-group' as const,
        text: item.text,
        expanded: true, // Change to false to collapse by default
        items: mapToCloudscapeFormat(item.children),
      } as ExpandableLinkGroupProps;
    }
    return {
      type: 'link' as const,
      text: item.text,
      href: item.href,
    } as LinkProps;
  });
}

/**
 * React component to render a hierarchical navigation menu using Cloudscape SideNavigation.
 */
export default function NavigationTree() {
  const [data, setData] = useState<ApiNavItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Fetch navigation data from the API when the component mounts.
    async function fetchData() {
      try {
        // Fetch data from the API
        const response = await fetch(
          'https://9dn7gxcgyf.execute-api.us-east-1.amazonaws.com/dev/Navigation'
        );
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const json = await response.json();

        // Parse the body field from the API response
        const parsedData: ApiNavItem[] = JSON.parse(json.body);

        // Build the hierarchical structure
        const hierarchicalData = buildHierarchy(parsedData);

        // Set the parsed data to state
        setData(hierarchicalData);
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Unknown error'));
        setLoading(false);
        console.error('Error fetching navigation data:', err);
      }
    }
    fetchData();
  }, []); // Empty dependency array means this effect runs only once on mount.

  if (loading) return <div>Loading navigation...</div>;
  if (error) return <div>Error loading navigation: {error.message}</div>;

  // Transform hierarchical data to Cloudscape format
  const items = mapToCloudscapeFormat(data);

  // Debugging: Log the transformed data to verify its structure
  console.log('Transformed Navigation Items:', items);

  return (
    <SideNavigation
      activeHref={window.location.pathname} // Highlight the active link based on the current URL
      header={{ href: '/home/<USER>', text: 'Welcome to Amazonians workshops!' }} // Header for the side navigation
      items={items} // Pass the transformed navigation items
    />
  );
}
