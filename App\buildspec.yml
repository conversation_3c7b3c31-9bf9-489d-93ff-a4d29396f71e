version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - echo Installing dependencies...
      - cd App
      - echo Logging in to CodeArtifact...
      - aws codeartifact login --tool npm --domain amazonians-domain --domain-owner 202533526407 --repository Amazonians-repo --region us-east-1
      - npm install
  build:
    commands:
      - echo Building the React app...
      - npm run build
artifacts:
  files:
    - '**/*' # Or specify the files/folders to deploy
  base-directory: App/build # Specify the correct base directory for artifacts
  discard-paths: yes
