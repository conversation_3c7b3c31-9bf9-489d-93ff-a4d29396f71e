{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { memo, useRef } from 'react';\nimport clsx from 'clsx';\nimport { getIsRtl } from '@cloudscape-design/component-toolkit/internal';\nimport { useInternalI18n } from '../../../i18n/context';\nimport ResponsiveText from '../responsive-text';\nimport { TICK_LENGTH, TICK_LINE_HEIGHT, TICK_MARGIN } from './constants';\nimport { formatTicks, getSVGTextSize, getVisibleTicks } from './label-utils';\nimport styles from './styles.css.js';\nconst OFFSET_PX = 12;\nexport default memo(InlineStartLabels);\n// Renders the visible tick labels on the value axis, as well as their grid lines.\nfunction InlineStartLabels({\n  axis = 'y',\n  plotWidth,\n  plotHeight,\n  maxLabelsWidth = Number.POSITIVE_INFINITY,\n  scale,\n  ticks,\n  tickFormatter,\n  title,\n  ariaRoleDescription\n}) {\n  const i18n = useInternalI18n('[charts]');\n  const virtualTextRef = useRef(null);\n  const yOffset = axis === 'x' && scale.isCategorical() ? Math.max(0, scale.d3Scale.bandwidth() - 1) / 2 : 0;\n  const labelToBoxCache = useRef({});\n  const getLabelSpace = label => {\n    var _a, _b, _c, _d;\n    if (labelToBoxCache.current[label] !== undefined) {\n      return (_b = (_a = labelToBoxCache.current[label]) === null || _a === void 0 ? void 0 : _a.height) !== null && _b !== void 0 ? _b : 0;\n    }\n    if (virtualTextRef.current) {\n      virtualTextRef.current.textContent = label;\n    }\n    labelToBoxCache.current[label] = getSVGTextSize(virtualTextRef.current);\n    return (_d = (_c = labelToBoxCache.current[label]) === null || _c === void 0 ? void 0 : _c.height) !== null && _d !== void 0 ? _d : 0;\n  };\n  const formattedTicks = formatTicks({\n    ticks,\n    scale,\n    getLabelSpace,\n    tickFormatter\n  });\n  if (virtualTextRef.current) {\n    virtualTextRef.current.textContent = '';\n  }\n  const from = 0 - OFFSET_PX - yOffset;\n  const until = plotHeight + OFFSET_PX - yOffset;\n  const visibleTicks = getVisibleTicks(formattedTicks, from, until);\n  const isRtl = virtualTextRef.current ? getIsRtl(virtualTextRef.current) : false;\n  return React.createElement(\"g\", {\n    className: styles['labels-inline-start'],\n    \"aria-label\": title,\n    role: \"list\",\n    \"aria-roledescription\": i18n('i18nStrings.chartAriaRoleDescription', ariaRoleDescription),\n    \"aria-hidden\": true\n  }, visibleTicks.map(({\n    position,\n    lines\n  }, index) => isFinite(position) && React.createElement(\"g\", {\n    key: index,\n    role: \"listitem\",\n    transform: `translate(0,${position + yOffset})`,\n    className: clsx(styles.ticks, axis === 'x' ? styles['ticks--x'] : styles['ticks--y'])\n  }, axis === 'y' && React.createElement(\"line\", {\n    className: clsx(styles.grid, styles.ticks_line),\n    x1: -TICK_LENGTH,\n    y1: 0,\n    x2: plotWidth,\n    y2: 0,\n    \"aria-hidden\": \"true\"\n  }), lines.map((line, lineIndex) => {\n    var _a, _b;\n    const x = -(TICK_LENGTH + TICK_MARGIN);\n    const lineTextProps = {\n      x: !isRtl ? x : plotWidth - x,\n      y: (lineIndex - (lines.length - 1) * 0.5) * TICK_LINE_HEIGHT,\n      className: styles.ticks__text,\n      children: line\n    };\n    return ((_b = (_a = labelToBoxCache.current[lines[0]]) === null || _a === void 0 ? void 0 : _a.width) !== null && _b !== void 0 ? _b : 0) > maxLabelsWidth ? React.createElement(ResponsiveText, Object.assign({\n      key: lineIndex\n    }, lineTextProps, {\n      maxWidth: maxLabelsWidth\n    })) : React.createElement(\"text\", Object.assign({\n      key: lineIndex\n    }, lineTextProps));\n  }))), React.createElement(\"text\", {\n    ref: virtualTextRef,\n    x: 0,\n    y: 0,\n    style: {\n      visibility: 'hidden'\n    },\n    \"aria-hidden\": \"true\"\n  }));\n}", "map": {"version": 3, "names": ["React", "memo", "useRef", "clsx", "getIsRtl", "useInternalI18n", "ResponsiveText", "TICK_LENGTH", "TICK_LINE_HEIGHT", "TICK_MARGIN", "formatTicks", "getSVGTextSize", "getVisibleTicks", "styles", "OFFSET_PX", "InlineStartLabels", "axis", "plot<PERSON>id<PERSON>", "plotHeight", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "Number", "POSITIVE_INFINITY", "scale", "ticks", "tick<PERSON><PERSON><PERSON><PERSON>", "title", "ariaRoleDescription", "i18n", "virtualTextRef", "yOffset", "isCategorical", "Math", "max", "d3Scale", "bandwidth", "labelToBoxCache", "getLabelSpace", "label", "current", "undefined", "_b", "_a", "height", "textContent", "_d", "_c", "formattedTicks", "from", "until", "visibleTicks", "isRtl", "createElement", "className", "role", "map", "position", "lines", "index", "isFinite", "key", "transform", "grid", "ticks_line", "x1", "y1", "x2", "y2", "line", "lineIndex", "x", "lineTextProps", "y", "length", "ticks__text", "children", "width", "Object", "assign", "max<PERSON><PERSON><PERSON>", "ref", "style", "visibility"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\internal\\components\\cartesian-chart\\inline-start-labels.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { memo, useRef } from 'react';\nimport clsx from 'clsx';\n\nimport { getIsRtl } from '@cloudscape-design/component-toolkit/internal';\n\nimport { useInternalI18n } from '../../../i18n/context';\nimport { ChartDataTypes } from '../../../mixed-line-bar-chart/interfaces';\nimport ResponsiveText from '../responsive-text';\nimport { TICK_LENGTH, TICK_LINE_HEIGHT, TICK_MARGIN } from './constants';\nimport { formatTicks, getSVGTextSize, getVisibleTicks } from './label-utils';\nimport { ChartScale, NumericChartScale } from './scales';\n\nimport styles from './styles.css.js';\n\nconst OFFSET_PX = 12;\n\ninterface InlineStartLabelsProps {\n  axis?: 'x' | 'y';\n  plotWidth: number;\n  plotHeight: number;\n  maxLabelsWidth?: number;\n  scale: ChartScale | NumericChartScale;\n  ticks: readonly ChartDataTypes[];\n  tickFormatter?: (value: number) => string;\n  title?: string;\n  ariaRoleDescription?: string;\n}\n\nexport default memo(InlineStartLabels) as typeof InlineStartLabels;\n\n// Renders the visible tick labels on the value axis, as well as their grid lines.\nfunction InlineStartLabels({\n  axis = 'y',\n  plotWidth,\n  plotHeight,\n  maxLabelsWidth = Number.POSITIVE_INFINITY,\n  scale,\n  ticks,\n  tickFormatter,\n  title,\n  ariaRoleDescription,\n}: InlineStartLabelsProps) {\n  const i18n = useInternalI18n('[charts]');\n  const virtualTextRef = useRef<SVGTextElement>(null);\n\n  const yOffset = axis === 'x' && scale.isCategorical() ? Math.max(0, scale.d3Scale.bandwidth() - 1) / 2 : 0;\n\n  const labelToBoxCache = useRef<{ [label: string]: undefined | { width: number; height: number } }>({});\n  const getLabelSpace = (label: string) => {\n    if (labelToBoxCache.current[label] !== undefined) {\n      return labelToBoxCache.current[label]?.height ?? 0;\n    }\n    if (virtualTextRef.current) {\n      virtualTextRef.current.textContent = label;\n    }\n    labelToBoxCache.current[label] = getSVGTextSize(virtualTextRef.current);\n    return labelToBoxCache.current[label]?.height ?? 0;\n  };\n\n  const formattedTicks = formatTicks({ ticks, scale, getLabelSpace, tickFormatter });\n\n  if (virtualTextRef.current) {\n    virtualTextRef.current.textContent = '';\n  }\n\n  const from = 0 - OFFSET_PX - yOffset;\n  const until = plotHeight + OFFSET_PX - yOffset;\n  const visibleTicks = getVisibleTicks(formattedTicks, from, until);\n\n  const isRtl = virtualTextRef.current ? getIsRtl(virtualTextRef.current) : false;\n\n  return (\n    <g\n      className={styles['labels-inline-start']}\n      aria-label={title}\n      role=\"list\"\n      aria-roledescription={i18n('i18nStrings.chartAriaRoleDescription', ariaRoleDescription)}\n      aria-hidden={true}\n    >\n      {visibleTicks.map(\n        ({ position, lines }, index) =>\n          isFinite(position) && (\n            <g\n              key={index}\n              role=\"listitem\"\n              transform={`translate(0,${position + yOffset})`}\n              className={clsx(styles.ticks, axis === 'x' ? styles['ticks--x'] : styles['ticks--y'])}\n            >\n              {axis === 'y' && (\n                <line\n                  className={clsx(styles.grid, styles.ticks_line)}\n                  x1={-TICK_LENGTH}\n                  y1={0}\n                  x2={plotWidth}\n                  y2={0}\n                  aria-hidden=\"true\"\n                />\n              )}\n\n              {lines.map((line, lineIndex) => {\n                const x = -(TICK_LENGTH + TICK_MARGIN);\n                const lineTextProps = {\n                  x: !isRtl ? x : plotWidth - x,\n                  y: (lineIndex - (lines.length - 1) * 0.5) * TICK_LINE_HEIGHT,\n                  className: styles.ticks__text,\n                  children: line,\n                };\n                return (labelToBoxCache.current[lines[0]]?.width ?? 0) > maxLabelsWidth ? (\n                  <ResponsiveText key={lineIndex} {...lineTextProps} maxWidth={maxLabelsWidth} />\n                ) : (\n                  <text key={lineIndex} {...lineTextProps} />\n                );\n              })}\n            </g>\n          )\n      )}\n\n      <text ref={virtualTextRef} x={0} y={0} style={{ visibility: 'hidden' }} aria-hidden=\"true\"></text>\n    </g>\n  );\n}\n"], "mappings": "AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,IAAI,EAAEC,MAAM,QAAQ,OAAO;AAC3C,OAAOC,IAAI,MAAM,MAAM;AAEvB,SAASC,QAAQ,QAAQ,+CAA+C;AAExE,SAASC,eAAe,QAAQ,uBAAuB;AAEvD,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,SAASC,WAAW,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,aAAa;AACxE,SAASC,WAAW,EAAEC,cAAc,EAAEC,eAAe,QAAQ,eAAe;AAG5E,OAAOC,MAAM,MAAM,iBAAiB;AAEpC,MAAMC,SAAS,GAAG,EAAE;AAcpB,eAAeb,IAAI,CAACc,iBAAiB,CAA6B;AAElE;AACA,SAASA,iBAAiBA,CAAC;EACzBC,IAAI,GAAG,GAAG;EACVC,SAAS;EACTC,UAAU;EACVC,cAAc,GAAGC,MAAM,CAACC,iBAAiB;EACzCC,KAAK;EACLC,KAAK;EACLC,aAAa;EACbC,KAAK;EACLC;AAAmB,CACI;EACvB,MAAMC,IAAI,GAAGtB,eAAe,CAAC,UAAU,CAAC;EACxC,MAAMuB,cAAc,GAAG1B,MAAM,CAAiB,IAAI,CAAC;EAEnD,MAAM2B,OAAO,GAAGb,IAAI,KAAK,GAAG,IAAIM,KAAK,CAACQ,aAAa,EAAE,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEV,KAAK,CAACW,OAAO,CAACC,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAE1G,MAAMC,eAAe,GAAGjC,MAAM,CAAqE,EAAE,CAAC;EACtG,MAAMkC,aAAa,GAAIC,KAAa,IAAI;;IACtC,IAAIF,eAAe,CAACG,OAAO,CAACD,KAAK,CAAC,KAAKE,SAAS,EAAE;MAChD,OAAO,CAAAC,EAAA,IAAAC,EAAA,GAAAN,eAAe,CAACG,OAAO,CAACD,KAAK,CAAC,cAAAI,EAAA,uBAAAA,EAAA,CAAEC,MAAM,cAAAF,EAAA,cAAAA,EAAA,GAAI,CAAC;;IAEpD,IAAIZ,cAAc,CAACU,OAAO,EAAE;MAC1BV,cAAc,CAACU,OAAO,CAACK,WAAW,GAAGN,KAAK;;IAE5CF,eAAe,CAACG,OAAO,CAACD,KAAK,CAAC,GAAG1B,cAAc,CAACiB,cAAc,CAACU,OAAO,CAAC;IACvE,OAAO,CAAAM,EAAA,IAAAC,EAAA,GAAAV,eAAe,CAACG,OAAO,CAACD,KAAK,CAAC,cAAAQ,EAAA,uBAAAA,EAAA,CAAEH,MAAM,cAAAE,EAAA,cAAAA,EAAA,GAAI,CAAC;EACpD,CAAC;EAED,MAAME,cAAc,GAAGpC,WAAW,CAAC;IAAEa,KAAK;IAAED,KAAK;IAAEc,aAAa;IAAEZ;EAAa,CAAE,CAAC;EAElF,IAAII,cAAc,CAACU,OAAO,EAAE;IAC1BV,cAAc,CAACU,OAAO,CAACK,WAAW,GAAG,EAAE;;EAGzC,MAAMI,IAAI,GAAG,CAAC,GAAGjC,SAAS,GAAGe,OAAO;EACpC,MAAMmB,KAAK,GAAG9B,UAAU,GAAGJ,SAAS,GAAGe,OAAO;EAC9C,MAAMoB,YAAY,GAAGrC,eAAe,CAACkC,cAAc,EAAEC,IAAI,EAAEC,KAAK,CAAC;EAEjE,MAAME,KAAK,GAAGtB,cAAc,CAACU,OAAO,GAAGlC,QAAQ,CAACwB,cAAc,CAACU,OAAO,CAAC,GAAG,KAAK;EAE/E,OACEtC,KAAA,CAAAmD,aAAA;IACEC,SAAS,EAAEvC,MAAM,CAAC,qBAAqB,CAAC;IAAA,cAC5BY,KAAK;IACjB4B,IAAI,EAAC,MAAM;IAAA,wBACW1B,IAAI,CAAC,sCAAsC,EAAED,mBAAmB,CAAC;IAAA,eAC1E;EAAI,GAEhBuB,YAAY,CAACK,GAAG,CACf,CAAC;IAAEC,QAAQ;IAAEC;EAAK,CAAE,EAAEC,KAAK,KACzBC,QAAQ,CAACH,QAAQ,CAAC,IAChBvD,KAAA,CAAAmD,aAAA;IACEQ,GAAG,EAAEF,KAAK;IACVJ,IAAI,EAAC,UAAU;IACfO,SAAS,EAAE,eAAeL,QAAQ,GAAG1B,OAAO,GAAG;IAC/CuB,SAAS,EAAEjD,IAAI,CAACU,MAAM,CAACU,KAAK,EAAEP,IAAI,KAAK,GAAG,GAAGH,MAAM,CAAC,UAAU,CAAC,GAAGA,MAAM,CAAC,UAAU,CAAC;EAAC,GAEpFG,IAAI,KAAK,GAAG,IACXhB,KAAA,CAAAmD,aAAA;IACEC,SAAS,EAAEjD,IAAI,CAACU,MAAM,CAACgD,IAAI,EAAEhD,MAAM,CAACiD,UAAU,CAAC;IAC/CC,EAAE,EAAE,CAACxD,WAAW;IAChByD,EAAE,EAAE,CAAC;IACLC,EAAE,EAAEhD,SAAS;IACbiD,EAAE,EAAE,CAAC;IAAA,eACO;EAAM,EAErB,EAEAV,KAAK,CAACF,GAAG,CAAC,CAACa,IAAI,EAAEC,SAAS,KAAI;;IAC7B,MAAMC,CAAC,GAAG,EAAE9D,WAAW,GAAGE,WAAW,CAAC;IACtC,MAAM6D,aAAa,GAAG;MACpBD,CAAC,EAAE,CAACnB,KAAK,GAAGmB,CAAC,GAAGpD,SAAS,GAAGoD,CAAC;MAC7BE,CAAC,EAAE,CAACH,SAAS,GAAG,CAACZ,KAAK,CAACgB,MAAM,GAAG,CAAC,IAAI,GAAG,IAAIhE,gBAAgB;MAC5D4C,SAAS,EAAEvC,MAAM,CAAC4D,WAAW;MAC7BC,QAAQ,EAAEP;KACX;IACD,OAAO,CAAC,CAAA3B,EAAA,IAAAC,EAAA,GAAAN,eAAe,CAACG,OAAO,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC,cAAAf,EAAA,uBAAAA,EAAA,CAAEkC,KAAK,cAAAnC,EAAA,cAAAA,EAAA,GAAI,CAAC,IAAIrB,cAAc,GACrEnB,KAAA,CAAAmD,aAAA,CAAC7C,cAAc,EAAAsE,MAAA,CAAAC,MAAA;MAAClB,GAAG,EAAES;IAAS,GAAME,aAAa;MAAEQ,QAAQ,EAAE3D;IAAc,GAAI,GAE/EnB,KAAA,CAAAmD,aAAA,SAAAyB,MAAA,CAAAC,MAAA;MAAMlB,GAAG,EAAES;IAAS,GAAME,aAAa,EACxC;EACH,CAAC,CAAC,CAEL,CACJ,EAEDtE,KAAA,CAAAmD,aAAA;IAAM4B,GAAG,EAAEnD,cAAc;IAAEyC,CAAC,EAAE,CAAC;IAAEE,CAAC,EAAE,CAAC;IAAES,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAQ,CAAE;IAAA,eAAc;EAAM,EAAQ,CAChG;AAER", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}