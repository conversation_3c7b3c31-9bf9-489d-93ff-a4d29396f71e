{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport { __rest } from \"tslib\";\nimport { useMemo, useState } from 'react';\nimport { warnOnce } from '@cloudscape-design/component-toolkit/internal';\nimport { useInternalI18n } from '../i18n/context';\nimport { generateTestIndexes } from '../internal/components/options-list/utils/test-indexes';\nimport { useHighlightedOption } from '../internal/components/options-list/utils/use-highlight-option';\nimport { filterOptions } from './utils/utils';\nconst isHighlightable = option => {\n  return !!option && option.type !== 'parent';\n};\nconst isInteractive = option => !!option && !option.disabled && option.type !== 'parent';\nexport const useAutosuggestItems = ({\n  options,\n  filterValue,\n  filterText,\n  filteringType,\n  enteredTextLabel,\n  hideEnteredTextLabel,\n  onSelectItem\n}) => {\n  const i18n = useInternalI18n('autosuggest');\n  const [showAll, setShowAll] = useState(false);\n  const {\n    items,\n    getItemGroup,\n    getItemParent\n  } = useMemo(() => createItems(options), [options]);\n  const enteredItemLabel = i18n('enteredTextLabel', enteredTextLabel === null || enteredTextLabel === void 0 ? void 0 : enteredTextLabel(filterValue), format => format({\n    value: filterValue\n  }));\n  if (!enteredItemLabel) {\n    warnOnce('Autosuggest', 'A value for enteredTextLabel must be provided.');\n  }\n  const filteredItems = useMemo(() => {\n    const filteredItems = filteringType === 'auto' && !showAll ? filterOptions(items, filterText) : [...items];\n    if (filterValue && !hideEnteredTextLabel) {\n      filteredItems.unshift({\n        value: filterValue,\n        type: 'use-entered',\n        label: enteredItemLabel,\n        option: {\n          value: filterValue\n        }\n      });\n    }\n    generateTestIndexes(filteredItems, getItemParent);\n    return filteredItems;\n  }, [filteringType, showAll, items, filterText, filterValue, hideEnteredTextLabel, getItemParent, enteredItemLabel]);\n  const [highlightedOptionState, highlightedOptionHandlers] = useHighlightedOption({\n    options: filteredItems,\n    isHighlightable\n  });\n  const selectHighlightedOptionWithKeyboard = () => {\n    var _a;\n    if (highlightedOptionState.highlightedOption && !isInteractive(highlightedOptionState.highlightedOption)) {\n      // skip selection when a non-interactive item is active\n      return false;\n    }\n    onSelectItem((_a = highlightedOptionState.highlightedOption) !== null && _a !== void 0 ? _a : {\n      // put use-entered item as a fallback\n      value: filterValue,\n      type: 'use-entered',\n      option: {\n        value: filterValue\n      }\n    });\n    return true;\n  };\n  const highlightVisibleOptionWithMouse = index => {\n    if (filteredItems[index] && isHighlightable(filteredItems[index])) {\n      highlightedOptionHandlers.setHighlightedIndexWithMouse(index);\n    }\n  };\n  const selectVisibleOptionWithMouse = index => {\n    if (filteredItems[index] && isInteractive(filteredItems[index])) {\n      onSelectItem(filteredItems[index]);\n    }\n  };\n  return [Object.assign(Object.assign({}, highlightedOptionState), {\n    items: filteredItems,\n    showAll,\n    getItemGroup\n  }), Object.assign(Object.assign({}, highlightedOptionHandlers), {\n    setShowAll,\n    selectHighlightedOptionWithKeyboard,\n    highlightVisibleOptionWithMouse,\n    selectVisibleOptionWithMouse\n  })];\n};\nfunction createItems(options) {\n  const items = [];\n  const itemToGroup = new WeakMap();\n  const getItemParent = item => itemToGroup.get(item);\n  const getItemGroup = item => {\n    var _a;\n    return (_a = getItemParent(item)) === null || _a === void 0 ? void 0 : _a.option;\n  };\n  for (const option of options) {\n    if (isGroup(option)) {\n      for (const item of flattenGroup(option)) {\n        items.push(item);\n      }\n    } else {\n      items.push(Object.assign(Object.assign({}, option), {\n        option\n      }));\n    }\n  }\n  function flattenGroup(group) {\n    const {\n        options\n      } = group,\n      rest = __rest(group, [\"options\"]);\n    let hasOnlyDisabledChildren = true;\n    const groupItem = Object.assign(Object.assign({}, rest), {\n      type: 'parent',\n      option: group\n    });\n    const items = [groupItem];\n    for (const option of options) {\n      if (!option.disabled) {\n        hasOnlyDisabledChildren = false;\n      }\n      const childOption = Object.assign(Object.assign({}, option), {\n        type: 'child',\n        disabled: option.disabled || rest.disabled,\n        option\n      });\n      items.push(childOption);\n      itemToGroup.set(childOption, groupItem);\n    }\n    items[0].disabled = items[0].disabled || hasOnlyDisabledChildren;\n    return items;\n  }\n  return {\n    items,\n    getItemGroup,\n    getItemParent\n  };\n}\nfunction isGroup(optionOrGroup) {\n  return 'options' in optionOrGroup;\n}", "map": {"version": 3, "names": ["useMemo", "useState", "warnOnce", "useInternalI18n", "generateTestIndexes", "useHighlightedOption", "filterOptions", "isHighlightable", "option", "type", "isInteractive", "disabled", "useAutosuggestItems", "options", "filterValue", "filterText", "filteringType", "enteredTextLabel", "hideEnteredTextLabel", "onSelectItem", "i18n", "showAll", "setShowAll", "items", "getItemGroup", "getItemParent", "createItems", "enteredItemLabel", "format", "value", "filteredItems", "unshift", "label", "highlightedOptionState", "highlightedOptionHandlers", "selectHighlightedOptionWithKeyboard", "highlightedOption", "_a", "highlightVisibleOptionWithMouse", "index", "setHighlightedIndexWithMouse", "selectVisibleOptionWithMouse", "itemToGroup", "WeakMap", "item", "get", "isGroup", "flattenGroup", "push", "Object", "assign", "group", "rest", "__rest", "hasOnlyDisabledChildren", "groupItem", "childOption", "set", "optionOrGroup"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\autosuggest\\options-controller.ts"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { useMemo, useState } from 'react';\n\nimport { warnOnce } from '@cloudscape-design/component-toolkit/internal';\n\nimport { useInternalI18n } from '../i18n/context';\nimport { generateTestIndexes } from '../internal/components/options-list/utils/test-indexes';\nimport {\n  HighlightedOptionHandlers,\n  HighlightedOptionState,\n  useHighlightedOption,\n} from '../internal/components/options-list/utils/use-highlight-option';\nimport { AutosuggestItem, AutosuggestProps } from './interfaces';\nimport { filterOptions } from './utils/utils';\n\ntype Options = AutosuggestProps.Options;\n\nexport interface UseAutosuggestItemsProps {\n  options: Options;\n  filterValue: string;\n  filterText: string;\n  filteringType: AutosuggestProps.FilteringType;\n  enteredTextLabel?: AutosuggestProps.EnteredTextLabel;\n  hideEnteredTextLabel?: boolean;\n  onSelectItem: (option: AutosuggestItem) => void;\n}\n\nexport interface AutosuggestItemsState extends HighlightedOptionState<AutosuggestItem> {\n  items: readonly AutosuggestItem[];\n  showAll: boolean;\n  getItemGroup: (item: AutosuggestItem) => undefined | AutosuggestProps.OptionGroup;\n}\n\nexport interface AutosuggestItemsHandlers extends HighlightedOptionHandlers<AutosuggestItem> {\n  setShowAll(value: boolean): void;\n  selectHighlightedOptionWithKeyboard(): boolean;\n  highlightVisibleOptionWithMouse(index: number): void;\n  selectVisibleOptionWithMouse(index: number): void;\n}\n\nconst isHighlightable = (option?: AutosuggestItem) => {\n  return !!option && option.type !== 'parent';\n};\n\nconst isInteractive = (option?: AutosuggestItem) => !!option && !option.disabled && option.type !== 'parent';\n\nexport const useAutosuggestItems = ({\n  options,\n  filterValue,\n  filterText,\n  filteringType,\n  enteredTextLabel,\n  hideEnteredTextLabel,\n  onSelectItem,\n}: UseAutosuggestItemsProps): [AutosuggestItemsState, AutosuggestItemsHandlers] => {\n  const i18n = useInternalI18n('autosuggest');\n  const [showAll, setShowAll] = useState(false);\n\n  const { items, getItemGroup, getItemParent } = useMemo(() => createItems(options), [options]);\n\n  const enteredItemLabel = i18n('enteredTextLabel', enteredTextLabel?.(filterValue), format =>\n    format({ value: filterValue })\n  );\n\n  if (!enteredItemLabel) {\n    warnOnce('Autosuggest', 'A value for enteredTextLabel must be provided.');\n  }\n\n  const filteredItems = useMemo(() => {\n    const filteredItems = filteringType === 'auto' && !showAll ? filterOptions(items, filterText) : [...items];\n    if (filterValue && !hideEnteredTextLabel) {\n      filteredItems.unshift({\n        value: filterValue,\n        type: 'use-entered',\n        label: enteredItemLabel,\n        option: { value: filterValue },\n      });\n    }\n    generateTestIndexes(filteredItems, getItemParent);\n    return filteredItems;\n  }, [filteringType, showAll, items, filterText, filterValue, hideEnteredTextLabel, getItemParent, enteredItemLabel]);\n\n  const [highlightedOptionState, highlightedOptionHandlers] = useHighlightedOption({\n    options: filteredItems,\n    isHighlightable,\n  });\n\n  const selectHighlightedOptionWithKeyboard = () => {\n    if (highlightedOptionState.highlightedOption && !isInteractive(highlightedOptionState.highlightedOption)) {\n      // skip selection when a non-interactive item is active\n      return false;\n    }\n    onSelectItem(\n      highlightedOptionState.highlightedOption ?? {\n        // put use-entered item as a fallback\n        value: filterValue,\n        type: 'use-entered',\n        option: { value: filterValue },\n      }\n    );\n    return true;\n  };\n\n  const highlightVisibleOptionWithMouse = (index: number) => {\n    if (filteredItems[index] && isHighlightable(filteredItems[index])) {\n      highlightedOptionHandlers.setHighlightedIndexWithMouse(index);\n    }\n  };\n\n  const selectVisibleOptionWithMouse = (index: number) => {\n    if (filteredItems[index] && isInteractive(filteredItems[index])) {\n      onSelectItem(filteredItems[index]);\n    }\n  };\n\n  return [\n    { ...highlightedOptionState, items: filteredItems, showAll, getItemGroup },\n    {\n      ...highlightedOptionHandlers,\n      setShowAll,\n      selectHighlightedOptionWithKeyboard,\n      highlightVisibleOptionWithMouse,\n      selectVisibleOptionWithMouse,\n    },\n  ];\n};\n\nfunction createItems(options: Options) {\n  const items: AutosuggestItem[] = [];\n  const itemToGroup = new WeakMap<AutosuggestItem, AutosuggestItem>();\n  const getItemParent = (item: AutosuggestItem) => itemToGroup.get(item);\n  const getItemGroup = (item: AutosuggestItem) => getItemParent(item)?.option as AutosuggestProps.OptionGroup;\n\n  for (const option of options) {\n    if (isGroup(option)) {\n      for (const item of flattenGroup(option)) {\n        items.push(item);\n      }\n    } else {\n      items.push({ ...option, option });\n    }\n  }\n\n  function flattenGroup(group: AutosuggestProps.OptionGroup) {\n    const { options, ...rest } = group;\n\n    let hasOnlyDisabledChildren = true;\n\n    const groupItem: AutosuggestItem = { ...rest, type: 'parent', option: group };\n\n    const items: AutosuggestItem[] = [groupItem];\n\n    for (const option of options) {\n      if (!option.disabled) {\n        hasOnlyDisabledChildren = false;\n      }\n\n      const childOption: AutosuggestItem = {\n        ...option,\n        type: 'child',\n        disabled: option.disabled || rest.disabled,\n        option,\n      };\n\n      items.push(childOption);\n\n      itemToGroup.set(childOption, groupItem);\n    }\n\n    items[0].disabled = items[0].disabled || hasOnlyDisabledChildren;\n\n    return items;\n  }\n\n  return { items, getItemGroup, getItemParent };\n}\n\nfunction isGroup(optionOrGroup: AutosuggestProps.Option): optionOrGroup is AutosuggestProps.OptionGroup {\n  return 'options' in optionOrGroup;\n}\n"], "mappings": "AAAA;AACA;;AAEA,SAASA,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AAEzC,SAASC,QAAQ,QAAQ,+CAA+C;AAExE,SAASC,eAAe,QAAQ,iBAAiB;AACjD,SAASC,mBAAmB,QAAQ,wDAAwD;AAC5F,SAGEC,oBAAoB,QACf,gEAAgE;AAEvE,SAASC,aAAa,QAAQ,eAAe;AA2B7C,MAAMC,eAAe,GAAIC,MAAwB,IAAI;EACnD,OAAO,CAAC,CAACA,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,QAAQ;AAC7C,CAAC;AAED,MAAMC,aAAa,GAAIF,MAAwB,IAAK,CAAC,CAACA,MAAM,IAAI,CAACA,MAAM,CAACG,QAAQ,IAAIH,MAAM,CAACC,IAAI,KAAK,QAAQ;AAE5G,OAAO,MAAMG,mBAAmB,GAAGA,CAAC;EAClCC,OAAO;EACPC,WAAW;EACXC,UAAU;EACVC,aAAa;EACbC,gBAAgB;EAChBC,oBAAoB;EACpBC;AAAY,CACa,KAAuD;EAChF,MAAMC,IAAI,GAAGjB,eAAe,CAAC,aAAa,CAAC;EAC3C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM;IAAEsB,KAAK;IAAEC,YAAY;IAAEC;EAAa,CAAE,GAAGzB,OAAO,CAAC,MAAM0B,WAAW,CAACb,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAE7F,MAAMc,gBAAgB,GAAGP,IAAI,CAAC,kBAAkB,EAAEH,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAGH,WAAW,CAAC,EAAEc,MAAM,IACvFA,MAAM,CAAC;IAAEC,KAAK,EAAEf;EAAW,CAAE,CAAC,CAC/B;EAED,IAAI,CAACa,gBAAgB,EAAE;IACrBzB,QAAQ,CAAC,aAAa,EAAE,gDAAgD,CAAC;;EAG3E,MAAM4B,aAAa,GAAG9B,OAAO,CAAC,MAAK;IACjC,MAAM8B,aAAa,GAAGd,aAAa,KAAK,MAAM,IAAI,CAACK,OAAO,GAAGf,aAAa,CAACiB,KAAK,EAAER,UAAU,CAAC,GAAG,CAAC,GAAGQ,KAAK,CAAC;IAC1G,IAAIT,WAAW,IAAI,CAACI,oBAAoB,EAAE;MACxCY,aAAa,CAACC,OAAO,CAAC;QACpBF,KAAK,EAAEf,WAAW;QAClBL,IAAI,EAAE,aAAa;QACnBuB,KAAK,EAAEL,gBAAgB;QACvBnB,MAAM,EAAE;UAAEqB,KAAK,EAAEf;QAAW;OAC7B,CAAC;;IAEJV,mBAAmB,CAAC0B,aAAa,EAAEL,aAAa,CAAC;IACjD,OAAOK,aAAa;EACtB,CAAC,EAAE,CAACd,aAAa,EAAEK,OAAO,EAAEE,KAAK,EAAER,UAAU,EAAED,WAAW,EAAEI,oBAAoB,EAAEO,aAAa,EAAEE,gBAAgB,CAAC,CAAC;EAEnH,MAAM,CAACM,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7B,oBAAoB,CAAC;IAC/EQ,OAAO,EAAEiB,aAAa;IACtBvB;GACD,CAAC;EAEF,MAAM4B,mCAAmC,GAAGA,CAAA,KAAK;;IAC/C,IAAIF,sBAAsB,CAACG,iBAAiB,IAAI,CAAC1B,aAAa,CAACuB,sBAAsB,CAACG,iBAAiB,CAAC,EAAE;MACxG;MACA,OAAO,KAAK;;IAEdjB,YAAY,CACV,CAAAkB,EAAA,GAAAJ,sBAAsB,CAACG,iBAAiB,cAAAC,EAAA,cAAAA,EAAA,GAAI;MAC1C;MACAR,KAAK,EAAEf,WAAW;MAClBL,IAAI,EAAE,aAAa;MACnBD,MAAM,EAAE;QAAEqB,KAAK,EAAEf;MAAW;KAC7B,CACF;IACD,OAAO,IAAI;EACb,CAAC;EAED,MAAMwB,+BAA+B,GAAIC,KAAa,IAAI;IACxD,IAAIT,aAAa,CAACS,KAAK,CAAC,IAAIhC,eAAe,CAACuB,aAAa,CAACS,KAAK,CAAC,CAAC,EAAE;MACjEL,yBAAyB,CAACM,4BAA4B,CAACD,KAAK,CAAC;;EAEjE,CAAC;EAED,MAAME,4BAA4B,GAAIF,KAAa,IAAI;IACrD,IAAIT,aAAa,CAACS,KAAK,CAAC,IAAI7B,aAAa,CAACoB,aAAa,CAACS,KAAK,CAAC,CAAC,EAAE;MAC/DpB,YAAY,CAACW,aAAa,CAACS,KAAK,CAAC,CAAC;;EAEtC,CAAC;EAED,OAAO,C,gCACAN,sBAAsB;IAAEV,KAAK,EAAEO,aAAa;IAAET,OAAO;IAAEG;EAAY,I,gCAEnEU,yBAAyB;IAC5BZ,UAAU;IACVa,mCAAmC;IACnCG,+BAA+B;IAC/BG;EAA4B,GAE/B;AACH,CAAC;AAED,SAASf,WAAWA,CAACb,OAAgB;EACnC,MAAMU,KAAK,GAAsB,EAAE;EACnC,MAAMmB,WAAW,GAAG,IAAIC,OAAO,EAAoC;EACnE,MAAMlB,aAAa,GAAImB,IAAqB,IAAKF,WAAW,CAACG,GAAG,CAACD,IAAI,CAAC;EACtE,MAAMpB,YAAY,GAAIoB,IAAqB,IAAI;IAAA,IAAAP,EAAA;IAAC,QAAAA,EAAA,GAAAZ,aAAa,CAACmB,IAAI,CAAC,cAAAP,EAAA,uBAAAA,EAAA,CAAE7B,MAAsC;EAAA;EAE3G,KAAK,MAAMA,MAAM,IAAIK,OAAO,EAAE;IAC5B,IAAIiC,OAAO,CAACtC,MAAM,CAAC,EAAE;MACnB,KAAK,MAAMoC,IAAI,IAAIG,YAAY,CAACvC,MAAM,CAAC,EAAE;QACvCe,KAAK,CAACyB,IAAI,CAACJ,IAAI,CAAC;;KAEnB,MAAM;MACLrB,KAAK,CAACyB,IAAI,CAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAM1C,MAAM;QAAEA;MAAM,GAAG;;;EAIrC,SAASuC,YAAYA,CAACI,KAAmC;IACvD,MAAM;QAAEtC;MAAO,IAAcsC,KAAK;MAAdC,IAAI,GAAAC,MAAA,CAAKF,KAAK,EAA5B,WAAoB,CAAQ;IAElC,IAAIG,uBAAuB,GAAG,IAAI;IAElC,MAAMC,SAAS,GAAAN,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAyBE,IAAI;MAAE3C,IAAI,EAAE,QAAQ;MAAED,MAAM,EAAE2C;IAAK,EAAE;IAE7E,MAAM5B,KAAK,GAAsB,CAACgC,SAAS,CAAC;IAE5C,KAAK,MAAM/C,MAAM,IAAIK,OAAO,EAAE;MAC5B,IAAI,CAACL,MAAM,CAACG,QAAQ,EAAE;QACpB2C,uBAAuB,GAAG,KAAK;;MAGjC,MAAME,WAAW,GAAAP,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACZ1C,MAAM;QACTC,IAAI,EAAE,OAAO;QACbE,QAAQ,EAAEH,MAAM,CAACG,QAAQ,IAAIyC,IAAI,CAACzC,QAAQ;QAC1CH;MAAM,EACP;MAEDe,KAAK,CAACyB,IAAI,CAACQ,WAAW,CAAC;MAEvBd,WAAW,CAACe,GAAG,CAACD,WAAW,EAAED,SAAS,CAAC;;IAGzChC,KAAK,CAAC,CAAC,CAAC,CAACZ,QAAQ,GAAGY,KAAK,CAAC,CAAC,CAAC,CAACZ,QAAQ,IAAI2C,uBAAuB;IAEhE,OAAO/B,KAAK;EACd;EAEA,OAAO;IAAEA,KAAK;IAAEC,YAAY;IAAEC;EAAa,CAAE;AAC/C;AAEA,SAASqB,OAAOA,CAACY,aAAsC;EACrD,OAAO,SAAS,IAAIA,aAAa;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}