{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useRef, useState } from 'react';\nimport InternalAutosuggest from '../autosuggest/internal';\nimport { KeyCode } from '../internal/keycode';\nimport { makeCancellable, PromiseCancelledSignal } from '../internal/utils/promises';\nimport styles from './styles.css.js';\nexport const TagControl = React.forwardRef(({\n  row,\n  value,\n  readOnly,\n  defaultOptions,\n  placeholder,\n  errorText,\n  loadingText,\n  suggestionText,\n  tooManySuggestionText,\n  limit,\n  filteringKey,\n  enteredTextLabel,\n  clearAriaLabel,\n  onChange,\n  onBlur,\n  onRequest,\n  initialOptionsRef\n}, ref) => {\n  const [options, setOptions] = useState(defaultOptions);\n  const [statusType, setStatusType] = useState();\n  const requestCancelFnRef = useRef({\n    cancel: () => {},\n    isCancelled: () => false\n  });\n  const latestFilteringQuery = useRef({\n    key: undefined,\n    value: undefined\n  });\n  const isSameQuery = (key, value) => latestFilteringQuery.current.key === key && latestFilteringQuery.current.value === value;\n  const onLoadItems = filteringText => {\n    if (!onRequest || isSameQuery(filteringKey, filteringText) || requestCancelFnRef.current.isCancelled()) {\n      return;\n    }\n    requestCancelFnRef.current.cancel();\n    if (latestFilteringQuery.current.key !== filteringKey) {\n      // Reset suggestions for values if the key is different.\n      setOptions([]);\n    } else if (filteringText === '' && (initialOptionsRef === null || initialOptionsRef === void 0 ? void 0 : initialOptionsRef.current) && initialOptionsRef.current.length > 0) {\n      // Load in the background, if the value is empty and we already have suggestions.\n      setOptions(initialOptionsRef.current);\n    }\n    setStatusType('loading');\n    latestFilteringQuery.current = {\n      key: filteringKey,\n      value: filteringText\n    };\n    const {\n      promise,\n      cancel,\n      isCancelled\n    } = makeCancellable(onRequest(filteringText));\n    promise.then(newValues => {\n      const newOptions = newValues.map(value => ({\n        value\n      }));\n      setStatusType(undefined);\n      setOptions(newOptions);\n      if (initialOptionsRef) {\n        initialOptionsRef.current = newOptions;\n      }\n    }).catch(err => {\n      if (!(err instanceof PromiseCancelledSignal)) {\n        setStatusType('error');\n      }\n    });\n    requestCancelFnRef.current = {\n      cancel,\n      isCancelled\n    };\n  };\n  return React.createElement(InternalAutosuggest, {\n    ref: ref,\n    value: value,\n    readOnly: readOnly,\n    statusType: statusType,\n    options: options.length < limit ? options : [],\n    empty: options.length < limit ? suggestionText : tooManySuggestionText,\n    placeholder: placeholder,\n    errorText: errorText,\n    loadingText: loadingText,\n    enteredTextLabel: enteredTextLabel,\n    clearAriaLabel: clearAriaLabel,\n    onChange: ({\n      detail\n    }) => onChange(detail.value, row),\n    onBlur: () => onBlur === null || onBlur === void 0 ? void 0 : onBlur(row),\n    onFocus: () => {\n      onLoadItems('');\n    },\n    onLoadItems: ({\n      detail\n    }) => {\n      onLoadItems(detail.filteringText);\n    }\n  });\n});\nexport const UndoButton = React.forwardRef(({\n  children,\n  onClick\n}, ref) => {\n  return React.createElement(\"a\", {\n    ref: ref,\n    role: \"button\",\n    tabIndex: 0,\n    className: styles['undo-button'],\n    onClick: onClick,\n    onKeyDown: event => {\n      if (event.keyCode === KeyCode.space || event.keyCode === KeyCode.enter) {\n        event.preventDefault();\n      }\n      // Enter activates the button on key down instead of key up.\n      if (event.keyCode === KeyCode.enter) {\n        onClick();\n      }\n    },\n    onKeyUp: event => {\n      // Emulate button behavior, which also fires on space.\n      if (event.keyCode === KeyCode.space) {\n        onClick();\n      }\n    }\n  }, children);\n});", "map": {"version": 3, "names": ["React", "useRef", "useState", "InternalAutosuggest", "KeyCode", "makeCancellable", "PromiseCancelledSignal", "styles", "TagControl", "forwardRef", "row", "value", "readOnly", "defaultOptions", "placeholder", "errorText", "loadingText", "suggestionText", "tooManySuggestionText", "limit", "<PERSON><PERSON><PERSON>", "enteredTextLabel", "clearAriaLabel", "onChange", "onBlur", "onRequest", "initialOptionsRef", "ref", "options", "setOptions", "statusType", "setStatusType", "requestCancelFnRef", "cancel", "isCancelled", "latestFilteringQuery", "key", "undefined", "is<PERSON>ame<PERSON><PERSON>y", "current", "onLoadItems", "filteringText", "length", "promise", "then", "newValues", "newOptions", "map", "catch", "err", "createElement", "empty", "detail", "onFocus", "Undo<PERSON><PERSON>on", "children", "onClick", "role", "tabIndex", "className", "onKeyDown", "event", "keyCode", "space", "enter", "preventDefault", "onKeyUp"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\tag-editor\\internal.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useRef, useState } from 'react';\n\nimport { AutosuggestProps } from '../autosuggest/interfaces';\nimport InternalAutosuggest from '../autosuggest/internal';\nimport { InputProps } from '../input/interfaces';\nimport { DropdownStatusProps } from '../internal/components/dropdown-status';\nimport { KeyCode } from '../internal/keycode';\nimport { makeCancellable, PromiseCancelledSignal } from '../internal/utils/promises';\n\nimport styles from './styles.css.js';\n\ninterface FilteringParams {\n  key?: string;\n  value?: string;\n}\nexport interface TagControlProps {\n  row: number;\n  value: string;\n  readOnly: boolean;\n  defaultOptions: AutosuggestProps.Options;\n  placeholder?: string;\n  errorText?: string;\n  loadingText?: string;\n  suggestionText?: string;\n  tooManySuggestionText?: string;\n  limit: number;\n  filteringKey?: string;\n  clearAriaLabel?: string;\n  enteredTextLabel?: (value: string) => string;\n  onChange: (value: string, row: number) => void;\n  onBlur?: (row: number) => void;\n  onRequest?: (value: string) => Promise<readonly string[]>;\n\n  initialOptionsRef?: React.MutableRefObject<AutosuggestProps.Options>;\n}\n\nexport const TagControl = React.forwardRef(\n  (\n    {\n      row,\n      value,\n      readOnly,\n      defaultOptions,\n      placeholder,\n      errorText,\n      loadingText,\n      suggestionText,\n      tooManySuggestionText,\n      limit,\n      filteringKey,\n      enteredTextLabel,\n      clearAriaLabel,\n      onChange,\n      onBlur,\n      onRequest,\n      initialOptionsRef,\n    }: TagControlProps,\n    ref: React.Ref<InputProps.Ref>\n  ) => {\n    const [options, setOptions] = useState<AutosuggestProps.Options>(defaultOptions);\n    const [statusType, setStatusType] = useState<DropdownStatusProps.StatusType>();\n    const requestCancelFnRef = useRef<{ cancel: () => void; isCancelled: () => boolean }>({\n      cancel: () => {},\n      isCancelled: () => false,\n    });\n\n    const latestFilteringQuery = useRef<FilteringParams>({ key: undefined, value: undefined });\n    const isSameQuery = (key: string | undefined, value: string) =>\n      latestFilteringQuery.current.key === key && latestFilteringQuery.current.value === value;\n\n    const onLoadItems = (filteringText: string) => {\n      if (!onRequest || isSameQuery(filteringKey, filteringText) || requestCancelFnRef.current.isCancelled()) {\n        return;\n      }\n      requestCancelFnRef.current.cancel();\n\n      if (latestFilteringQuery.current.key !== filteringKey) {\n        // Reset suggestions for values if the key is different.\n        setOptions([]);\n      } else if (filteringText === '' && initialOptionsRef?.current && initialOptionsRef.current.length > 0) {\n        // Load in the background, if the value is empty and we already have suggestions.\n        setOptions(initialOptionsRef.current);\n      }\n\n      setStatusType('loading');\n      latestFilteringQuery.current = { key: filteringKey, value: filteringText };\n\n      const { promise, cancel, isCancelled } = makeCancellable(onRequest(filteringText));\n      promise\n        .then(newValues => {\n          const newOptions = newValues.map(value => ({ value }));\n          setStatusType(undefined);\n          setOptions(newOptions);\n          if (initialOptionsRef) {\n            initialOptionsRef.current = newOptions;\n          }\n        })\n        .catch(err => {\n          if (!(err instanceof PromiseCancelledSignal)) {\n            setStatusType('error');\n          }\n        });\n      requestCancelFnRef.current = { cancel, isCancelled };\n    };\n\n    return (\n      <InternalAutosuggest\n        ref={ref}\n        value={value}\n        readOnly={readOnly}\n        statusType={statusType}\n        options={options.length < limit ? options : []}\n        empty={options.length < limit ? suggestionText : tooManySuggestionText}\n        placeholder={placeholder}\n        errorText={errorText}\n        loadingText={loadingText}\n        enteredTextLabel={enteredTextLabel}\n        clearAriaLabel={clearAriaLabel}\n        onChange={({ detail }) => onChange(detail.value, row)}\n        onBlur={() => onBlur?.(row)}\n        onFocus={() => {\n          onLoadItems('');\n        }}\n        onLoadItems={({ detail }) => {\n          onLoadItems(detail.filteringText);\n        }}\n      />\n    );\n  }\n);\n\ninterface UndoButtonProps {\n  children: React.ReactNode;\n  onClick: () => void;\n}\n\nexport const UndoButton = React.forwardRef(\n  ({ children, onClick }: UndoButtonProps, ref: React.Ref<HTMLAnchorElement>) => {\n    return (\n      <a\n        ref={ref}\n        role=\"button\"\n        tabIndex={0}\n        className={styles['undo-button']}\n        onClick={onClick}\n        onKeyDown={event => {\n          if (event.keyCode === KeyCode.space || event.keyCode === KeyCode.enter) {\n            event.preventDefault();\n          }\n          // Enter activates the button on key down instead of key up.\n          if (event.keyCode === KeyCode.enter) {\n            onClick();\n          }\n        }}\n        onKeyUp={event => {\n          // Emulate button behavior, which also fires on space.\n          if (event.keyCode === KeyCode.space) {\n            onClick();\n          }\n        }}\n      >\n        {children}\n      </a>\n    );\n  }\n);\n"], "mappings": "AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAG/C,OAAOC,mBAAmB,MAAM,yBAAyB;AAGzD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,eAAe,EAAEC,sBAAsB,QAAQ,4BAA4B;AAEpF,OAAOC,MAAM,MAAM,iBAAiB;AA2BpC,OAAO,MAAMC,UAAU,GAAGR,KAAK,CAACS,UAAU,CACxC,CACE;EACEC,GAAG;EACHC,KAAK;EACLC,QAAQ;EACRC,cAAc;EACdC,WAAW;EACXC,SAAS;EACTC,WAAW;EACXC,cAAc;EACdC,qBAAqB;EACrBC,KAAK;EACLC,YAAY;EACZC,gBAAgB;EAChBC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,SAAS;EACTC;AAAiB,CACD,EAClBC,GAA8B,KAC5B;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAA2BW,cAAc,CAAC;EAChF,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,EAAkC;EAC9E,MAAM8B,kBAAkB,GAAG/B,MAAM,CAAqD;IACpFgC,MAAM,EAAEA,CAAA,KAAK,CAAE,CAAC;IAChBC,WAAW,EAAEA,CAAA,KAAM;GACpB,CAAC;EAEF,MAAMC,oBAAoB,GAAGlC,MAAM,CAAkB;IAAEmC,GAAG,EAAEC,SAAS;IAAE1B,KAAK,EAAE0B;EAAS,CAAE,CAAC;EAC1F,MAAMC,WAAW,GAAGA,CAACF,GAAuB,EAAEzB,KAAa,KACzDwB,oBAAoB,CAACI,OAAO,CAACH,GAAG,KAAKA,GAAG,IAAID,oBAAoB,CAACI,OAAO,CAAC5B,KAAK,KAAKA,KAAK;EAE1F,MAAM6B,WAAW,GAAIC,aAAqB,IAAI;IAC5C,IAAI,CAAChB,SAAS,IAAIa,WAAW,CAAClB,YAAY,EAAEqB,aAAa,CAAC,IAAIT,kBAAkB,CAACO,OAAO,CAACL,WAAW,EAAE,EAAE;MACtG;;IAEFF,kBAAkB,CAACO,OAAO,CAACN,MAAM,EAAE;IAEnC,IAAIE,oBAAoB,CAACI,OAAO,CAACH,GAAG,KAAKhB,YAAY,EAAE;MACrD;MACAS,UAAU,CAAC,EAAE,CAAC;KACf,MAAM,IAAIY,aAAa,KAAK,EAAE,KAAIf,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEa,OAAO,KAAIb,iBAAiB,CAACa,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;MACrG;MACAb,UAAU,CAACH,iBAAiB,CAACa,OAAO,CAAC;;IAGvCR,aAAa,CAAC,SAAS,CAAC;IACxBI,oBAAoB,CAACI,OAAO,GAAG;MAAEH,GAAG,EAAEhB,YAAY;MAAET,KAAK,EAAE8B;IAAa,CAAE;IAE1E,MAAM;MAAEE,OAAO;MAAEV,MAAM;MAAEC;IAAW,CAAE,GAAG7B,eAAe,CAACoB,SAAS,CAACgB,aAAa,CAAC,CAAC;IAClFE,OAAO,CACJC,IAAI,CAACC,SAAS,IAAG;MAChB,MAAMC,UAAU,GAAGD,SAAS,CAACE,GAAG,CAACpC,KAAK,KAAK;QAAEA;MAAK,CAAE,CAAC,CAAC;MACtDoB,aAAa,CAACM,SAAS,CAAC;MACxBR,UAAU,CAACiB,UAAU,CAAC;MACtB,IAAIpB,iBAAiB,EAAE;QACrBA,iBAAiB,CAACa,OAAO,GAAGO,UAAU;;IAE1C,CAAC,CAAC,CACDE,KAAK,CAACC,GAAG,IAAG;MACX,IAAI,EAAEA,GAAG,YAAY3C,sBAAsB,CAAC,EAAE;QAC5CyB,aAAa,CAAC,OAAO,CAAC;;IAE1B,CAAC,CAAC;IACJC,kBAAkB,CAACO,OAAO,GAAG;MAAEN,MAAM;MAAEC;IAAW,CAAE;EACtD,CAAC;EAED,OACElC,KAAA,CAAAkD,aAAA,CAAC/C,mBAAmB;IAClBwB,GAAG,EAAEA,GAAG;IACRhB,KAAK,EAAEA,KAAK;IACZC,QAAQ,EAAEA,QAAQ;IAClBkB,UAAU,EAAEA,UAAU;IACtBF,OAAO,EAAEA,OAAO,CAACc,MAAM,GAAGvB,KAAK,GAAGS,OAAO,GAAG,EAAE;IAC9CuB,KAAK,EAAEvB,OAAO,CAACc,MAAM,GAAGvB,KAAK,GAAGF,cAAc,GAAGC,qBAAqB;IACtEJ,WAAW,EAAEA,WAAW;IACxBC,SAAS,EAAEA,SAAS;IACpBC,WAAW,EAAEA,WAAW;IACxBK,gBAAgB,EAAEA,gBAAgB;IAClCC,cAAc,EAAEA,cAAc;IAC9BC,QAAQ,EAAEA,CAAC;MAAE6B;IAAM,CAAE,KAAK7B,QAAQ,CAAC6B,MAAM,CAACzC,KAAK,EAAED,GAAG,CAAC;IACrDc,MAAM,EAAEA,CAAA,KAAMA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGd,GAAG,CAAC;IAC3B2C,OAAO,EAAEA,CAAA,KAAK;MACZb,WAAW,CAAC,EAAE,CAAC;IACjB,CAAC;IACDA,WAAW,EAAEA,CAAC;MAAEY;IAAM,CAAE,KAAI;MAC1BZ,WAAW,CAACY,MAAM,CAACX,aAAa,CAAC;IACnC;EAAC,EACD;AAEN,CAAC,CACF;AAOD,OAAO,MAAMa,UAAU,GAAGtD,KAAK,CAACS,UAAU,CACxC,CAAC;EAAE8C,QAAQ;EAAEC;AAAO,CAAmB,EAAE7B,GAAiC,KAAI;EAC5E,OACE3B,KAAA,CAAAkD,aAAA;IACEvB,GAAG,EAAEA,GAAG;IACR8B,IAAI,EAAC,QAAQ;IACbC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAEpD,MAAM,CAAC,aAAa,CAAC;IAChCiD,OAAO,EAAEA,OAAO;IAChBI,SAAS,EAAEC,KAAK,IAAG;MACjB,IAAIA,KAAK,CAACC,OAAO,KAAK1D,OAAO,CAAC2D,KAAK,IAAIF,KAAK,CAACC,OAAO,KAAK1D,OAAO,CAAC4D,KAAK,EAAE;QACtEH,KAAK,CAACI,cAAc,EAAE;;MAExB;MACA,IAAIJ,KAAK,CAACC,OAAO,KAAK1D,OAAO,CAAC4D,KAAK,EAAE;QACnCR,OAAO,EAAE;;IAEb,CAAC;IACDU,OAAO,EAAEL,KAAK,IAAG;MACf;MACA,IAAIA,KAAK,CAACC,OAAO,KAAK1D,OAAO,CAAC2D,KAAK,EAAE;QACnCP,OAAO,EAAE;;IAEb;EAAC,GAEAD,QAAQ,CACP;AAER,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}