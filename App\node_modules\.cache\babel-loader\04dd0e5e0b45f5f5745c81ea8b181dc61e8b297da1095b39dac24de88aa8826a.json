{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport { isDataSeries, isXThreshold, isYThreshold, matchesX } from './utils';\nexport function computeDomainX(series, xScaleType) {\n  if (xScaleType === 'categorical') {\n    return series.reduce((acc, s) => {\n      if (isDataSeries(s.series)) {\n        s.series.data.forEach(({\n          x\n        }) => {\n          if (acc.indexOf(x) === -1) {\n            acc.push(x);\n          }\n        });\n      }\n      if (isXThreshold(s.series)) {\n        if (acc.indexOf(s.series.x) === -1) {\n          acc.push(s.series.x);\n        }\n      }\n      return acc;\n    }, []);\n  }\n  return series.reduce((acc, curr) => {\n    // Y-thresholds don't have X value.\n    if (isYThreshold(curr.series)) {\n      return acc;\n    }\n    // Compare x-threshold X with current min, max.\n    if (isXThreshold(curr.series)) {\n      const [min, max] = acc;\n      const newMin = min === undefined || min === null || curr.series.x < min ? curr.series.x : min;\n      const newMax = max === undefined || max === null || max < curr.series.x ? curr.series.x : max;\n      return [newMin, newMax];\n    }\n    // Compare all series X values with current min, max.\n    if (isDataSeries(curr.series)) {\n      return curr.series.data.reduce(([min, max], {\n        x\n      }) => {\n        const newMin = min === undefined || min === null || x < min ? x : min;\n        const newMax = max === undefined || max === null || max < x ? x : max;\n        return [newMin, newMax];\n      }, acc);\n    }\n    return acc;\n  }, []);\n}\nfunction find(arr, func) {\n  for (let i = 0; i < arr.length; i++) {\n    const found = func(arr[i]);\n    if (found) {\n      return arr[i];\n    }\n  }\n  return null;\n}\nexport function computeDomainY(series, scaleType, stackedBars) {\n  let _series = series;\n  // For stacked bars, we need to accumulate all the bar series into a positive and a negative series\n  if (stackedBars) {\n    const {\n      positiveData,\n      negativeData\n    } = series.reduce((acc, curr) => {\n      if (curr.series.type === 'bar') {\n        curr.series.data.forEach(({\n          x,\n          y\n        }) => {\n          const data = y < 0 ? acc.negativeData : acc.positiveData;\n          const stackedDatum = find(data, el => matchesX(el.x, x));\n          if (stackedDatum) {\n            stackedDatum.y += y;\n          } else {\n            data.push({\n              x,\n              y\n            });\n          }\n          return acc;\n        });\n      }\n      return acc;\n    }, {\n      positiveData: [],\n      negativeData: []\n    });\n    // Artificial series with the sum of all bars when stacked\n    const stackedSeries = [{\n      color: '',\n      index: NaN,\n      series: {\n        type: 'bar',\n        title: 'positive',\n        data: positiveData\n      }\n    }, {\n      color: '',\n      index: NaN,\n      series: {\n        type: 'bar',\n        title: 'negative',\n        data: negativeData\n      }\n    }];\n    // MixedLineBarChart can also contain other non-bar series,\n    // so we replace all bars with the artificial bar series\n    // Then proceed to compute range with it and the remaining (non-bar) series\n    _series = [...stackedSeries, ..._series.filter(s => s.series.type !== 'bar')];\n  }\n  const domain = _series.reduce((acc, curr) => {\n    // Compare threshold Y value with current min, max.\n    if (isYThreshold(curr.series)) {\n      const [min, max] = acc;\n      const newMin = min === undefined || curr.series.y < min ? curr.series.y : min;\n      const newMax = max === undefined || max < curr.series.y ? curr.series.y : max;\n      return [newMin, newMax];\n    }\n    // X-thresholds don't have Y value.\n    if (isXThreshold(curr.series)) {\n      return acc;\n    }\n    // Compare all series Y values with current min, max.\n    if (isDataSeries(curr.series)) {\n      return curr.series.data.reduce(([min, max], {\n        y\n      }) => {\n        const newMin = min === undefined || y < min ? y : min;\n        const newMax = max === undefined || max < y ? y : max;\n        return [newMin, newMax];\n      }, acc);\n    }\n    return acc;\n  }, [0, 0]);\n  // Log scales can't start from 0, so if possible, start from 1.\n  if (scaleType === 'log' && domain[0] === 0 && domain[1] > 1) {\n    return [1, domain[1]];\n  }\n  return domain;\n}", "map": {"version": 3, "names": ["isDataSeries", "isXThreshold", "isYThreshold", "matchesX", "computeDomainX", "series", "xScaleType", "reduce", "acc", "s", "data", "for<PERSON>ach", "x", "indexOf", "push", "curr", "min", "max", "newMin", "undefined", "newMax", "find", "arr", "func", "i", "length", "found", "computeDomainY", "scaleType", "stackedBars", "_series", "positiveData", "negativeData", "type", "y", "stackedDatum", "el", "stackedSeries", "color", "index", "NaN", "title", "filter", "domain"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\mixed-line-bar-chart\\domain.ts"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { InternalChartSeries, MixedLineBarChartProps, ScaleType } from './interfaces';\nimport { isDataSeries, isXThreshold, isYThreshold, matchesX } from './utils';\n\nexport function computeDomainX<T>(series: readonly InternalChartSeries<T>[], xScaleType: ScaleType) {\n  if (xScaleType === 'categorical') {\n    return series.reduce((acc, s) => {\n      if (isDataSeries(s.series)) {\n        s.series.data.forEach(({ x }) => {\n          if (acc.indexOf(x) === -1) {\n            acc.push(x);\n          }\n        });\n      }\n      if (isXThreshold(s.series)) {\n        if (acc.indexOf(s.series.x) === -1) {\n          acc.push(s.series.x);\n        }\n      }\n      return acc;\n    }, [] as T[]);\n  }\n\n  return series.reduce((acc, curr) => {\n    // Y-thresholds don't have X value.\n    if (isYThreshold(curr.series)) {\n      return acc;\n    }\n\n    // Compare x-threshold X with current min, max.\n    if (isXThreshold(curr.series)) {\n      const [min, max] = acc;\n      const newMin = min === undefined || min === null || curr.series.x < min ? curr.series.x : min;\n      const newMax = max === undefined || max === null || max < curr.series.x ? curr.series.x : max;\n      return [newMin, newMax] as T[];\n    }\n\n    // Compare all series X values with current min, max.\n    if (isDataSeries(curr.series)) {\n      return curr.series.data.reduce(([min, max], { x }) => {\n        const newMin = min === undefined || min === null || x < min ? x : min;\n        const newMax = max === undefined || max === null || max < x ? x : max;\n        return [newMin, newMax] as T[];\n      }, acc);\n    }\n\n    return acc;\n  }, [] as T[]);\n}\n\nfunction find<Q>(arr: readonly Q[], func: (el: Q) => boolean) {\n  for (let i = 0; i < arr.length; i++) {\n    const found = func(arr[i]);\n    if (found) {\n      return arr[i];\n    }\n  }\n  return null;\n}\n\nexport function computeDomainY<T>(\n  series: readonly InternalChartSeries<T>[],\n  scaleType: 'linear' | 'log',\n  stackedBars: boolean\n) {\n  let _series = series;\n\n  // For stacked bars, we need to accumulate all the bar series into a positive and a negative series\n  if (stackedBars) {\n    const { positiveData, negativeData } = series.reduce(\n      (acc, curr) => {\n        if (curr.series.type === 'bar') {\n          curr.series.data.forEach(({ x, y }) => {\n            const data = y < 0 ? acc.negativeData : acc.positiveData;\n            const stackedDatum = find(data, el => matchesX(el.x, x));\n            if (stackedDatum) {\n              stackedDatum.y += y;\n            } else {\n              data.push({ x, y });\n            }\n            return acc;\n          });\n        }\n        return acc;\n      },\n      {\n        positiveData: [] as MixedLineBarChartProps.Datum<T>[],\n        negativeData: [] as MixedLineBarChartProps.Datum<T>[],\n      }\n    );\n\n    // Artificial series with the sum of all bars when stacked\n    const stackedSeries: InternalChartSeries<T>[] = [\n      {\n        color: '',\n        index: NaN,\n        series: {\n          type: 'bar',\n          title: 'positive',\n          data: positiveData as any,\n        },\n      },\n      {\n        color: '',\n        index: NaN,\n        series: {\n          type: 'bar',\n          title: 'negative',\n          data: negativeData as any,\n        },\n      },\n    ];\n\n    // MixedLineBarChart can also contain other non-bar series,\n    // so we replace all bars with the artificial bar series\n    // Then proceed to compute range with it and the remaining (non-bar) series\n    _series = [...stackedSeries, ..._series.filter(s => s.series.type !== 'bar')];\n  }\n\n  const domain = _series.reduce(\n    (acc, curr) => {\n      // Compare threshold Y value with current min, max.\n      if (isYThreshold(curr.series)) {\n        const [min, max] = acc;\n        const newMin = min === undefined || curr.series.y < min ? curr.series.y : min;\n        const newMax = max === undefined || max < curr.series.y ? curr.series.y : max;\n        return [newMin, newMax];\n      }\n\n      // X-thresholds don't have Y value.\n      if (isXThreshold(curr.series)) {\n        return acc;\n      }\n\n      // Compare all series Y values with current min, max.\n      if (isDataSeries(curr.series)) {\n        return curr.series.data.reduce(([min, max], { y }) => {\n          const newMin = min === undefined || y < min ? y : min;\n          const newMax = max === undefined || max < y ? y : max;\n          return [newMin, newMax];\n        }, acc);\n      }\n\n      return acc;\n    },\n    [0, 0]\n  );\n\n  // Log scales can't start from 0, so if possible, start from 1.\n  if (scaleType === 'log' && domain[0] === 0 && domain[1] > 1) {\n    return [1, domain[1]];\n  }\n  return domain;\n}\n"], "mappings": "AAAA;AACA;AAGA,SAASA,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,SAAS;AAE5E,OAAM,SAAUC,cAAcA,CAAIC,MAAyC,EAAEC,UAAqB;EAChG,IAAIA,UAAU,KAAK,aAAa,EAAE;IAChC,OAAOD,MAAM,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAI;MAC9B,IAAIT,YAAY,CAACS,CAAC,CAACJ,MAAM,CAAC,EAAE;QAC1BI,CAAC,CAACJ,MAAM,CAACK,IAAI,CAACC,OAAO,CAAC,CAAC;UAAEC;QAAC,CAAE,KAAI;UAC9B,IAAIJ,GAAG,CAACK,OAAO,CAACD,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YACzBJ,GAAG,CAACM,IAAI,CAACF,CAAC,CAAC;;QAEf,CAAC,CAAC;;MAEJ,IAAIX,YAAY,CAACQ,CAAC,CAACJ,MAAM,CAAC,EAAE;QAC1B,IAAIG,GAAG,CAACK,OAAO,CAACJ,CAAC,CAACJ,MAAM,CAACO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;UAClCJ,GAAG,CAACM,IAAI,CAACL,CAAC,CAACJ,MAAM,CAACO,CAAC,CAAC;;;MAGxB,OAAOJ,GAAG;IACZ,CAAC,EAAE,EAAS,CAAC;;EAGf,OAAOH,MAAM,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEO,IAAI,KAAI;IACjC;IACA,IAAIb,YAAY,CAACa,IAAI,CAACV,MAAM,CAAC,EAAE;MAC7B,OAAOG,GAAG;;IAGZ;IACA,IAAIP,YAAY,CAACc,IAAI,CAACV,MAAM,CAAC,EAAE;MAC7B,MAAM,CAACW,GAAG,EAAEC,GAAG,CAAC,GAAGT,GAAG;MACtB,MAAMU,MAAM,GAAGF,GAAG,KAAKG,SAAS,IAAIH,GAAG,KAAK,IAAI,IAAID,IAAI,CAACV,MAAM,CAACO,CAAC,GAAGI,GAAG,GAAGD,IAAI,CAACV,MAAM,CAACO,CAAC,GAAGI,GAAG;MAC7F,MAAMI,MAAM,GAAGH,GAAG,KAAKE,SAAS,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,GAAGF,IAAI,CAACV,MAAM,CAACO,CAAC,GAAGG,IAAI,CAACV,MAAM,CAACO,CAAC,GAAGK,GAAG;MAC7F,OAAO,CAACC,MAAM,EAAEE,MAAM,CAAQ;;IAGhC;IACA,IAAIpB,YAAY,CAACe,IAAI,CAACV,MAAM,CAAC,EAAE;MAC7B,OAAOU,IAAI,CAACV,MAAM,CAACK,IAAI,CAACH,MAAM,CAAC,CAAC,CAACS,GAAG,EAAEC,GAAG,CAAC,EAAE;QAAEL;MAAC,CAAE,KAAI;QACnD,MAAMM,MAAM,GAAGF,GAAG,KAAKG,SAAS,IAAIH,GAAG,KAAK,IAAI,IAAIJ,CAAC,GAAGI,GAAG,GAAGJ,CAAC,GAAGI,GAAG;QACrE,MAAMI,MAAM,GAAGH,GAAG,KAAKE,SAAS,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,GAAGL,CAAC,GAAGA,CAAC,GAAGK,GAAG;QACrE,OAAO,CAACC,MAAM,EAAEE,MAAM,CAAQ;MAChC,CAAC,EAAEZ,GAAG,CAAC;;IAGT,OAAOA,GAAG;EACZ,CAAC,EAAE,EAAS,CAAC;AACf;AAEA,SAASa,IAAIA,CAAIC,GAAiB,EAAEC,IAAwB;EAC1D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,MAAME,KAAK,GAAGH,IAAI,CAACD,GAAG,CAACE,CAAC,CAAC,CAAC;IAC1B,IAAIE,KAAK,EAAE;MACT,OAAOJ,GAAG,CAACE,CAAC,CAAC;;;EAGjB,OAAO,IAAI;AACb;AAEA,OAAM,SAAUG,cAAcA,CAC5BtB,MAAyC,EACzCuB,SAA2B,EAC3BC,WAAoB;EAEpB,IAAIC,OAAO,GAAGzB,MAAM;EAEpB;EACA,IAAIwB,WAAW,EAAE;IACf,MAAM;MAAEE,YAAY;MAAEC;IAAY,CAAE,GAAG3B,MAAM,CAACE,MAAM,CAClD,CAACC,GAAG,EAAEO,IAAI,KAAI;MACZ,IAAIA,IAAI,CAACV,MAAM,CAAC4B,IAAI,KAAK,KAAK,EAAE;QAC9BlB,IAAI,CAACV,MAAM,CAACK,IAAI,CAACC,OAAO,CAAC,CAAC;UAAEC,CAAC;UAAEsB;QAAC,CAAE,KAAI;UACpC,MAAMxB,IAAI,GAAGwB,CAAC,GAAG,CAAC,GAAG1B,GAAG,CAACwB,YAAY,GAAGxB,GAAG,CAACuB,YAAY;UACxD,MAAMI,YAAY,GAAGd,IAAI,CAACX,IAAI,EAAE0B,EAAE,IAAIjC,QAAQ,CAACiC,EAAE,CAACxB,CAAC,EAAEA,CAAC,CAAC,CAAC;UACxD,IAAIuB,YAAY,EAAE;YAChBA,YAAY,CAACD,CAAC,IAAIA,CAAC;WACpB,MAAM;YACLxB,IAAI,CAACI,IAAI,CAAC;cAAEF,CAAC;cAAEsB;YAAC,CAAE,CAAC;;UAErB,OAAO1B,GAAG;QACZ,CAAC,CAAC;;MAEJ,OAAOA,GAAG;IACZ,CAAC,EACD;MACEuB,YAAY,EAAE,EAAuC;MACrDC,YAAY,EAAE;KACf,CACF;IAED;IACA,MAAMK,aAAa,GAA6B,CAC9C;MACEC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAEC,GAAG;MACVnC,MAAM,EAAE;QACN4B,IAAI,EAAE,KAAK;QACXQ,KAAK,EAAE,UAAU;QACjB/B,IAAI,EAAEqB;;KAET,EACD;MACEO,KAAK,EAAE,EAAE;MACTC,KAAK,EAAEC,GAAG;MACVnC,MAAM,EAAE;QACN4B,IAAI,EAAE,KAAK;QACXQ,KAAK,EAAE,UAAU;QACjB/B,IAAI,EAAEsB;;KAET,CACF;IAED;IACA;IACA;IACAF,OAAO,GAAG,CAAC,GAAGO,aAAa,EAAE,GAAGP,OAAO,CAACY,MAAM,CAACjC,CAAC,IAAIA,CAAC,CAACJ,MAAM,CAAC4B,IAAI,KAAK,KAAK,CAAC,CAAC;;EAG/E,MAAMU,MAAM,GAAGb,OAAO,CAACvB,MAAM,CAC3B,CAACC,GAAG,EAAEO,IAAI,KAAI;IACZ;IACA,IAAIb,YAAY,CAACa,IAAI,CAACV,MAAM,CAAC,EAAE;MAC7B,MAAM,CAACW,GAAG,EAAEC,GAAG,CAAC,GAAGT,GAAG;MACtB,MAAMU,MAAM,GAAGF,GAAG,KAAKG,SAAS,IAAIJ,IAAI,CAACV,MAAM,CAAC6B,CAAC,GAAGlB,GAAG,GAAGD,IAAI,CAACV,MAAM,CAAC6B,CAAC,GAAGlB,GAAG;MAC7E,MAAMI,MAAM,GAAGH,GAAG,KAAKE,SAAS,IAAIF,GAAG,GAAGF,IAAI,CAACV,MAAM,CAAC6B,CAAC,GAAGnB,IAAI,CAACV,MAAM,CAAC6B,CAAC,GAAGjB,GAAG;MAC7E,OAAO,CAACC,MAAM,EAAEE,MAAM,CAAC;;IAGzB;IACA,IAAInB,YAAY,CAACc,IAAI,CAACV,MAAM,CAAC,EAAE;MAC7B,OAAOG,GAAG;;IAGZ;IACA,IAAIR,YAAY,CAACe,IAAI,CAACV,MAAM,CAAC,EAAE;MAC7B,OAAOU,IAAI,CAACV,MAAM,CAACK,IAAI,CAACH,MAAM,CAAC,CAAC,CAACS,GAAG,EAAEC,GAAG,CAAC,EAAE;QAAEiB;MAAC,CAAE,KAAI;QACnD,MAAMhB,MAAM,GAAGF,GAAG,KAAKG,SAAS,IAAIe,CAAC,GAAGlB,GAAG,GAAGkB,CAAC,GAAGlB,GAAG;QACrD,MAAMI,MAAM,GAAGH,GAAG,KAAKE,SAAS,IAAIF,GAAG,GAAGiB,CAAC,GAAGA,CAAC,GAAGjB,GAAG;QACrD,OAAO,CAACC,MAAM,EAAEE,MAAM,CAAC;MACzB,CAAC,EAAEZ,GAAG,CAAC;;IAGT,OAAOA,GAAG;EACZ,CAAC,EACD,CAAC,CAAC,EAAE,CAAC,CAAC,CACP;EAED;EACA,IAAIoB,SAAS,KAAK,KAAK,IAAIe,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;IAC3D,OAAO,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEvB,OAAOA,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}