{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React from 'react';\nimport { useInternalI18n } from '../../i18n/context';\nimport { joinObjectPath } from '../utils';\nimport { BasicS3Table, getSharedI18Strings } from './basic-table';\nimport { formatDefault, formatSize } from './column-formats';\nimport { compareDates, getColumnAriaLabel } from './table-utils';\nexport function VersionsTable({\n  forwardFocusRef,\n  pathSegments,\n  i18nStrings,\n  isVisualRefresh,\n  isItemDisabled,\n  fetchData,\n  visibleColumns,\n  onSelect\n}) {\n  var _a, _b, _c, _d;\n  const i18n = useInternalI18n('s3-resource-selector');\n  return React.createElement(BasicS3Table, {\n    forwardFocusRef: forwardFocusRef,\n    trackBy: \"VersionId\",\n    fetchData: () => {\n      const [bucketName, ...rest] = pathSegments;\n      return fetchData(bucketName, joinObjectPath(rest));\n    },\n    i18nStrings: Object.assign(Object.assign({}, getSharedI18Strings(i18n, i18nStrings)), {\n      header: i18n('i18nStrings.selectionVersions', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.selectionVersions),\n      loadingText: i18n('i18nStrings.selectionVersionsLoading', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.selectionVersionsLoading),\n      filteringAriaLabel: (_a = i18n('i18nStrings.labelFiltering', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.labelFiltering, format => itemsType => format({\n        itemsType\n      }))) === null || _a === void 0 ? void 0 : _a((_b = i18n('i18nStrings.selectionVersions', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.selectionVersions)) !== null && _b !== void 0 ? _b : ''),\n      filteringPlaceholder: i18n('i18nStrings.selectionVersionsSearchPlaceholder', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.selectionVersionsSearchPlaceholder),\n      emptyText: i18n('i18nStrings.selectionVersionsNoItems', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.selectionVersionsNoItems),\n      selectionLabels: Object.assign(Object.assign({}, i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.labelsVersionsSelection), {\n        selectionGroupLabel: i18n('i18nStrings.labelsVersionsSelection.selectionGroupLabel', (_c = i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.labelsVersionsSelection) === null || _c === void 0 ? void 0 : _c.selectionGroupLabel),\n        itemSelectionLabel: i18n('i18nStrings.labelsVersionsSelection.itemSelectionLabel', (_d = i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.labelsVersionsSelection) === null || _d === void 0 ? void 0 : _d.itemSelectionLabel, format => (data, item) => {\n          var _a;\n          return format({\n            item__VersionId: (_a = item.VersionId) !== null && _a !== void 0 ? _a : ''\n          });\n        })\n      })\n    }),\n    isVisualRefresh: isVisualRefresh,\n    visibleColumns: visibleColumns,\n    isItemDisabled: isItemDisabled,\n    columnDefinitions: [{\n      id: 'ID',\n      header: i18n('i18nStrings.columnVersionID', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.columnVersionID),\n      ariaLabel: getColumnAriaLabel(i18n, i18nStrings, i18n('i18nStrings.columnVersionID', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.columnVersionID)),\n      sortingField: 'VersionId',\n      cell: item => item.VersionId,\n      minWidth: '250px'\n    }, {\n      id: 'LastModified',\n      header: i18n('i18nStrings.columnVersionLastModified', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.columnVersionLastModified),\n      ariaLabel: getColumnAriaLabel(i18n, i18nStrings, i18n('i18nStrings.columnVersionLastModified', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.columnVersionLastModified)),\n      sortingComparator: (a, b) => compareDates(a.LastModified, b.LastModified),\n      cell: item => formatDefault(item.LastModified)\n    }, {\n      id: 'Size',\n      header: i18n('i18nStrings.columnVersionSize', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.columnVersionSize),\n      ariaLabel: getColumnAriaLabel(i18n, i18nStrings, i18n('i18nStrings.columnVersionSize', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.columnVersionSize)),\n      sortingField: 'Size',\n      cell: item => formatSize(item.Size)\n    }],\n    onSelect: item => {\n      var _a;\n      return onSelect((_a = item === null || item === void 0 ? void 0 : item.VersionId) !== null && _a !== void 0 ? _a : '');\n    }\n  });\n}", "map": {"version": 3, "names": ["React", "useInternalI18n", "joinObjectPath", "BasicS3Table", "getSharedI18Strings", "formatDefault", "formatSize", "compareDates", "getColumnAriaLabel", "VersionsTable", "forwardFocusRef", "pathSegments", "i18nStrings", "isVisualRefresh", "isItemDisabled", "fetchData", "visibleColumns", "onSelect", "i18n", "createElement", "trackBy", "bucketName", "rest", "Object", "assign", "header", "selectionVersions", "loadingText", "selectionVersionsLoading", "filteringAriaLabel", "_a", "labelFiltering", "format", "itemsType", "_b", "filteringPlaceholder", "selectionVersionsSearchPlaceholder", "emptyText", "selectionVersionsNoItems", "<PERSON><PERSON><PERSON><PERSON>", "labelsVersionsSelection", "selectionGroupLabel", "_c", "itemSelectionLabel", "_d", "data", "item", "item__VersionId", "VersionId", "columnDefinitions", "id", "columnVersionID", "aria<PERSON><PERSON><PERSON>", "sortingField", "cell", "min<PERSON><PERSON><PERSON>", "columnVersionLastModified", "sortingComparator", "a", "b", "LastModified", "columnVersionSize", "Size"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\s3-resource-selector\\s3-modal\\versions-table.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React from 'react';\n\nimport { useInternalI18n } from '../../i18n/context';\nimport { ForwardFocusRef } from '../../internal/hooks/forward-focus';\nimport { TableProps } from '../../table/interfaces';\nimport { S3ResourceSelectorProps } from '../interfaces';\nimport { joinObjectPath } from '../utils';\nimport { BasicS3Table, getSharedI18Strings } from './basic-table';\nimport { formatDefault, formatSize } from './column-formats';\nimport { compareDates, getColumnAriaLabel } from './table-utils';\n\ninterface VersionsTableProps {\n  forwardFocusRef: React.Ref<ForwardFocusRef>;\n  pathSegments: ReadonlyArray<string>;\n  visibleColumns: ReadonlyArray<string>;\n  isItemDisabled: TableProps.IsItemDisabled<S3ResourceSelectorProps.Version> | undefined;\n  fetchData: S3ResourceSelectorProps['fetchVersions'];\n  i18nStrings: S3ResourceSelectorProps.I18nStrings | undefined;\n  isVisualRefresh?: boolean;\n  onSelect: (versionId: string) => void;\n}\n\nexport function VersionsTable({\n  forwardFocusRef,\n  pathSegments,\n  i18nStrings,\n  isVisualRefresh,\n  isItemDisabled,\n  fetchData,\n  visibleColumns,\n  onSelect,\n}: VersionsTableProps) {\n  const i18n = useInternalI18n('s3-resource-selector');\n\n  return (\n    <BasicS3Table<S3ResourceSelectorProps.Version>\n      forwardFocusRef={forwardFocusRef}\n      trackBy=\"VersionId\"\n      fetchData={() => {\n        const [bucketName, ...rest] = pathSegments;\n        return fetchData(bucketName, joinObjectPath(rest));\n      }}\n      i18nStrings={{\n        ...getSharedI18Strings(i18n, i18nStrings),\n        header: i18n('i18nStrings.selectionVersions', i18nStrings?.selectionVersions),\n        loadingText: i18n('i18nStrings.selectionVersionsLoading', i18nStrings?.selectionVersionsLoading),\n        filteringAriaLabel: i18n(\n          'i18nStrings.labelFiltering',\n          i18nStrings?.labelFiltering,\n          format => itemsType => format({ itemsType })\n        )?.(i18n('i18nStrings.selectionVersions', i18nStrings?.selectionVersions) ?? ''),\n        filteringPlaceholder: i18n(\n          'i18nStrings.selectionVersionsSearchPlaceholder',\n          i18nStrings?.selectionVersionsSearchPlaceholder\n        ),\n        emptyText: i18n('i18nStrings.selectionVersionsNoItems', i18nStrings?.selectionVersionsNoItems),\n        selectionLabels: {\n          ...i18nStrings?.labelsVersionsSelection,\n          selectionGroupLabel: i18n(\n            'i18nStrings.labelsVersionsSelection.selectionGroupLabel',\n            i18nStrings?.labelsVersionsSelection?.selectionGroupLabel\n          ),\n          itemSelectionLabel: i18n(\n            'i18nStrings.labelsVersionsSelection.itemSelectionLabel',\n            i18nStrings?.labelsVersionsSelection?.itemSelectionLabel,\n            format => (data, item) => format({ item__VersionId: item.VersionId ?? '' })\n          ),\n        },\n      }}\n      isVisualRefresh={isVisualRefresh}\n      visibleColumns={visibleColumns}\n      isItemDisabled={isItemDisabled}\n      columnDefinitions={[\n        {\n          id: 'ID',\n          header: i18n('i18nStrings.columnVersionID', i18nStrings?.columnVersionID),\n          ariaLabel: getColumnAriaLabel(\n            i18n,\n            i18nStrings,\n            i18n('i18nStrings.columnVersionID', i18nStrings?.columnVersionID)\n          ),\n          sortingField: 'VersionId',\n          cell: item => item.VersionId,\n          minWidth: '250px',\n        },\n        {\n          id: 'LastModified',\n          header: i18n('i18nStrings.columnVersionLastModified', i18nStrings?.columnVersionLastModified),\n          ariaLabel: getColumnAriaLabel(\n            i18n,\n            i18nStrings,\n            i18n('i18nStrings.columnVersionLastModified', i18nStrings?.columnVersionLastModified)\n          ),\n          sortingComparator: (a, b) => compareDates(a.LastModified, b.LastModified),\n          cell: item => formatDefault(item.LastModified),\n        },\n        {\n          id: 'Size',\n          header: i18n('i18nStrings.columnVersionSize', i18nStrings?.columnVersionSize),\n          ariaLabel: getColumnAriaLabel(\n            i18n,\n            i18nStrings,\n            i18n('i18nStrings.columnVersionSize', i18nStrings?.columnVersionSize)\n          ),\n          sortingField: 'Size',\n          cell: item => formatSize(item.Size),\n        },\n      ]}\n      onSelect={item => onSelect(item?.VersionId ?? '')}\n    />\n  );\n}\n"], "mappings": "AAAA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,eAAe,QAAQ,oBAAoB;AAIpD,SAASC,cAAc,QAAQ,UAAU;AACzC,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,eAAe;AACjE,SAASC,aAAa,EAAEC,UAAU,QAAQ,kBAAkB;AAC5D,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,eAAe;AAahE,OAAM,SAAUC,aAAaA,CAAC;EAC5BC,eAAe;EACfC,YAAY;EACZC,WAAW;EACXC,eAAe;EACfC,cAAc;EACdC,SAAS;EACTC,cAAc;EACdC;AAAQ,CACW;;EACnB,MAAMC,IAAI,GAAGjB,eAAe,CAAC,sBAAsB,CAAC;EAEpD,OACED,KAAA,CAAAmB,aAAA,CAAChB,YAAY;IACXO,eAAe,EAAEA,eAAe;IAChCU,OAAO,EAAC,WAAW;IACnBL,SAAS,EAAEA,CAAA,KAAK;MACd,MAAM,CAACM,UAAU,EAAE,GAAGC,IAAI,CAAC,GAAGX,YAAY;MAC1C,OAAOI,SAAS,CAACM,UAAU,EAAEnB,cAAc,CAACoB,IAAI,CAAC,CAAC;IACpD,CAAC;IACDV,WAAW,EAAAW,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACNpB,mBAAmB,CAACc,IAAI,EAAEN,WAAW,CAAC;MACzCa,MAAM,EAAEP,IAAI,CAAC,+BAA+B,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,iBAAiB,CAAC;MAC7EC,WAAW,EAAET,IAAI,CAAC,sCAAsC,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEgB,wBAAwB,CAAC;MAChGC,kBAAkB,EAAE,CAAAC,EAAA,GAAAZ,IAAI,CACtB,4BAA4B,EAC5BN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmB,cAAc,EAC3BC,MAAM,IAAIC,SAAS,IAAID,MAAM,CAAC;QAAEC;MAAS,CAAE,CAAC,CAC7C,cAAAH,EAAA,uBAAAA,EAAA,CAAG,CAAAI,EAAA,GAAAhB,IAAI,CAAC,+BAA+B,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEc,iBAAiB,CAAC,cAAAQ,EAAA,cAAAA,EAAA,GAAI,EAAE,CAAC;MAChFC,oBAAoB,EAAEjB,IAAI,CACxB,gDAAgD,EAChDN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEwB,kCAAkC,CAChD;MACDC,SAAS,EAAEnB,IAAI,CAAC,sCAAsC,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE0B,wBAAwB,CAAC;MAC9FC,eAAe,EAAAhB,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACVZ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4B,uBAAuB;QACvCC,mBAAmB,EAAEvB,IAAI,CACvB,yDAAyD,EACzD,CAAAwB,EAAA,GAAA9B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4B,uBAAuB,cAAAE,EAAA,uBAAAA,EAAA,CAAED,mBAAmB,CAC1D;QACDE,kBAAkB,EAAEzB,IAAI,CACtB,wDAAwD,EACxD,CAAA0B,EAAA,GAAAhC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4B,uBAAuB,cAAAI,EAAA,uBAAAA,EAAA,CAAED,kBAAkB,EACxDX,MAAM,IAAI,CAACa,IAAI,EAAEC,IAAI,KAAI;UAAA,IAAAhB,EAAA;UAAC,OAAAE,MAAM,CAAC;YAAEe,eAAe,EAAE,CAAAjB,EAAA,GAAAgB,IAAI,CAACE,SAAS,cAAAlB,EAAA,cAAAA,EAAA,GAAI;UAAE,CAAE,CAAC;QAAA;MAC5E;IAAA;IAGLjB,eAAe,EAAEA,eAAe;IAChCG,cAAc,EAAEA,cAAc;IAC9BF,cAAc,EAAEA,cAAc;IAC9BmC,iBAAiB,EAAE,CACjB;MACEC,EAAE,EAAE,IAAI;MACRzB,MAAM,EAAEP,IAAI,CAAC,6BAA6B,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuC,eAAe,CAAC;MACzEC,SAAS,EAAE5C,kBAAkB,CAC3BU,IAAI,EACJN,WAAW,EACXM,IAAI,CAAC,6BAA6B,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEuC,eAAe,CAAC,CAClE;MACDE,YAAY,EAAE,WAAW;MACzBC,IAAI,EAAER,IAAI,IAAIA,IAAI,CAACE,SAAS;MAC5BO,QAAQ,EAAE;KACX,EACD;MACEL,EAAE,EAAE,cAAc;MAClBzB,MAAM,EAAEP,IAAI,CAAC,uCAAuC,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4C,yBAAyB,CAAC;MAC7FJ,SAAS,EAAE5C,kBAAkB,CAC3BU,IAAI,EACJN,WAAW,EACXM,IAAI,CAAC,uCAAuC,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4C,yBAAyB,CAAC,CACtF;MACDC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKpD,YAAY,CAACmD,CAAC,CAACE,YAAY,EAAED,CAAC,CAACC,YAAY,CAAC;MACzEN,IAAI,EAAER,IAAI,IAAIzC,aAAa,CAACyC,IAAI,CAACc,YAAY;KAC9C,EACD;MACEV,EAAE,EAAE,MAAM;MACVzB,MAAM,EAAEP,IAAI,CAAC,+BAA+B,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiD,iBAAiB,CAAC;MAC7ET,SAAS,EAAE5C,kBAAkB,CAC3BU,IAAI,EACJN,WAAW,EACXM,IAAI,CAAC,+BAA+B,EAAEN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiD,iBAAiB,CAAC,CACtE;MACDR,YAAY,EAAE,MAAM;MACpBC,IAAI,EAAER,IAAI,IAAIxC,UAAU,CAACwC,IAAI,CAACgB,IAAI;KACnC,CACF;IACD7C,QAAQ,EAAE6B,IAAI,IAAG;MAAA,IAAAhB,EAAA;MAAC,OAAAb,QAAQ,CAAC,CAAAa,EAAA,GAAAgB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,cAAAlB,EAAA,cAAAA,EAAA,GAAI,EAAE,CAAC;IAAA;EAAA,EACjD;AAEN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}