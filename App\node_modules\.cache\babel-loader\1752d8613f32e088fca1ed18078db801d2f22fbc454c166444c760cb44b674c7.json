{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport { createRef, useCallback, useEffect, useRef } from 'react';\nexport function useMultipleFocusControl(restoreFocus, activeDrawersIds) {\n  const refs = useRef({});\n  activeDrawersIds.forEach(drawerId => {\n    if (!refs.current[drawerId]) {\n      refs.current[drawerId] = {\n        toggle: createRef(),\n        close: createRef(),\n        slider: createRef()\n      };\n    }\n  });\n  const doFocus = useCallback((drawerId, open = true) => {\n    var _a, _b, _c, _d, _e, _f;\n    if (!shouldFocus.current) {\n      return;\n    }\n    const ref = refs.current[drawerId];\n    if (open) {\n      previousFocusedElement.current = document.activeElement !== document.body ? document.activeElement : undefined;\n      if ((_a = ref === null || ref === void 0 ? void 0 : ref.slider) === null || _a === void 0 ? void 0 : _a.current) {\n        (_b = ref.slider.current) === null || _b === void 0 ? void 0 : _b.focus();\n      } else {\n        (_d = (_c = ref === null || ref === void 0 ? void 0 : ref.close) === null || _c === void 0 ? void 0 : _c.current) === null || _d === void 0 ? void 0 : _d.focus();\n      }\n    } else {\n      if (restoreFocus && previousFocusedElement.current && document.contains(previousFocusedElement.current)) {\n        previousFocusedElement.current.focus();\n        previousFocusedElement.current = undefined;\n      } else {\n        (_f = (_e = ref === null || ref === void 0 ? void 0 : ref.toggle) === null || _e === void 0 ? void 0 : _e.current) === null || _f === void 0 ? void 0 : _f.focus();\n      }\n    }\n    shouldFocus.current = false;\n  }, [refs, restoreFocus]);\n  const setFocus = params => {\n    const {\n      force = false,\n      drawerId = null,\n      open = true\n    } = params || {};\n    shouldFocus.current = true;\n    if (force && (!drawerId || activeDrawersIds.includes(drawerId))) {\n      doFocus(drawerId, open);\n    }\n  };\n  const loseFocus = useCallback(() => {\n    previousFocusedElement.current = undefined;\n  }, []);\n  const previousFocusedElement = useRef();\n  const shouldFocus = useRef(false);\n  useEffect(() => {\n    doFocus(activeDrawersIds[0]);\n  }, [activeDrawersIds, doFocus]);\n  return {\n    refs: refs.current,\n    setFocus,\n    loseFocus\n  };\n}\nexport function useFocusControl(isOpen, restoreFocus = false, activeDrawerId) {\n  const refs = {\n    toggle: useRef(null),\n    close: useRef(null),\n    slider: useRef(null)\n  };\n  const previousFocusedElement = useRef();\n  const shouldFocus = useRef(false);\n  const doFocus = () => {\n    var _a, _b, _c;\n    if (!shouldFocus.current) {\n      return;\n    }\n    if (isOpen) {\n      previousFocusedElement.current = document.activeElement !== document.body ? document.activeElement : undefined;\n      if (refs.slider.current) {\n        (_a = refs.slider.current) === null || _a === void 0 ? void 0 : _a.focus();\n      } else {\n        (_b = refs.close.current) === null || _b === void 0 ? void 0 : _b.focus();\n      }\n    } else {\n      if (restoreFocus && previousFocusedElement.current && document.contains(previousFocusedElement.current)) {\n        previousFocusedElement.current.focus();\n        previousFocusedElement.current = undefined;\n      } else {\n        (_c = refs.toggle.current) === null || _c === void 0 ? void 0 : _c.focus();\n      }\n    }\n    shouldFocus.current = false;\n  };\n  const setFocus = force => {\n    shouldFocus.current = true;\n    if (force && isOpen) {\n      doFocus();\n    }\n  };\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useEffect(doFocus, [isOpen, activeDrawerId]);\n  const loseFocus = useCallback(() => {\n    previousFocusedElement.current = undefined;\n  }, []);\n  return {\n    refs,\n    setFocus,\n    loseFocus\n  };\n}", "map": {"version": 3, "names": ["createRef", "useCallback", "useEffect", "useRef", "useMultipleFocusControl", "restoreFocus", "activeDrawersIds", "refs", "for<PERSON>ach", "drawerId", "current", "toggle", "close", "slider", "doFocus", "open", "shouldFocus", "ref", "previousFocusedElement", "document", "activeElement", "body", "undefined", "_a", "_b", "focus", "_d", "_c", "contains", "_f", "_e", "setFocus", "params", "force", "includes", "loseFocus", "useFocusControl", "isOpen", "activeDrawerId"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\app-layout\\utils\\use-focus-control.ts"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport { createRef, RefObject, useCallback, useEffect, useRef } from 'react';\n\nexport interface Focusable {\n  focus(): void;\n}\n\nexport interface FocusControlRefs {\n  toggle: RefObject<Focusable>;\n  close: RefObject<Focusable>;\n  slider: RefObject<HTMLDivElement>;\n}\n\nexport interface FocusControlState {\n  refs: FocusControlRefs;\n  setFocus: (force?: boolean) => void;\n  loseFocus: () => void;\n}\n\nexport interface FocusControlMultipleStates {\n  refs: Record<string, FocusControlRefs>;\n  setFocus: (params?: { force?: boolean; drawerId?: string; open?: boolean }) => void;\n  loseFocus: () => void;\n}\n\nexport function useMultipleFocusControl(\n  restoreFocus: boolean,\n  activeDrawersIds: Array<string>\n): FocusControlMultipleStates {\n  const refs = useRef<Record<string, FocusControlRefs>>({});\n\n  activeDrawersIds.forEach(drawerId => {\n    if (!refs.current[drawerId]) {\n      refs.current[drawerId] = {\n        toggle: createRef<Focusable>(),\n        close: createRef<Focusable>(),\n        slider: createRef<HTMLDivElement>(),\n      };\n    }\n  });\n\n  const doFocus = useCallback(\n    (drawerId: string, open = true) => {\n      if (!shouldFocus.current) {\n        return;\n      }\n      const ref = refs.current[drawerId];\n      if (open) {\n        previousFocusedElement.current =\n          document.activeElement !== document.body ? (document.activeElement as HTMLElement) : undefined;\n        if (ref?.slider?.current) {\n          ref.slider.current?.focus();\n        } else {\n          ref?.close?.current?.focus();\n        }\n      } else {\n        if (restoreFocus && previousFocusedElement.current && document.contains(previousFocusedElement.current)) {\n          previousFocusedElement.current.focus();\n          previousFocusedElement.current = undefined;\n        } else {\n          ref?.toggle?.current?.focus();\n        }\n      }\n      shouldFocus.current = false;\n    },\n    [refs, restoreFocus]\n  );\n\n  const setFocus = (params?: { force?: boolean; drawerId?: string; open?: boolean }) => {\n    const { force = false, drawerId = null, open = true } = params || {};\n    shouldFocus.current = true;\n    if (force && (!drawerId || activeDrawersIds.includes(drawerId))) {\n      doFocus(drawerId!, open);\n    }\n  };\n\n  const loseFocus = useCallback(() => {\n    previousFocusedElement.current = undefined;\n  }, []);\n\n  const previousFocusedElement = useRef<HTMLElement>();\n  const shouldFocus = useRef(false);\n\n  useEffect(() => {\n    doFocus(activeDrawersIds[0]);\n  }, [activeDrawersIds, doFocus]);\n\n  return {\n    refs: refs.current,\n    setFocus,\n    loseFocus,\n  };\n}\n\nexport function useFocusControl(\n  isOpen: boolean,\n  restoreFocus = false,\n  activeDrawerId?: string | null\n): FocusControlState {\n  const refs = {\n    toggle: useRef<Focusable>(null),\n    close: useRef<Focusable>(null),\n    slider: useRef<HTMLDivElement>(null),\n  };\n  const previousFocusedElement = useRef<HTMLElement>();\n  const shouldFocus = useRef(false);\n\n  const doFocus = () => {\n    if (!shouldFocus.current) {\n      return;\n    }\n    if (isOpen) {\n      previousFocusedElement.current =\n        document.activeElement !== document.body ? (document.activeElement as HTMLElement) : undefined;\n      if (refs.slider.current) {\n        refs.slider.current?.focus();\n      } else {\n        refs.close.current?.focus();\n      }\n    } else {\n      if (restoreFocus && previousFocusedElement.current && document.contains(previousFocusedElement.current)) {\n        previousFocusedElement.current.focus();\n        previousFocusedElement.current = undefined;\n      } else {\n        refs.toggle.current?.focus();\n      }\n    }\n    shouldFocus.current = false;\n  };\n\n  const setFocus = (force?: boolean) => {\n    shouldFocus.current = true;\n    if (force && isOpen) {\n      doFocus();\n    }\n  };\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  useEffect(doFocus, [isOpen, activeDrawerId]);\n\n  const loseFocus = useCallback(() => {\n    previousFocusedElement.current = undefined;\n  }, []);\n\n  return {\n    refs,\n    setFocus,\n    loseFocus,\n  };\n}\n"], "mappings": "AAAA;AACA;AACA,SAASA,SAAS,EAAaC,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAwB5E,OAAM,SAAUC,uBAAuBA,CACrCC,YAAqB,EACrBC,gBAA+B;EAE/B,MAAMC,IAAI,GAAGJ,MAAM,CAAmC,EAAE,CAAC;EAEzDG,gBAAgB,CAACE,OAAO,CAACC,QAAQ,IAAG;IAClC,IAAI,CAACF,IAAI,CAACG,OAAO,CAACD,QAAQ,CAAC,EAAE;MAC3BF,IAAI,CAACG,OAAO,CAACD,QAAQ,CAAC,GAAG;QACvBE,MAAM,EAAEX,SAAS,EAAa;QAC9BY,KAAK,EAAEZ,SAAS,EAAa;QAC7Ba,MAAM,EAAEb,SAAS;OAClB;;EAEL,CAAC,CAAC;EAEF,MAAMc,OAAO,GAAGb,WAAW,CACzB,CAACQ,QAAgB,EAAEM,IAAI,GAAG,IAAI,KAAI;;IAChC,IAAI,CAACC,WAAW,CAACN,OAAO,EAAE;MACxB;;IAEF,MAAMO,GAAG,GAAGV,IAAI,CAACG,OAAO,CAACD,QAAQ,CAAC;IAClC,IAAIM,IAAI,EAAE;MACRG,sBAAsB,CAACR,OAAO,GAC5BS,QAAQ,CAACC,aAAa,KAAKD,QAAQ,CAACE,IAAI,GAAIF,QAAQ,CAACC,aAA6B,GAAGE,SAAS;MAChG,IAAI,CAAAC,EAAA,GAAAN,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEJ,MAAM,cAAAU,EAAA,uBAAAA,EAAA,CAAEb,OAAO,EAAE;QACxB,CAAAc,EAAA,GAAAP,GAAG,CAACJ,MAAM,CAACH,OAAO,cAAAc,EAAA,uBAAAA,EAAA,CAAEC,KAAK,EAAE;OAC5B,MAAM;QACL,CAAAC,EAAA,IAAAC,EAAA,GAAAV,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEL,KAAK,cAAAe,EAAA,uBAAAA,EAAA,CAAEjB,OAAO,cAAAgB,EAAA,uBAAAA,EAAA,CAAED,KAAK,EAAE;;KAE/B,MAAM;MACL,IAAIpB,YAAY,IAAIa,sBAAsB,CAACR,OAAO,IAAIS,QAAQ,CAACS,QAAQ,CAACV,sBAAsB,CAACR,OAAO,CAAC,EAAE;QACvGQ,sBAAsB,CAACR,OAAO,CAACe,KAAK,EAAE;QACtCP,sBAAsB,CAACR,OAAO,GAAGY,SAAS;OAC3C,MAAM;QACL,CAAAO,EAAA,IAAAC,EAAA,GAAAb,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEN,MAAM,cAAAmB,EAAA,uBAAAA,EAAA,CAAEpB,OAAO,cAAAmB,EAAA,uBAAAA,EAAA,CAAEJ,KAAK,EAAE;;;IAGjCT,WAAW,CAACN,OAAO,GAAG,KAAK;EAC7B,CAAC,EACD,CAACH,IAAI,EAAEF,YAAY,CAAC,CACrB;EAED,MAAM0B,QAAQ,GAAIC,MAA+D,IAAI;IACnF,MAAM;MAAEC,KAAK,GAAG,KAAK;MAAExB,QAAQ,GAAG,IAAI;MAAEM,IAAI,GAAG;IAAI,CAAE,GAAGiB,MAAM,IAAI,EAAE;IACpEhB,WAAW,CAACN,OAAO,GAAG,IAAI;IAC1B,IAAIuB,KAAK,KAAK,CAACxB,QAAQ,IAAIH,gBAAgB,CAAC4B,QAAQ,CAACzB,QAAQ,CAAC,CAAC,EAAE;MAC/DK,OAAO,CAACL,QAAS,EAAEM,IAAI,CAAC;;EAE5B,CAAC;EAED,MAAMoB,SAAS,GAAGlC,WAAW,CAAC,MAAK;IACjCiB,sBAAsB,CAACR,OAAO,GAAGY,SAAS;EAC5C,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMJ,sBAAsB,GAAGf,MAAM,EAAe;EACpD,MAAMa,WAAW,GAAGb,MAAM,CAAC,KAAK,CAAC;EAEjCD,SAAS,CAAC,MAAK;IACbY,OAAO,CAACR,gBAAgB,CAAC,CAAC,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACA,gBAAgB,EAAEQ,OAAO,CAAC,CAAC;EAE/B,OAAO;IACLP,IAAI,EAAEA,IAAI,CAACG,OAAO;IAClBqB,QAAQ;IACRI;GACD;AACH;AAEA,OAAM,SAAUC,eAAeA,CAC7BC,MAAe,EACfhC,YAAY,GAAG,KAAK,EACpBiC,cAA8B;EAE9B,MAAM/B,IAAI,GAAG;IACXI,MAAM,EAAER,MAAM,CAAY,IAAI,CAAC;IAC/BS,KAAK,EAAET,MAAM,CAAY,IAAI,CAAC;IAC9BU,MAAM,EAAEV,MAAM,CAAiB,IAAI;GACpC;EACD,MAAMe,sBAAsB,GAAGf,MAAM,EAAe;EACpD,MAAMa,WAAW,GAAGb,MAAM,CAAC,KAAK,CAAC;EAEjC,MAAMW,OAAO,GAAGA,CAAA,KAAK;;IACnB,IAAI,CAACE,WAAW,CAACN,OAAO,EAAE;MACxB;;IAEF,IAAI2B,MAAM,EAAE;MACVnB,sBAAsB,CAACR,OAAO,GAC5BS,QAAQ,CAACC,aAAa,KAAKD,QAAQ,CAACE,IAAI,GAAIF,QAAQ,CAACC,aAA6B,GAAGE,SAAS;MAChG,IAAIf,IAAI,CAACM,MAAM,CAACH,OAAO,EAAE;QACvB,CAAAa,EAAA,GAAAhB,IAAI,CAACM,MAAM,CAACH,OAAO,cAAAa,EAAA,uBAAAA,EAAA,CAAEE,KAAK,EAAE;OAC7B,MAAM;QACL,CAAAD,EAAA,GAAAjB,IAAI,CAACK,KAAK,CAACF,OAAO,cAAAc,EAAA,uBAAAA,EAAA,CAAEC,KAAK,EAAE;;KAE9B,MAAM;MACL,IAAIpB,YAAY,IAAIa,sBAAsB,CAACR,OAAO,IAAIS,QAAQ,CAACS,QAAQ,CAACV,sBAAsB,CAACR,OAAO,CAAC,EAAE;QACvGQ,sBAAsB,CAACR,OAAO,CAACe,KAAK,EAAE;QACtCP,sBAAsB,CAACR,OAAO,GAAGY,SAAS;OAC3C,MAAM;QACL,CAAAK,EAAA,GAAApB,IAAI,CAACI,MAAM,CAACD,OAAO,cAAAiB,EAAA,uBAAAA,EAAA,CAAEF,KAAK,EAAE;;;IAGhCT,WAAW,CAACN,OAAO,GAAG,KAAK;EAC7B,CAAC;EAED,MAAMqB,QAAQ,GAAIE,KAAe,IAAI;IACnCjB,WAAW,CAACN,OAAO,GAAG,IAAI;IAC1B,IAAIuB,KAAK,IAAII,MAAM,EAAE;MACnBvB,OAAO,EAAE;;EAEb,CAAC;EAED;EACAZ,SAAS,CAACY,OAAO,EAAE,CAACuB,MAAM,EAAEC,cAAc,CAAC,CAAC;EAE5C,MAAMH,SAAS,GAAGlC,WAAW,CAAC,MAAK;IACjCiB,sBAAsB,CAACR,OAAO,GAAGY,SAAS;EAC5C,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLf,IAAI;IACJwB,QAAQ;IACRI;GACD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}