{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { memo, useRef } from 'react';\nimport clsx from 'clsx';\nimport { useInternalI18n } from '../../../i18n/context';\nimport { TICK_LENGTH, TICK_LINE_HEIGHT, TICK_MARGIN } from './constants';\nimport { formatTicks, getVisibleTicks } from './label-utils';\nimport styles from './styles.css.js';\nexport function useBLockEndLabels({\n  ticks,\n  scale,\n  tickFormatter\n}) {\n  const virtualTextRef = useRef(null);\n  const cacheRef = useRef({});\n  const getLabelSpace = label => {\n    if (cacheRef.current[label] !== undefined && cacheRef.current[label] !== 0) {\n      return cacheRef.current[label];\n    }\n    if (virtualTextRef.current && virtualTextRef.current.getComputedTextLength) {\n      virtualTextRef.current.textContent = label;\n      cacheRef.current[label] = virtualTextRef.current.getComputedTextLength();\n      return cacheRef.current[label];\n    }\n    return 0;\n  };\n  const formattedTicks = formatTicks({\n    ticks,\n    scale,\n    getLabelSpace,\n    tickFormatter\n  });\n  if (virtualTextRef.current) {\n    virtualTextRef.current.textContent = '';\n  }\n  let height = TICK_LENGTH + TICK_MARGIN;\n  for (const {\n    lines\n  } of formattedTicks) {\n    height = Math.max(height, TICK_LENGTH + TICK_MARGIN + lines.length * TICK_LINE_HEIGHT);\n  }\n  return {\n    virtualTextRef,\n    formattedTicks,\n    height\n  };\n}\nexport default memo(BlockEndLabels);\n// Renders the visible tick labels on the bottom axis, as well as their grid lines.\nfunction BlockEndLabels({\n  axis = 'x',\n  width,\n  height,\n  scale,\n  title,\n  ariaRoleDescription,\n  offsetLeft = 0,\n  offsetRight = 0,\n  virtualTextRef,\n  formattedTicks,\n  isRTL = false\n}) {\n  const i18n = useInternalI18n('[charts]');\n  const xOffset = scale.isCategorical() && axis === 'x' ? Math.max(0, scale.d3Scale.bandwidth() - 1) / 2 : 0;\n  const offsetInlineStart = isRTL ? offsetRight : offsetLeft;\n  const offsetInlineEnd = isRTL ? offsetLeft : offsetRight;\n  const from = 0 - offsetInlineStart - xOffset;\n  const until = width + offsetInlineEnd - xOffset;\n  const balanceLabels = axis === 'x' && scale.scaleType !== 'log';\n  const visibleTicks = getVisibleTicks(formattedTicks, from, until, balanceLabels);\n  return React.createElement(\"g\", {\n    transform: `translate(0,${height})`,\n    className: styles['labels-block-end'],\n    \"aria-label\": title,\n    role: \"list\",\n    \"aria-roledescription\": i18n('i18nStrings.chartAriaRoleDescription', ariaRoleDescription),\n    \"aria-hidden\": true\n  }, visibleTicks.map(({\n    position,\n    lines\n  }, index) => isFinite(position) && React.createElement(\"g\", {\n    key: index,\n    transform: `translate(${position + xOffset},0)`,\n    className: clsx(styles.ticks, styles['ticks--bottom'], {\n      [styles['ticks--x']]: axis === 'x',\n      [styles['ticks--y']]: axis === 'y'\n    }),\n    role: \"listitem\",\n    \"aria-label\": lines.join('\\n')\n  }, React.createElement(\"line\", {\n    className: styles.ticks__line,\n    x1: 0,\n    x2: 0,\n    y1: 0,\n    y2: TICK_LENGTH,\n    \"aria-hidden\": \"true\"\n  }), lines.map((line, lineIndex) => React.createElement(\"text\", {\n    className: styles.ticks__text,\n    key: lineIndex,\n    x: 0,\n    y: TICK_LENGTH + TICK_MARGIN + lineIndex * TICK_LINE_HEIGHT\n  }, line)))), React.createElement(\"text\", {\n    ref: virtualTextRef,\n    x: 0,\n    y: 0,\n    style: {\n      visibility: 'hidden'\n    },\n    \"aria-hidden\": \"true\"\n  }));\n}", "map": {"version": 3, "names": ["React", "memo", "useRef", "clsx", "useInternalI18n", "TICK_LENGTH", "TICK_LINE_HEIGHT", "TICK_MARGIN", "formatTicks", "getVisibleTicks", "styles", "useBLockEndLabels", "ticks", "scale", "tick<PERSON><PERSON><PERSON><PERSON>", "virtualTextRef", "cacheRef", "getLabelSpace", "label", "current", "undefined", "getComputedTextLength", "textContent", "formattedTicks", "height", "lines", "Math", "max", "length", "BlockEndLabels", "axis", "width", "title", "ariaRoleDescription", "offsetLeft", "offsetRight", "isRTL", "i18n", "xOffset", "isCategorical", "d3Scale", "bandwidth", "offsetInlineStart", "offsetInlineEnd", "from", "until", "balanceLabels", "scaleType", "visibleTicks", "createElement", "transform", "className", "role", "map", "position", "index", "isFinite", "key", "join", "ticks__line", "x1", "x2", "y1", "y2", "line", "lineIndex", "ticks__text", "x", "y", "ref", "style", "visibility"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\internal\\components\\cartesian-chart\\block-end-labels.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { memo, useRef } from 'react';\nimport clsx from 'clsx';\n\nimport { useInternalI18n } from '../../../i18n/context';\nimport { TICK_LENGTH, TICK_LINE_HEIGHT, TICK_MARGIN } from './constants';\nimport { ChartDataTypes } from './interfaces';\nimport { FormattedTick, formatTicks, getVisibleTicks } from './label-utils';\nimport { ChartScale, NumericChartScale } from './scales';\n\nimport styles from './styles.css.js';\n\ninterface BlockEndLabelsProps {\n  axis?: 'x' | 'y';\n  width: number;\n  height: number;\n  scale: ChartScale | NumericChartScale;\n  title?: string;\n  ariaRoleDescription?: string;\n  offsetLeft?: number;\n  offsetRight?: number;\n  virtualTextRef: React.Ref<SVGTextElement>;\n  formattedTicks: readonly FormattedTick[];\n  isRTL?: boolean;\n}\n\nexport function useBLockEndLabels({\n  ticks,\n  scale,\n  tickFormatter,\n}: {\n  scale: ChartScale | NumericChartScale;\n  ticks: readonly ChartDataTypes[];\n  tickFormatter?: (value: ChartDataTypes) => string;\n}) {\n  const virtualTextRef = useRef<SVGTextElement>(null);\n\n  const cacheRef = useRef<{ [label: string]: number }>({});\n  const getLabelSpace = (label: string) => {\n    if (cacheRef.current[label] !== undefined && cacheRef.current[label] !== 0) {\n      return cacheRef.current[label];\n    }\n    if (virtualTextRef.current && virtualTextRef.current.getComputedTextLength) {\n      virtualTextRef.current.textContent = label;\n      cacheRef.current[label] = virtualTextRef.current.getComputedTextLength();\n      return cacheRef.current[label];\n    }\n    return 0;\n  };\n\n  const formattedTicks = formatTicks({ ticks, scale, getLabelSpace, tickFormatter });\n\n  if (virtualTextRef.current) {\n    virtualTextRef.current.textContent = '';\n  }\n\n  let height = TICK_LENGTH + TICK_MARGIN;\n  for (const { lines } of formattedTicks) {\n    height = Math.max(height, TICK_LENGTH + TICK_MARGIN + lines.length * TICK_LINE_HEIGHT);\n  }\n\n  return { virtualTextRef, formattedTicks, height };\n}\n\nexport default memo(BlockEndLabels) as typeof BlockEndLabels;\n\n// Renders the visible tick labels on the bottom axis, as well as their grid lines.\nfunction BlockEndLabels({\n  axis = 'x',\n  width,\n  height,\n  scale,\n  title,\n  ariaRoleDescription,\n  offsetLeft = 0,\n  offsetRight = 0,\n  virtualTextRef,\n  formattedTicks,\n  isRTL = false,\n}: BlockEndLabelsProps) {\n  const i18n = useInternalI18n('[charts]');\n\n  const xOffset = scale.isCategorical() && axis === 'x' ? Math.max(0, scale.d3Scale.bandwidth() - 1) / 2 : 0;\n\n  const offsetInlineStart = isRTL ? offsetRight : offsetLeft;\n  const offsetInlineEnd = isRTL ? offsetLeft : offsetRight;\n\n  const from = 0 - offsetInlineStart - xOffset;\n  const until = width + offsetInlineEnd - xOffset;\n  const balanceLabels = axis === 'x' && scale.scaleType !== 'log';\n  const visibleTicks = getVisibleTicks(formattedTicks, from, until, balanceLabels);\n\n  return (\n    <g\n      transform={`translate(0,${height})`}\n      className={styles['labels-block-end']}\n      aria-label={title}\n      role=\"list\"\n      aria-roledescription={i18n('i18nStrings.chartAriaRoleDescription', ariaRoleDescription)}\n      aria-hidden={true}\n    >\n      {visibleTicks.map(\n        ({ position, lines }, index) =>\n          isFinite(position) && (\n            <g\n              key={index}\n              transform={`translate(${position + xOffset},0)`}\n              className={clsx(styles.ticks, styles['ticks--bottom'], {\n                [styles['ticks--x']]: axis === 'x',\n                [styles['ticks--y']]: axis === 'y',\n              })}\n              role=\"listitem\"\n              aria-label={lines.join('\\n')}\n            >\n              <line className={styles.ticks__line} x1={0} x2={0} y1={0} y2={TICK_LENGTH} aria-hidden=\"true\" />\n              {lines.map((line, lineIndex) => (\n                <text\n                  className={styles.ticks__text}\n                  key={lineIndex}\n                  x={0}\n                  y={TICK_LENGTH + TICK_MARGIN + lineIndex * TICK_LINE_HEIGHT}\n                >\n                  {line}\n                </text>\n              ))}\n            </g>\n          )\n      )}\n\n      <text ref={virtualTextRef} x={0} y={0} style={{ visibility: 'hidden' }} aria-hidden=\"true\"></text>\n    </g>\n  );\n}\n"], "mappings": "AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,IAAI,EAAEC,MAAM,QAAQ,OAAO;AAC3C,OAAOC,IAAI,MAAM,MAAM;AAEvB,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,WAAW,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,aAAa;AAExE,SAAwBC,WAAW,EAAEC,eAAe,QAAQ,eAAe;AAG3E,OAAOC,MAAM,MAAM,iBAAiB;AAgBpC,OAAM,SAAUC,iBAAiBA,CAAC;EAChCC,KAAK;EACLC,KAAK;EACLC;AAAa,CAKd;EACC,MAAMC,cAAc,GAAGb,MAAM,CAAiB,IAAI,CAAC;EAEnD,MAAMc,QAAQ,GAAGd,MAAM,CAA8B,EAAE,CAAC;EACxD,MAAMe,aAAa,GAAIC,KAAa,IAAI;IACtC,IAAIF,QAAQ,CAACG,OAAO,CAACD,KAAK,CAAC,KAAKE,SAAS,IAAIJ,QAAQ,CAACG,OAAO,CAACD,KAAK,CAAC,KAAK,CAAC,EAAE;MAC1E,OAAOF,QAAQ,CAACG,OAAO,CAACD,KAAK,CAAC;;IAEhC,IAAIH,cAAc,CAACI,OAAO,IAAIJ,cAAc,CAACI,OAAO,CAACE,qBAAqB,EAAE;MAC1EN,cAAc,CAACI,OAAO,CAACG,WAAW,GAAGJ,KAAK;MAC1CF,QAAQ,CAACG,OAAO,CAACD,KAAK,CAAC,GAAGH,cAAc,CAACI,OAAO,CAACE,qBAAqB,EAAE;MACxE,OAAOL,QAAQ,CAACG,OAAO,CAACD,KAAK,CAAC;;IAEhC,OAAO,CAAC;EACV,CAAC;EAED,MAAMK,cAAc,GAAGf,WAAW,CAAC;IAAEI,KAAK;IAAEC,KAAK;IAAEI,aAAa;IAAEH;EAAa,CAAE,CAAC;EAElF,IAAIC,cAAc,CAACI,OAAO,EAAE;IAC1BJ,cAAc,CAACI,OAAO,CAACG,WAAW,GAAG,EAAE;;EAGzC,IAAIE,MAAM,GAAGnB,WAAW,GAAGE,WAAW;EACtC,KAAK,MAAM;IAAEkB;EAAK,CAAE,IAAIF,cAAc,EAAE;IACtCC,MAAM,GAAGE,IAAI,CAACC,GAAG,CAACH,MAAM,EAAEnB,WAAW,GAAGE,WAAW,GAAGkB,KAAK,CAACG,MAAM,GAAGtB,gBAAgB,CAAC;;EAGxF,OAAO;IAAES,cAAc;IAAEQ,cAAc;IAAEC;EAAM,CAAE;AACnD;AAEA,eAAevB,IAAI,CAAC4B,cAAc,CAA0B;AAE5D;AACA,SAASA,cAAcA,CAAC;EACtBC,IAAI,GAAG,GAAG;EACVC,KAAK;EACLP,MAAM;EACNX,KAAK;EACLmB,KAAK;EACLC,mBAAmB;EACnBC,UAAU,GAAG,CAAC;EACdC,WAAW,GAAG,CAAC;EACfpB,cAAc;EACdQ,cAAc;EACda,KAAK,GAAG;AAAK,CACO;EACpB,MAAMC,IAAI,GAAGjC,eAAe,CAAC,UAAU,CAAC;EAExC,MAAMkC,OAAO,GAAGzB,KAAK,CAAC0B,aAAa,EAAE,IAAIT,IAAI,KAAK,GAAG,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEd,KAAK,CAAC2B,OAAO,CAACC,SAAS,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;EAE1G,MAAMC,iBAAiB,GAAGN,KAAK,GAAGD,WAAW,GAAGD,UAAU;EAC1D,MAAMS,eAAe,GAAGP,KAAK,GAAGF,UAAU,GAAGC,WAAW;EAExD,MAAMS,IAAI,GAAG,CAAC,GAAGF,iBAAiB,GAAGJ,OAAO;EAC5C,MAAMO,KAAK,GAAGd,KAAK,GAAGY,eAAe,GAAGL,OAAO;EAC/C,MAAMQ,aAAa,GAAGhB,IAAI,KAAK,GAAG,IAAIjB,KAAK,CAACkC,SAAS,KAAK,KAAK;EAC/D,MAAMC,YAAY,GAAGvC,eAAe,CAACc,cAAc,EAAEqB,IAAI,EAAEC,KAAK,EAAEC,aAAa,CAAC;EAEhF,OACE9C,KAAA,CAAAiD,aAAA;IACEC,SAAS,EAAE,eAAe1B,MAAM,GAAG;IACnC2B,SAAS,EAAEzC,MAAM,CAAC,kBAAkB,CAAC;IAAA,cACzBsB,KAAK;IACjBoB,IAAI,EAAC,MAAM;IAAA,wBACWf,IAAI,CAAC,sCAAsC,EAAEJ,mBAAmB,CAAC;IAAA,eAC1E;EAAI,GAEhBe,YAAY,CAACK,GAAG,CACf,CAAC;IAAEC,QAAQ;IAAE7B;EAAK,CAAE,EAAE8B,KAAK,KACzBC,QAAQ,CAACF,QAAQ,CAAC,IAChBtD,KAAA,CAAAiD,aAAA;IACEQ,GAAG,EAAEF,KAAK;IACVL,SAAS,EAAE,aAAaI,QAAQ,GAAGhB,OAAO,KAAK;IAC/Ca,SAAS,EAAEhD,IAAI,CAACO,MAAM,CAACE,KAAK,EAAEF,MAAM,CAAC,eAAe,CAAC,EAAE;MACrD,CAACA,MAAM,CAAC,UAAU,CAAC,GAAGoB,IAAI,KAAK,GAAG;MAClC,CAACpB,MAAM,CAAC,UAAU,CAAC,GAAGoB,IAAI,KAAK;KAChC,CAAC;IACFsB,IAAI,EAAC,UAAU;IAAA,cACH3B,KAAK,CAACiC,IAAI,CAAC,IAAI;EAAC,GAE5B1D,KAAA,CAAAiD,aAAA;IAAME,SAAS,EAAEzC,MAAM,CAACiD,WAAW;IAAEC,EAAE,EAAE,CAAC;IAAEC,EAAE,EAAE,CAAC;IAAEC,EAAE,EAAE,CAAC;IAAEC,EAAE,EAAE1D,WAAW;IAAA,eAAc;EAAM,EAAG,EAC/FoB,KAAK,CAAC4B,GAAG,CAAC,CAACW,IAAI,EAAEC,SAAS,KACzBjE,KAAA,CAAAiD,aAAA;IACEE,SAAS,EAAEzC,MAAM,CAACwD,WAAW;IAC7BT,GAAG,EAAEQ,SAAS;IACdE,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE/D,WAAW,GAAGE,WAAW,GAAG0D,SAAS,GAAG3D;EAAgB,GAE1D0D,IAAI,CAER,CAAC,CAEL,CACJ,EAEDhE,KAAA,CAAAiD,aAAA;IAAMoB,GAAG,EAAEtD,cAAc;IAAEoD,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEE,KAAK,EAAE;MAAEC,UAAU,EAAE;IAAQ,CAAE;IAAA,eAAc;EAAM,EAAQ,CAChG;AAER", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}