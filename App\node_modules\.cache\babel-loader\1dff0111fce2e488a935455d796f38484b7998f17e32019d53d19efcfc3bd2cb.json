{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React from 'react';\nimport clsx from 'clsx';\nimport { findUpUntil } from '@cloudscape-design/component-toolkit/dom';\nimport { getAnalyticsMetadataAttribute } from '@cloudscape-design/component-toolkit/internal/analytics-metadata';\nimport { fireNonCancelableEvent } from '../internal/events';\nimport { TableHeaderCell } from './header-cell';\nimport { focusMarkers } from './selection';\nimport { TableHeaderSelectionCell } from './selection/selection-cell';\nimport { getTableHeaderRowRoleProps } from './table-role';\nimport { useColumnWidths } from './use-column-widths';\nimport { getColumnKey } from './utils';\nimport styles from './styles.css.js';\nconst Thead = React.forwardRef((_ref, outerRef) => {\n  let {\n    selectionType,\n    getSelectAllProps,\n    columnDefinitions,\n    sortingColumn,\n    sortingDisabled,\n    sortingDescending,\n    resizableColumns,\n    variant,\n    wrapLines,\n    onFocusMove,\n    onSortingChange,\n    onResizeFinish,\n    singleSelectionHeaderAriaLabel,\n    stripedRows,\n    sticky = false,\n    hidden = false,\n    stuck = false,\n    stickyState,\n    selectionColumnId,\n    focusedComponent,\n    onFocusedComponentChange,\n    tableRole,\n    resizerRoleDescription,\n    isExpandable,\n    setLastUserAction\n  } = _ref;\n  const {\n    getColumnStyles,\n    columnWidths,\n    updateColumn,\n    setCell\n  } = useColumnWidths();\n  const commonCellProps = {\n    stuck,\n    sticky,\n    hidden,\n    stripedRows,\n    tableRole,\n    variant,\n    stickyState\n  };\n  return React.createElement(\"thead\", {\n    className: clsx(!hidden && styles['thead-active'])\n  }, React.createElement(\"tr\", Object.assign({}, focusMarkers.all, {\n    ref: outerRef,\n    \"aria-rowindex\": 1\n  }, getTableHeaderRowRoleProps({\n    tableRole\n  }), {\n    onFocus: event => {\n      var _a;\n      const focusControlElement = findUpUntil(event.target, element => !!element.getAttribute('data-focus-id'));\n      const focusId = (_a = focusControlElement === null || focusControlElement === void 0 ? void 0 : focusControlElement.getAttribute('data-focus-id')) !== null && _a !== void 0 ? _a : null;\n      onFocusedComponentChange === null || onFocusedComponentChange === void 0 ? void 0 : onFocusedComponentChange(focusId);\n    },\n    onBlur: () => onFocusedComponentChange === null || onFocusedComponentChange === void 0 ? void 0 : onFocusedComponentChange(null)\n  }), selectionType ? React.createElement(TableHeaderSelectionCell, Object.assign({}, commonCellProps, {\n    focusedComponent: focusedComponent,\n    columnId: selectionColumnId,\n    getSelectAllProps: getSelectAllProps,\n    onFocusMove: onFocusMove,\n    singleSelectionHeaderAriaLabel: singleSelectionHeaderAriaLabel\n  }, getAnalyticsMetadataAttribute({\n    action: 'selectAll'\n  }))) : null, columnDefinitions.map((column, colIndex) => {\n    const columnId = getColumnKey(column, colIndex);\n    return React.createElement(TableHeaderCell, Object.assign({}, commonCellProps, {\n      key: columnId,\n      tabIndex: sticky ? -1 : 0,\n      focusedComponent: focusedComponent,\n      column: column,\n      activeSortingColumn: sortingColumn,\n      sortingDescending: sortingDescending,\n      sortingDisabled: sortingDisabled,\n      wrapLines: wrapLines,\n      colIndex: selectionType ? colIndex + 1 : colIndex,\n      columnId: columnId,\n      updateColumn: updateColumn,\n      onResizeFinish: () => onResizeFinish(columnWidths),\n      resizableColumns: resizableColumns,\n      resizableStyle: getColumnStyles(sticky, columnId),\n      onClick: detail => {\n        setLastUserAction('sorting');\n        fireNonCancelableEvent(onSortingChange, detail);\n      },\n      isEditable: !!column.editConfig,\n      cellRef: node => setCell(sticky, columnId, node),\n      tableRole: tableRole,\n      resizerRoleDescription: resizerRoleDescription,\n      // Expandable option is only applicable to the first data column of the table.\n      // When present, the header content receives extra padding to match the first offset in the data cells.\n      isExpandable: colIndex === 0 && isExpandable,\n      hasDynamicContent: hidden && !resizableColumns && column.hasDynamicContent\n    }));\n  })));\n});\nexport default Thead;", "map": {"version": 3, "names": ["React", "clsx", "findUpUntil", "getAnalyticsMetadataAttribute", "fireNonCancelableEvent", "TableHeaderCell", "focusMarkers", "TableHeaderSelectionCell", "getTableHeaderRowRoleProps", "useColumnWidths", "getColumnKey", "styles", "<PERSON><PERSON>", "forwardRef", "_ref", "outerRef", "selectionType", "getSelectAllProps", "columnDefinitions", "sortingColumn", "sortingDisabled", "sortingDescending", "resizableColumns", "variant", "wrapLines", "onFocusMove", "onSortingChange", "onResizeFinish", "singleSelectionHeaderAriaLabel", "stripedRows", "sticky", "hidden", "stuck", "stickyState", "selectionColumnId", "focusedComponent", "onFocusedComponentChange", "tableRole", "resizerRoleDescription", "isExpandable", "setLastUserAction", "getColumnStyles", "columnWidths", "updateColumn", "setCell", "commonCellProps", "createElement", "className", "Object", "assign", "all", "ref", "onFocus", "event", "focusControlElement", "target", "element", "getAttribute", "focusId", "_a", "onBlur", "columnId", "action", "map", "column", "colIndex", "key", "tabIndex", "activeSortingColumn", "resizableStyle", "onClick", "detail", "isEditable", "editConfig", "cellRef", "node", "hasDynamicContent"], "sources": ["C:\\Repos2025\\App-main\\App\\node_modules\\src\\table\\thead.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React from 'react';\nimport clsx from 'clsx';\n\nimport { findUpUntil } from '@cloudscape-design/component-toolkit/dom';\nimport { getAnalyticsMetadataAttribute } from '@cloudscape-design/component-toolkit/internal/analytics-metadata';\n\nimport { fireNonCancelableEvent, NonCancelableEventHandler } from '../internal/events';\nimport { GeneratedAnalyticsMetadataTableSelectAll } from './analytics-metadata/interfaces';\nimport { TableHeaderCell } from './header-cell';\nimport { TableProps } from './interfaces';\nimport { focusMarkers, SelectionProps } from './selection';\nimport { TableHeaderSelectionCell } from './selection/selection-cell';\nimport { StickyColumnsModel } from './sticky-columns';\nimport { getTableHeaderRowRoleProps, TableRole } from './table-role';\nimport { useColumnWidths } from './use-column-widths';\nimport { getColumnKey } from './utils';\n\nimport styles from './styles.css.js';\n\nexport interface TheadProps {\n  selectionType: TableProps.SelectionType | undefined;\n  columnDefinitions: ReadonlyArray<TableProps.ColumnDefinition<any>>;\n  sortingColumn: TableProps.SortingColumn<any> | undefined;\n  sortingDescending: boolean | undefined;\n  sortingDisabled: boolean | undefined;\n  variant: TableProps.Variant;\n  wrapLines: boolean | undefined;\n  resizableColumns: boolean | undefined;\n  getSelectAllProps?: () => SelectionProps;\n  onFocusMove: ((sourceElement: HTMLElement, fromIndex: number, direction: -1 | 1) => void) | undefined;\n  onResizeFinish: (newWidths: Map<PropertyKey, number>) => void;\n  onSortingChange: NonCancelableEventHandler<TableProps.SortingState<any>> | undefined;\n  sticky?: boolean;\n  hidden?: boolean;\n  stuck?: boolean;\n  singleSelectionHeaderAriaLabel?: string;\n  resizerRoleDescription?: string;\n  stripedRows?: boolean;\n  stickyState: StickyColumnsModel;\n  selectionColumnId: PropertyKey;\n  focusedComponent?: null | string;\n  onFocusedComponentChange?: (focusId: null | string) => void;\n  tableRole: TableRole;\n  isExpandable?: boolean;\n  setLastUserAction: (name: string) => void;\n}\n\nconst Thead = React.forwardRef(\n  (\n    {\n      selectionType,\n      getSelectAllProps,\n      columnDefinitions,\n      sortingColumn,\n      sortingDisabled,\n      sortingDescending,\n      resizableColumns,\n      variant,\n      wrapLines,\n      onFocusMove,\n      onSortingChange,\n      onResizeFinish,\n      singleSelectionHeaderAriaLabel,\n      stripedRows,\n      sticky = false,\n      hidden = false,\n      stuck = false,\n      stickyState,\n      selectionColumnId,\n      focusedComponent,\n      onFocusedComponentChange,\n      tableRole,\n      resizerRoleDescription,\n      isExpandable,\n      setLastUserAction,\n    }: TheadProps,\n    outerRef: React.Ref<HTMLTableRowElement>\n  ) => {\n    const { getColumnStyles, columnWidths, updateColumn, setCell } = useColumnWidths();\n\n    const commonCellProps = {\n      stuck,\n      sticky,\n      hidden,\n      stripedRows,\n      tableRole,\n      variant,\n      stickyState,\n    };\n\n    return (\n      <thead className={clsx(!hidden && styles['thead-active'])}>\n        <tr\n          {...focusMarkers.all}\n          ref={outerRef}\n          aria-rowindex={1}\n          {...getTableHeaderRowRoleProps({ tableRole })}\n          onFocus={event => {\n            const focusControlElement = findUpUntil(event.target, element => !!element.getAttribute('data-focus-id'));\n            const focusId = focusControlElement?.getAttribute('data-focus-id') ?? null;\n            onFocusedComponentChange?.(focusId);\n          }}\n          onBlur={() => onFocusedComponentChange?.(null)}\n        >\n          {selectionType ? (\n            <TableHeaderSelectionCell\n              {...commonCellProps}\n              focusedComponent={focusedComponent}\n              columnId={selectionColumnId}\n              getSelectAllProps={getSelectAllProps}\n              onFocusMove={onFocusMove}\n              singleSelectionHeaderAriaLabel={singleSelectionHeaderAriaLabel}\n              {...getAnalyticsMetadataAttribute({\n                action: 'selectAll',\n              } as Partial<GeneratedAnalyticsMetadataTableSelectAll>)}\n            />\n          ) : null}\n\n          {columnDefinitions.map((column, colIndex) => {\n            const columnId = getColumnKey(column, colIndex);\n            return (\n              <TableHeaderCell\n                {...commonCellProps}\n                key={columnId}\n                tabIndex={sticky ? -1 : 0}\n                focusedComponent={focusedComponent}\n                column={column}\n                activeSortingColumn={sortingColumn}\n                sortingDescending={sortingDescending}\n                sortingDisabled={sortingDisabled}\n                wrapLines={wrapLines}\n                colIndex={selectionType ? colIndex + 1 : colIndex}\n                columnId={columnId}\n                updateColumn={updateColumn}\n                onResizeFinish={() => onResizeFinish(columnWidths)}\n                resizableColumns={resizableColumns}\n                resizableStyle={getColumnStyles(sticky, columnId)}\n                onClick={detail => {\n                  setLastUserAction('sorting');\n                  fireNonCancelableEvent(onSortingChange, detail);\n                }}\n                isEditable={!!column.editConfig}\n                cellRef={node => setCell(sticky, columnId, node)}\n                tableRole={tableRole}\n                resizerRoleDescription={resizerRoleDescription}\n                // Expandable option is only applicable to the first data column of the table.\n                // When present, the header content receives extra padding to match the first offset in the data cells.\n                isExpandable={colIndex === 0 && isExpandable}\n                hasDynamicContent={hidden && !resizableColumns && column.hasDynamicContent}\n              />\n            );\n          })}\n        </tr>\n      </thead>\n    );\n  }\n);\n\nexport default Thead;\n"], "mappings": "AAAA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,MAAM;AAEvB,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,6BAA6B,QAAQ,kEAAkE;AAEhH,SAASC,sBAAsB,QAAmC,oBAAoB;AAEtF,SAASC,eAAe,QAAQ,eAAe;AAE/C,SAASC,YAAY,QAAwB,aAAa;AAC1D,SAASC,wBAAwB,QAAQ,4BAA4B;AAErE,SAASC,0BAA0B,QAAmB,cAAc;AACpE,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,YAAY,QAAQ,SAAS;AAEtC,OAAOC,MAAM,MAAM,iBAAiB;AA8BpC,MAAMC,KAAK,GAAGZ,KAAK,CAACa,UAAU,CAC5B,CAAAC,IAAA,EA4BEC,QAAwC,KACtC;EAAA,IA5BF;IACEC,aAAa;IACbC,iBAAiB;IACjBC,iBAAiB;IACjBC,aAAa;IACbC,eAAe;IACfC,iBAAiB;IACjBC,gBAAgB;IAChBC,OAAO;IACPC,SAAS;IACTC,WAAW;IACXC,eAAe;IACfC,cAAc;IACdC,8BAA8B;IAC9BC,WAAW;IACXC,MAAM,GAAG,KAAK;IACdC,MAAM,GAAG,KAAK;IACdC,KAAK,GAAG,KAAK;IACbC,WAAW;IACXC,iBAAiB;IACjBC,gBAAgB;IAChBC,wBAAwB;IACxBC,SAAS;IACTC,sBAAsB;IACtBC,YAAY;IACZC;EAAiB,CACN,GAAA1B,IAAA;EAGb,MAAM;IAAE2B,eAAe;IAAEC,YAAY;IAAEC,YAAY;IAAEC;EAAO,CAAE,GAAGnC,eAAe,EAAE;EAElF,MAAMoC,eAAe,GAAG;IACtBb,KAAK;IACLF,MAAM;IACNC,MAAM;IACNF,WAAW;IACXQ,SAAS;IACTd,OAAO;IACPU;GACD;EAED,OACEjC,KAAA,CAAA8C,aAAA;IAAOC,SAAS,EAAE9C,IAAI,CAAC,CAAC8B,MAAM,IAAIpB,MAAM,CAAC,cAAc,CAAC;EAAC,GACvDX,KAAA,CAAA8C,aAAA,OAAAE,MAAA,CAAAC,MAAA,KACM3C,YAAY,CAAC4C,GAAG;IACpBC,GAAG,EAAEpC,QAAQ;IAAA,iBACE;EAAC,GACZP,0BAA0B,CAAC;IAAE6B;EAAS,CAAE,CAAC;IAC7Ce,OAAO,EAAEC,KAAK,IAAG;;MACf,MAAMC,mBAAmB,GAAGpD,WAAW,CAACmD,KAAK,CAACE,MAAM,EAAEC,OAAO,IAAI,CAAC,CAACA,OAAO,CAACC,YAAY,CAAC,eAAe,CAAC,CAAC;MACzG,MAAMC,OAAO,GAAG,CAAAC,EAAA,GAAAL,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEG,YAAY,CAAC,eAAe,CAAC,cAAAE,EAAA,cAAAA,EAAA,GAAI,IAAI;MAC1EvB,wBAAwB,aAAxBA,wBAAwB,uBAAxBA,wBAAwB,CAAGsB,OAAO,CAAC;IACrC,CAAC;IACDE,MAAM,EAAEA,CAAA,KAAMxB,wBAAwB,aAAxBA,wBAAwB,uBAAxBA,wBAAwB,CAAG,IAAI;EAAC,IAE7CpB,aAAa,GACZhB,KAAA,CAAA8C,aAAA,CAACvC,wBAAwB,EAAAyC,MAAA,CAAAC,MAAA,KACnBJ,eAAe;IACnBV,gBAAgB,EAAEA,gBAAgB;IAClC0B,QAAQ,EAAE3B,iBAAiB;IAC3BjB,iBAAiB,EAAEA,iBAAiB;IACpCQ,WAAW,EAAEA,WAAW;IACxBG,8BAA8B,EAAEA;EAA8B,GAC1DzB,6BAA6B,CAAC;IAChC2D,MAAM,EAAE;GAC4C,CAAC,EACvD,GACA,IAAI,EAEP5C,iBAAiB,CAAC6C,GAAG,CAAC,CAACC,MAAM,EAAEC,QAAQ,KAAI;IAC1C,MAAMJ,QAAQ,GAAGnD,YAAY,CAACsD,MAAM,EAAEC,QAAQ,CAAC;IAC/C,OACEjE,KAAA,CAAA8C,aAAA,CAACzC,eAAe,EAAA2C,MAAA,CAAAC,MAAA,KACVJ,eAAe;MACnBqB,GAAG,EAAEL,QAAQ;MACbM,QAAQ,EAAErC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;MACzBK,gBAAgB,EAAEA,gBAAgB;MAClC6B,MAAM,EAAEA,MAAM;MACdI,mBAAmB,EAAEjD,aAAa;MAClCE,iBAAiB,EAAEA,iBAAiB;MACpCD,eAAe,EAAEA,eAAe;MAChCI,SAAS,EAAEA,SAAS;MACpByC,QAAQ,EAAEjD,aAAa,GAAGiD,QAAQ,GAAG,CAAC,GAAGA,QAAQ;MACjDJ,QAAQ,EAAEA,QAAQ;MAClBlB,YAAY,EAAEA,YAAY;MAC1BhB,cAAc,EAAEA,CAAA,KAAMA,cAAc,CAACe,YAAY,CAAC;MAClDpB,gBAAgB,EAAEA,gBAAgB;MAClC+C,cAAc,EAAE5B,eAAe,CAACX,MAAM,EAAE+B,QAAQ,CAAC;MACjDS,OAAO,EAAEC,MAAM,IAAG;QAChB/B,iBAAiB,CAAC,SAAS,CAAC;QAC5BpC,sBAAsB,CAACsB,eAAe,EAAE6C,MAAM,CAAC;MACjD,CAAC;MACDC,UAAU,EAAE,CAAC,CAACR,MAAM,CAACS,UAAU;MAC/BC,OAAO,EAAEC,IAAI,IAAI/B,OAAO,CAACd,MAAM,EAAE+B,QAAQ,EAAEc,IAAI,CAAC;MAChDtC,SAAS,EAAEA,SAAS;MACpBC,sBAAsB,EAAEA,sBAAsB;MAC9C;MACA;MACAC,YAAY,EAAE0B,QAAQ,KAAK,CAAC,IAAI1B,YAAY;MAC5CqC,iBAAiB,EAAE7C,MAAM,IAAI,CAACT,gBAAgB,IAAI0C,MAAM,CAACY;IAAiB,GAC1E;EAEN,CAAC,CAAC,CACC,CACC;AAEZ,CAAC,CACF;AAED,eAAehE,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}