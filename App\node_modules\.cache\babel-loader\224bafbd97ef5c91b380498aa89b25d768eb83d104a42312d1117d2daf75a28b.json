{"ast": null, "code": "import { __rest } from \"tslib\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useRef } from 'react';\nimport clsx from 'clsx';\nimport { copyAnalyticsMetadataAttribute } from '@cloudscape-design/component-toolkit/internal/analytics-metadata';\nimport { useSingleTabStopNavigation } from '../../internal/context/single-tab-stop-navigation-context';\nimport { useMergeRefs } from '../../internal/hooks/use-merge-refs';\nimport { useVisualRefresh } from '../../internal/hooks/use-visual-mode';\nimport { ExpandToggleButton } from '../expandable-rows/expand-toggle-button';\nimport { useStickyCellStyles } from '../sticky-columns';\nimport { getTableCellRoleProps } from '../table-role';\nimport { getStickyClassNames } from '../utils';\nimport tableStyles from '../styles.css.js';\nimport styles from './styles.css.js';\nexport const TableTdElement = React.forwardRef((_a, ref) => {\n  var {\n      children,\n      wrapLines,\n      isRowHeader,\n      isFirstRow,\n      isLastRow,\n      isSelected,\n      isNextSelected,\n      isPrevSelected,\n      nativeAttributes,\n      onClick,\n      onFocus,\n      onBlur,\n      isEvenRow,\n      stripedRows,\n      isSelection,\n      hasSelection,\n      hasFooter,\n      columnId,\n      colIndex,\n      stickyState,\n      tableRole,\n      level,\n      isExpandable,\n      isExpanded,\n      onExpandableItemToggle,\n      expandButtonLabel,\n      collapseButtonLabel,\n      verticalAlign,\n      resizableColumns,\n      resizableStyle,\n      isEditable,\n      isEditing,\n      isEditingDisabled,\n      hasSuccessIcon\n    } = _a,\n    rest = __rest(_a, [\"children\", \"wrapLines\", \"isRowHeader\", \"isFirstRow\", \"isLastRow\", \"isSelected\", \"isNextSelected\", \"isPrevSelected\", \"nativeAttributes\", \"onClick\", \"onFocus\", \"onBlur\", \"isEvenRow\", \"stripedRows\", \"isSelection\", \"hasSelection\", \"hasFooter\", \"columnId\", \"colIndex\", \"stickyState\", \"tableRole\", \"level\", \"isExpandable\", \"isExpanded\", \"onExpandableItemToggle\", \"expandButtonLabel\", \"collapseButtonLabel\", \"verticalAlign\", \"resizableColumns\", \"resizableStyle\", \"isEditable\", \"isEditing\", \"isEditingDisabled\", \"hasSuccessIcon\"]);\n  const Element = isRowHeader ? 'th' : 'td';\n  const isVisualRefresh = useVisualRefresh();\n  resizableStyle = resizableColumns ? {} : resizableStyle;\n  nativeAttributes = Object.assign(Object.assign({}, nativeAttributes), getTableCellRoleProps({\n    tableRole,\n    isRowHeader,\n    colIndex\n  }));\n  const stickyStyles = useStickyCellStyles({\n    stickyColumns: stickyState,\n    columnId,\n    getClassName: props => getStickyClassNames(styles, props)\n  });\n  const cellRefObject = useRef(null);\n  const mergedRef = useMergeRefs(stickyStyles.ref, ref, cellRefObject);\n  const {\n    tabIndex: cellTabIndex\n  } = useSingleTabStopNavigation(cellRefObject);\n  const isEditingActive = isEditing && !isEditingDisabled;\n  return React.createElement(Element, Object.assign({\n    style: Object.assign(Object.assign({}, resizableStyle), stickyStyles.style),\n    className: clsx(styles['body-cell'], isFirstRow && styles['body-cell-first-row'], isLastRow && styles['body-cell-last-row'], isSelected && styles['body-cell-selected'], isNextSelected && styles['body-cell-next-selected'], isPrevSelected && styles['body-cell-prev-selected'], !isEvenRow && stripedRows && styles['body-cell-shaded'], stripedRows && styles['has-striped-rows'], isVisualRefresh && styles['is-visual-refresh'], isSelection && tableStyles['selection-control'], hasSelection && styles['has-selection'], hasFooter && styles['has-footer'], resizableColumns && styles['resizable-columns'], verticalAlign === 'top' && styles['body-cell-align-top'], isEditable && styles['body-cell-editable'], isEditing && !isEditingDisabled && styles['body-cell-edit-active'], isEditing && isEditingDisabled && styles['body-cell-edit-disabled-popover'], hasSuccessIcon && styles['body-cell-has-success'], level !== undefined && !isEditingActive && styles['body-cell-expandable'], level !== undefined && !isEditingActive && styles[`expandable-level-${getLevelClassSuffix(level)}`], stickyStyles.className),\n    onClick: onClick,\n    onFocus: onFocus,\n    onBlur: onBlur,\n    ref: mergedRef\n  }, nativeAttributes, {\n    tabIndex: cellTabIndex === -1 ? undefined : cellTabIndex\n  }, copyAnalyticsMetadataAttribute(rest)), level !== undefined && isExpandable && !isEditingActive && React.createElement(\"div\", {\n    className: styles['expandable-toggle-wrapper']\n  }, React.createElement(ExpandToggleButton, {\n    isExpanded: isExpanded,\n    onExpandableItemToggle: onExpandableItemToggle,\n    expandButtonLabel: expandButtonLabel,\n    collapseButtonLabel: collapseButtonLabel\n  })), React.createElement(\"div\", {\n    className: clsx(styles['body-cell-content'], wrapLines && styles['body-cell-wrap'])\n  }, children));\n});\nfunction getLevelClassSuffix(level) {\n  return 0 <= level && level <= 9 ? level : 'next';\n}", "map": {"version": 3, "names": ["React", "useRef", "clsx", "copyAnalyticsMetadataAttribute", "useSingleTabStopNavigation", "useMergeRefs", "useVisualRefresh", "ExpandToggleButton", "useStickyCellStyles", "getTableCellRoleProps", "getStickyClassNames", "tableStyles", "styles", "TableTdElement", "forwardRef", "_a", "ref", "children", "wrapLines", "isRowHeader", "isFirstRow", "isLastRow", "isSelected", "isNextSelected", "isPrevSelected", "nativeAttributes", "onClick", "onFocus", "onBlur", "isEvenRow", "stripedRows", "isSelection", "hasSelection", "<PERSON><PERSON><PERSON>er", "columnId", "colIndex", "stickyState", "tableRole", "level", "isExpandable", "isExpanded", "onExpandableItemToggle", "expandButtonLabel", "collapseButtonLabel", "verticalAlign", "resizableColumns", "resizableStyle", "isEditable", "isEditing", "isEditingDisabled", "hasSuccessIcon", "rest", "__rest", "Element", "isVisualRefresh", "Object", "assign", "stickyStyles", "stickyColumns", "getClassName", "props", "cellRefObject", "mergedRef", "tabIndex", "cellTabIndex", "isEditingActive", "createElement", "style", "className", "undefined", "getLevelClassSuffix"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\table\\body-cell\\td-element.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useRef } from 'react';\nimport clsx from 'clsx';\n\nimport { copyAnalyticsMetadataAttribute } from '@cloudscape-design/component-toolkit/internal/analytics-metadata';\n\nimport { useSingleTabStopNavigation } from '../../internal/context/single-tab-stop-navigation-context';\nimport { useMergeRefs } from '../../internal/hooks/use-merge-refs';\nimport { useVisualRefresh } from '../../internal/hooks/use-visual-mode';\nimport { ColumnWidthStyle } from '../column-widths-utils';\nimport { ExpandToggleButton } from '../expandable-rows/expand-toggle-button';\nimport { TableProps } from '../interfaces.js';\nimport { StickyColumnsModel, useStickyCellStyles } from '../sticky-columns';\nimport { getTableCellRoleProps, TableRole } from '../table-role';\nimport { getStickyClassNames } from '../utils';\n\nimport tableStyles from '../styles.css.js';\nimport styles from './styles.css.js';\n\nexport interface TableTdElementProps {\n  wrapLines: boolean | undefined;\n  isRowHeader?: boolean;\n  isFirstRow: boolean;\n  isLastRow: boolean;\n  isSelected: boolean;\n  isNextSelected: boolean;\n  isPrevSelected: boolean;\n  nativeAttributes?: Omit<\n    React.TdHTMLAttributes<HTMLTableCellElement> | React.ThHTMLAttributes<HTMLTableCellElement>,\n    'style' | 'className' | 'onClick'\n  >;\n  onClick?: () => void;\n  onFocus?: () => void;\n  onBlur?: () => void;\n  children?: React.ReactNode;\n  isEvenRow?: boolean;\n  stripedRows?: boolean;\n  isSelection?: boolean;\n  hasSelection?: boolean;\n  hasFooter?: boolean;\n  columnId: PropertyKey;\n  colIndex: number;\n  stickyState: StickyColumnsModel;\n  tableRole: TableRole;\n  level?: number;\n  isExpandable?: boolean;\n  isExpanded?: boolean;\n  onExpandableItemToggle?: () => void;\n  expandButtonLabel?: string;\n  collapseButtonLabel?: string;\n  verticalAlign?: TableProps.VerticalAlign;\n  resizableColumns?: boolean;\n  resizableStyle?: ColumnWidthStyle;\n  isEditable: boolean;\n  isEditing: boolean;\n  isEditingDisabled?: boolean;\n  hasSuccessIcon?: boolean;\n}\n\nexport const TableTdElement = React.forwardRef<HTMLTableCellElement, TableTdElementProps>(\n  (\n    {\n      children,\n      wrapLines,\n      isRowHeader,\n      isFirstRow,\n      isLastRow,\n      isSelected,\n      isNextSelected,\n      isPrevSelected,\n      nativeAttributes,\n      onClick,\n      onFocus,\n      onBlur,\n      isEvenRow,\n      stripedRows,\n      isSelection,\n      hasSelection,\n      hasFooter,\n      columnId,\n      colIndex,\n      stickyState,\n      tableRole,\n      level,\n      isExpandable,\n      isExpanded,\n      onExpandableItemToggle,\n      expandButtonLabel,\n      collapseButtonLabel,\n      verticalAlign,\n      resizableColumns,\n      resizableStyle,\n      isEditable,\n      isEditing,\n      isEditingDisabled,\n      hasSuccessIcon,\n      ...rest\n    },\n    ref\n  ) => {\n    const Element = isRowHeader ? 'th' : 'td';\n    const isVisualRefresh = useVisualRefresh();\n\n    resizableStyle = resizableColumns ? {} : resizableStyle;\n\n    nativeAttributes = { ...nativeAttributes, ...getTableCellRoleProps({ tableRole, isRowHeader, colIndex }) };\n\n    const stickyStyles = useStickyCellStyles({\n      stickyColumns: stickyState,\n      columnId,\n      getClassName: props => getStickyClassNames(styles, props),\n    });\n\n    const cellRefObject = useRef<HTMLTableCellElement>(null);\n    const mergedRef = useMergeRefs(stickyStyles.ref, ref, cellRefObject);\n    const { tabIndex: cellTabIndex } = useSingleTabStopNavigation(cellRefObject);\n    const isEditingActive = isEditing && !isEditingDisabled;\n\n    return (\n      <Element\n        style={{ ...resizableStyle, ...stickyStyles.style }}\n        className={clsx(\n          styles['body-cell'],\n          isFirstRow && styles['body-cell-first-row'],\n          isLastRow && styles['body-cell-last-row'],\n          isSelected && styles['body-cell-selected'],\n          isNextSelected && styles['body-cell-next-selected'],\n          isPrevSelected && styles['body-cell-prev-selected'],\n          !isEvenRow && stripedRows && styles['body-cell-shaded'],\n          stripedRows && styles['has-striped-rows'],\n          isVisualRefresh && styles['is-visual-refresh'],\n          isSelection && tableStyles['selection-control'],\n          hasSelection && styles['has-selection'],\n          hasFooter && styles['has-footer'],\n          resizableColumns && styles['resizable-columns'],\n          verticalAlign === 'top' && styles['body-cell-align-top'],\n          isEditable && styles['body-cell-editable'],\n          isEditing && !isEditingDisabled && styles['body-cell-edit-active'],\n          isEditing && isEditingDisabled && styles['body-cell-edit-disabled-popover'],\n          hasSuccessIcon && styles['body-cell-has-success'],\n          level !== undefined && !isEditingActive && styles['body-cell-expandable'],\n          level !== undefined && !isEditingActive && styles[`expandable-level-${getLevelClassSuffix(level)}`],\n          stickyStyles.className\n        )}\n        onClick={onClick}\n        onFocus={onFocus}\n        onBlur={onBlur}\n        ref={mergedRef}\n        {...nativeAttributes}\n        tabIndex={cellTabIndex === -1 ? undefined : cellTabIndex}\n        {...copyAnalyticsMetadataAttribute(rest)}\n      >\n        {level !== undefined && isExpandable && !isEditingActive && (\n          <div className={styles['expandable-toggle-wrapper']}>\n            <ExpandToggleButton\n              isExpanded={isExpanded}\n              onExpandableItemToggle={onExpandableItemToggle}\n              expandButtonLabel={expandButtonLabel}\n              collapseButtonLabel={collapseButtonLabel}\n            />\n          </div>\n        )}\n\n        <div className={clsx(styles['body-cell-content'], wrapLines && styles['body-cell-wrap'])}>{children}</div>\n      </Element>\n    );\n  }\n);\n\nfunction getLevelClassSuffix(level: number) {\n  return 0 <= level && level <= 9 ? level : 'next';\n}\n"], "mappings": ";AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,OAAOC,IAAI,MAAM,MAAM;AAEvB,SAASC,8BAA8B,QAAQ,kEAAkE;AAEjH,SAASC,0BAA0B,QAAQ,2DAA2D;AACtG,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,gBAAgB,QAAQ,sCAAsC;AAEvE,SAASC,kBAAkB,QAAQ,yCAAyC;AAE5E,SAA6BC,mBAAmB,QAAQ,mBAAmB;AAC3E,SAASC,qBAAqB,QAAmB,eAAe;AAChE,SAASC,mBAAmB,QAAQ,UAAU;AAE9C,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,MAAM,MAAM,iBAAiB;AA0CpC,OAAO,MAAMC,cAAc,GAAGb,KAAK,CAACc,UAAU,CAC5C,CACEC,EAoCC,EACDC,GAAG,KACD;MAtCF;MACEC,QAAQ;MACRC,SAAS;MACTC,WAAW;MACXC,UAAU;MACVC,SAAS;MACTC,UAAU;MACVC,cAAc;MACdC,cAAc;MACdC,gBAAgB;MAChBC,OAAO;MACPC,OAAO;MACPC,MAAM;MACNC,SAAS;MACTC,WAAW;MACXC,WAAW;MACXC,YAAY;MACZC,SAAS;MACTC,QAAQ;MACRC,QAAQ;MACRC,WAAW;MACXC,SAAS;MACTC,KAAK;MACLC,YAAY;MACZC,UAAU;MACVC,sBAAsB;MACtBC,iBAAiB;MACjBC,mBAAmB;MACnBC,aAAa;MACbC,gBAAgB;MAChBC,cAAc;MACdC,UAAU;MACVC,SAAS;MACTC,iBAAiB;MACjBC;IAAc,IAAAnC,EAEf;IADIoC,IAAI,GAAAC,MAAA,CAAArC,EAAA,EAnCT,2gBAoCC,CADQ;EAIT,MAAMsC,OAAO,GAAGlC,WAAW,GAAG,IAAI,GAAG,IAAI;EACzC,MAAMmC,eAAe,GAAGhD,gBAAgB,EAAE;EAE1CwC,cAAc,GAAGD,gBAAgB,GAAG,EAAE,GAAGC,cAAc;EAEvDrB,gBAAgB,GAAA8B,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQ/B,gBAAgB,GAAKhB,qBAAqB,CAAC;IAAE4B,SAAS;IAAElB,WAAW;IAAEgB;EAAQ,CAAE,CAAC,CAAE;EAE1G,MAAMsB,YAAY,GAAGjD,mBAAmB,CAAC;IACvCkD,aAAa,EAAEtB,WAAW;IAC1BF,QAAQ;IACRyB,YAAY,EAAEC,KAAK,IAAIlD,mBAAmB,CAACE,MAAM,EAAEgD,KAAK;GACzD,CAAC;EAEF,MAAMC,aAAa,GAAG5D,MAAM,CAAuB,IAAI,CAAC;EACxD,MAAM6D,SAAS,GAAGzD,YAAY,CAACoD,YAAY,CAACzC,GAAG,EAAEA,GAAG,EAAE6C,aAAa,CAAC;EACpE,MAAM;IAAEE,QAAQ,EAAEC;EAAY,CAAE,GAAG5D,0BAA0B,CAACyD,aAAa,CAAC;EAC5E,MAAMI,eAAe,GAAGjB,SAAS,IAAI,CAACC,iBAAiB;EAEvD,OACEjD,KAAA,CAAAkE,aAAA,CAACb,OAAO,EAAAE,MAAA,CAAAC,MAAA;IACNW,KAAK,EAAAZ,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAOV,cAAc,GAAKW,YAAY,CAACU,KAAK;IACjDC,SAAS,EAAElE,IAAI,CACbU,MAAM,CAAC,WAAW,CAAC,EACnBQ,UAAU,IAAIR,MAAM,CAAC,qBAAqB,CAAC,EAC3CS,SAAS,IAAIT,MAAM,CAAC,oBAAoB,CAAC,EACzCU,UAAU,IAAIV,MAAM,CAAC,oBAAoB,CAAC,EAC1CW,cAAc,IAAIX,MAAM,CAAC,yBAAyB,CAAC,EACnDY,cAAc,IAAIZ,MAAM,CAAC,yBAAyB,CAAC,EACnD,CAACiB,SAAS,IAAIC,WAAW,IAAIlB,MAAM,CAAC,kBAAkB,CAAC,EACvDkB,WAAW,IAAIlB,MAAM,CAAC,kBAAkB,CAAC,EACzC0C,eAAe,IAAI1C,MAAM,CAAC,mBAAmB,CAAC,EAC9CmB,WAAW,IAAIpB,WAAW,CAAC,mBAAmB,CAAC,EAC/CqB,YAAY,IAAIpB,MAAM,CAAC,eAAe,CAAC,EACvCqB,SAAS,IAAIrB,MAAM,CAAC,YAAY,CAAC,EACjCiC,gBAAgB,IAAIjC,MAAM,CAAC,mBAAmB,CAAC,EAC/CgC,aAAa,KAAK,KAAK,IAAIhC,MAAM,CAAC,qBAAqB,CAAC,EACxDmC,UAAU,IAAInC,MAAM,CAAC,oBAAoB,CAAC,EAC1CoC,SAAS,IAAI,CAACC,iBAAiB,IAAIrC,MAAM,CAAC,uBAAuB,CAAC,EAClEoC,SAAS,IAAIC,iBAAiB,IAAIrC,MAAM,CAAC,iCAAiC,CAAC,EAC3EsC,cAAc,IAAItC,MAAM,CAAC,uBAAuB,CAAC,EACjD0B,KAAK,KAAK+B,SAAS,IAAI,CAACJ,eAAe,IAAIrD,MAAM,CAAC,sBAAsB,CAAC,EACzE0B,KAAK,KAAK+B,SAAS,IAAI,CAACJ,eAAe,IAAIrD,MAAM,CAAC,oBAAoB0D,mBAAmB,CAAChC,KAAK,CAAC,EAAE,CAAC,EACnGmB,YAAY,CAACW,SAAS,CACvB;IACD1C,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA,MAAM;IACdZ,GAAG,EAAE8C;EAAS,GACVrC,gBAAgB;IACpBsC,QAAQ,EAAEC,YAAY,KAAK,CAAC,CAAC,GAAGK,SAAS,GAAGL;EAAY,GACpD7D,8BAA8B,CAACgD,IAAI,CAAC,GAEvCb,KAAK,KAAK+B,SAAS,IAAI9B,YAAY,IAAI,CAAC0B,eAAe,IACtDjE,KAAA,CAAAkE,aAAA;IAAKE,SAAS,EAAExD,MAAM,CAAC,2BAA2B;EAAC,GACjDZ,KAAA,CAAAkE,aAAA,CAAC3D,kBAAkB;IACjBiC,UAAU,EAAEA,UAAU;IACtBC,sBAAsB,EAAEA,sBAAsB;IAC9CC,iBAAiB,EAAEA,iBAAiB;IACpCC,mBAAmB,EAAEA;EAAmB,EACxC,CAEL,EAED3C,KAAA,CAAAkE,aAAA;IAAKE,SAAS,EAAElE,IAAI,CAACU,MAAM,CAAC,mBAAmB,CAAC,EAAEM,SAAS,IAAIN,MAAM,CAAC,gBAAgB,CAAC;EAAC,GAAGK,QAAQ,CAAO,CAClG;AAEd,CAAC,CACF;AAED,SAASqD,mBAAmBA,CAAChC,KAAa;EACxC,OAAO,CAAC,IAAIA,KAAK,IAAIA,KAAK,IAAI,CAAC,GAAGA,KAAK,GAAG,MAAM;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}