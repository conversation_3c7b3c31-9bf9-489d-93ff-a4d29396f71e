{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport { findUpUntil } from '@cloudscape-design/component-toolkit/dom';\nexport const getOverflowParents = element => {\n  const parents = [];\n  let node = element;\n  while ((node = node.parentElement) && node !== element.ownerDocument.body) {\n    getComputedStyle(node).overflow !== 'visible' && parents.push(node);\n  }\n  return parents;\n};\nexport const getOverflowParentDimensions = ({\n  element,\n  excludeClosestParent = false,\n  expandToViewport = false,\n  canExpandOutsideViewport = false\n}) => {\n  var _a, _b, _c, _d, _e;\n  const parents = expandToViewport ? [] : getOverflowParents(element).map(el => {\n    const {\n      height,\n      width,\n      top,\n      left\n    } = el.getBoundingClientRect();\n    return {\n      // Treat the whole scrollable area as the available height\n      // if we're allowed to expand past the viewport.\n      blockSize: canExpandOutsideViewport ? el.scrollHeight : height,\n      inlineSize: width,\n      insetBlockStart: top,\n      insetInlineStart: left\n    };\n  });\n  if (canExpandOutsideViewport && !expandToViewport) {\n    const document = element.ownerDocument;\n    const documentDimensions = document.documentElement.getBoundingClientRect();\n    parents.push({\n      inlineSize: Math.max(documentDimensions.width, document.documentElement.clientWidth),\n      blockSize: Math.max(documentDimensions.height, document.documentElement.clientHeight),\n      insetBlockStart: documentDimensions.top,\n      insetInlineStart: documentDimensions.left\n    });\n  } else {\n    const owningWindow = (_a = element.ownerDocument.defaultView) !== null && _a !== void 0 ? _a : window;\n    parents.push({\n      blockSize: (_c = (_b = owningWindow.visualViewport) === null || _b === void 0 ? void 0 : _b.height) !== null && _c !== void 0 ? _c : owningWindow.innerHeight,\n      inlineSize: (_e = (_d = owningWindow.visualViewport) === null || _d === void 0 ? void 0 : _d.width) !== null && _e !== void 0 ? _e : owningWindow.innerWidth,\n      insetBlockStart: 0,\n      insetInlineStart: 0\n    });\n  }\n  if (excludeClosestParent && !expandToViewport) {\n    parents.shift();\n  }\n  return parents;\n};\n/**\n * Calls `scrollIntoView` on the provided element with sensible defaults. If\n * the element does not exist or does not support the `scrollIntoView`\n * method, it will do nothing. This wrapper is created to support environments\n * where the native function is not available like JSDom (feature request:\n * https://github.com/jsdom/jsdom/issues/1422).\n *\n * @param element to be scrolled into view\n * @param options native options for `scrollIntoView`\n */\nexport function scrollElementIntoView(element, options = {\n  block: 'nearest',\n  inline: 'nearest'\n}) {\n  var _a;\n  (_a = element === null || element === void 0 ? void 0 : element.scrollIntoView) === null || _a === void 0 ? void 0 : _a.call(element, options);\n}\nexport function calculateScroll({\n  insetBlockStart,\n  blockSize\n}) {\n  if (insetBlockStart < 0) {\n    return insetBlockStart;\n  } else if (insetBlockStart + blockSize > window.innerHeight) {\n    if (blockSize > window.innerHeight) {\n      return insetBlockStart;\n    } else {\n      return insetBlockStart + blockSize - window.innerHeight;\n    }\n  }\n  return 0;\n}\n/**\n * For elements with fixed position, the browser's native scrollIntoView API doesn't work,\n * so we need to manually scroll to the element's position.\n * Supports only vertical scrolling.\n */\nexport function scrollRectangleIntoView(box, scrollableParent) {\n  const scrollAmount = calculateScroll(box);\n  if (scrollAmount) {\n    (scrollableParent || window).scrollBy(0, scrollAmount);\n  }\n}\nexport function getFirstScrollableParent(element) {\n  return findUpUntil(element, el => {\n    const overflows = el.scrollHeight > el.clientHeight;\n    return overflows && ['scroll', 'auto'].includes(getComputedStyle(el).overflowY);\n  }) || undefined;\n}", "map": {"version": 3, "names": ["findUpUntil", "getOverflowParents", "element", "parents", "node", "parentElement", "ownerDocument", "body", "getComputedStyle", "overflow", "push", "getOverflowParentDimensions", "excludeClosestParent", "expandToViewport", "canExpandOutsideViewport", "map", "el", "height", "width", "top", "left", "getBoundingClientRect", "blockSize", "scrollHeight", "inlineSize", "insetBlockStart", "insetInlineStart", "document", "documentDimensions", "documentElement", "Math", "max", "clientWidth", "clientHeight", "owningWindow", "_a", "defaultView", "window", "_c", "_b", "visualViewport", "innerHeight", "_e", "_d", "innerWidth", "shift", "scrollElementIntoView", "options", "block", "inline", "scrollIntoView", "call", "calculateScroll", "scrollRectangleIntoView", "box", "scrollableParent", "scrollAmount", "scrollBy", "getFirstScrollableParent", "overflows", "includes", "overflowY", "undefined"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\internal\\utils\\scrollable-containers.ts"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { findUpUntil } from '@cloudscape-design/component-toolkit/dom';\n\nexport interface BoundingBox {\n  blockSize: number;\n  inlineSize: number;\n  insetBlockStart: number;\n  insetInlineStart: number;\n}\n\nexport const getOverflowParents = (element: HTMLElement): HTMLElement[] => {\n  const parents = [];\n  let node: HTMLElement | null = element;\n\n  while ((node = node.parentElement) && node !== element.ownerDocument.body) {\n    getComputedStyle(node).overflow !== 'visible' && parents.push(node);\n  }\n  return parents;\n};\n\nexport const getOverflowParentDimensions = ({\n  element,\n  excludeClosestParent = false,\n  expandToViewport = false,\n  canExpandOutsideViewport = false,\n}: {\n  element: HTMLElement;\n  excludeClosestParent: boolean;\n  expandToViewport: boolean;\n  canExpandOutsideViewport: boolean;\n}): BoundingBox[] => {\n  const parents = expandToViewport\n    ? []\n    : getOverflowParents(element).map(el => {\n        const { height, width, top, left } = el.getBoundingClientRect();\n        return {\n          // Treat the whole scrollable area as the available height\n          // if we're allowed to expand past the viewport.\n          blockSize: canExpandOutsideViewport ? el.scrollHeight : height,\n          inlineSize: width,\n          insetBlockStart: top,\n          insetInlineStart: left,\n        };\n      });\n\n  if (canExpandOutsideViewport && !expandToViewport) {\n    const document = element.ownerDocument;\n    const documentDimensions = document.documentElement.getBoundingClientRect();\n    parents.push({\n      inlineSize: Math.max(documentDimensions.width, document.documentElement.clientWidth),\n      blockSize: Math.max(documentDimensions.height, document.documentElement.clientHeight),\n      insetBlockStart: documentDimensions.top,\n      insetInlineStart: documentDimensions.left,\n    });\n  } else {\n    const owningWindow = element.ownerDocument.defaultView ?? window;\n    parents.push({\n      blockSize: owningWindow.visualViewport?.height ?? owningWindow.innerHeight,\n      inlineSize: owningWindow.visualViewport?.width ?? owningWindow.innerWidth,\n      insetBlockStart: 0,\n      insetInlineStart: 0,\n    });\n  }\n\n  if (excludeClosestParent && !expandToViewport) {\n    parents.shift();\n  }\n\n  return parents;\n};\n\ntype ScrollIntoViewOptions = Parameters<HTMLElement['scrollIntoView']>[0];\n\n/**\n * Calls `scrollIntoView` on the provided element with sensible defaults. If\n * the element does not exist or does not support the `scrollIntoView`\n * method, it will do nothing. This wrapper is created to support environments\n * where the native function is not available like JSDom (feature request:\n * https://github.com/jsdom/jsdom/issues/1422).\n *\n * @param element to be scrolled into view\n * @param options native options for `scrollIntoView`\n */\nexport function scrollElementIntoView(\n  element: HTMLElement | undefined,\n  options: ScrollIntoViewOptions = { block: 'nearest', inline: 'nearest' }\n) {\n  element?.scrollIntoView?.(options);\n}\n\nexport function calculateScroll({ insetBlockStart, blockSize }: BoundingBox) {\n  if (insetBlockStart < 0) {\n    return insetBlockStart;\n  } else if (insetBlockStart + blockSize > window.innerHeight) {\n    if (blockSize > window.innerHeight) {\n      return insetBlockStart;\n    } else {\n      return insetBlockStart + blockSize - window.innerHeight;\n    }\n  }\n  return 0;\n}\n\n/**\n * For elements with fixed position, the browser's native scrollIntoView API doesn't work,\n * so we need to manually scroll to the element's position.\n * Supports only vertical scrolling.\n */\nexport function scrollRectangleIntoView(box: BoundingBox, scrollableParent?: HTMLElement) {\n  const scrollAmount = calculateScroll(box);\n  if (scrollAmount) {\n    (scrollableParent || window).scrollBy(0, scrollAmount);\n  }\n}\n\nexport function getFirstScrollableParent(element: HTMLElement): HTMLElement | undefined {\n  return (\n    findUpUntil(element, el => {\n      const overflows = el.scrollHeight > el.clientHeight;\n      return overflows && ['scroll', 'auto'].includes(getComputedStyle(el).overflowY);\n    }) || undefined\n  );\n}\n"], "mappings": "AAAA;AACA;AAEA,SAASA,WAAW,QAAQ,0CAA0C;AAStE,OAAO,MAAMC,kBAAkB,GAAIC,OAAoB,IAAmB;EACxE,MAAMC,OAAO,GAAG,EAAE;EAClB,IAAIC,IAAI,GAAuBF,OAAO;EAEtC,OAAO,CAACE,IAAI,GAAGA,IAAI,CAACC,aAAa,KAAKD,IAAI,KAAKF,OAAO,CAACI,aAAa,CAACC,IAAI,EAAE;IACzEC,gBAAgB,CAACJ,IAAI,CAAC,CAACK,QAAQ,KAAK,SAAS,IAAIN,OAAO,CAACO,IAAI,CAACN,IAAI,CAAC;;EAErE,OAAOD,OAAO;AAChB,CAAC;AAED,OAAO,MAAMQ,2BAA2B,GAAGA,CAAC;EAC1CT,OAAO;EACPU,oBAAoB,GAAG,KAAK;EAC5BC,gBAAgB,GAAG,KAAK;EACxBC,wBAAwB,GAAG;AAAK,CAMjC,KAAmB;;EAClB,MAAMX,OAAO,GAAGU,gBAAgB,GAC5B,EAAE,GACFZ,kBAAkB,CAACC,OAAO,CAAC,CAACa,GAAG,CAACC,EAAE,IAAG;IACnC,MAAM;MAAEC,MAAM;MAAEC,KAAK;MAAEC,GAAG;MAAEC;IAAI,CAAE,GAAGJ,EAAE,CAACK,qBAAqB,EAAE;IAC/D,OAAO;MACL;MACA;MACAC,SAAS,EAAER,wBAAwB,GAAGE,EAAE,CAACO,YAAY,GAAGN,MAAM;MAC9DO,UAAU,EAAEN,KAAK;MACjBO,eAAe,EAAEN,GAAG;MACpBO,gBAAgB,EAAEN;KACnB;EACH,CAAC,CAAC;EAEN,IAAIN,wBAAwB,IAAI,CAACD,gBAAgB,EAAE;IACjD,MAAMc,QAAQ,GAAGzB,OAAO,CAACI,aAAa;IACtC,MAAMsB,kBAAkB,GAAGD,QAAQ,CAACE,eAAe,CAACR,qBAAqB,EAAE;IAC3ElB,OAAO,CAACO,IAAI,CAAC;MACXc,UAAU,EAAEM,IAAI,CAACC,GAAG,CAACH,kBAAkB,CAACV,KAAK,EAAES,QAAQ,CAACE,eAAe,CAACG,WAAW,CAAC;MACpFV,SAAS,EAAEQ,IAAI,CAACC,GAAG,CAACH,kBAAkB,CAACX,MAAM,EAAEU,QAAQ,CAACE,eAAe,CAACI,YAAY,CAAC;MACrFR,eAAe,EAAEG,kBAAkB,CAACT,GAAG;MACvCO,gBAAgB,EAAEE,kBAAkB,CAACR;KACtC,CAAC;GACH,MAAM;IACL,MAAMc,YAAY,GAAG,CAAAC,EAAA,GAAAjC,OAAO,CAACI,aAAa,CAAC8B,WAAW,cAAAD,EAAA,cAAAA,EAAA,GAAIE,MAAM;IAChElC,OAAO,CAACO,IAAI,CAAC;MACXY,SAAS,EAAE,CAAAgB,EAAA,IAAAC,EAAA,GAAAL,YAAY,CAACM,cAAc,cAAAD,EAAA,uBAAAA,EAAA,CAAEtB,MAAM,cAAAqB,EAAA,cAAAA,EAAA,GAAIJ,YAAY,CAACO,WAAW;MAC1EjB,UAAU,EAAE,CAAAkB,EAAA,IAAAC,EAAA,GAAAT,YAAY,CAACM,cAAc,cAAAG,EAAA,uBAAAA,EAAA,CAAEzB,KAAK,cAAAwB,EAAA,cAAAA,EAAA,GAAIR,YAAY,CAACU,UAAU;MACzEnB,eAAe,EAAE,CAAC;MAClBC,gBAAgB,EAAE;KACnB,CAAC;;EAGJ,IAAId,oBAAoB,IAAI,CAACC,gBAAgB,EAAE;IAC7CV,OAAO,CAAC0C,KAAK,EAAE;;EAGjB,OAAO1C,OAAO;AAChB,CAAC;AAID;;;;;;;;;;AAUA,OAAM,SAAU2C,qBAAqBA,CACnC5C,OAAgC,EAChC6C,OAAA,GAAiC;EAAEC,KAAK,EAAE,SAAS;EAAEC,MAAM,EAAE;AAAS,CAAE;;EAExE,CAAAd,EAAA,GAAAjC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgD,cAAc,cAAAf,EAAA,uBAAAA,EAAA,CAAAgB,IAAA,CAAAjD,OAAA,EAAG6C,OAAO,CAAC;AACpC;AAEA,OAAM,SAAUK,eAAeA,CAAC;EAAE3B,eAAe;EAAEH;AAAS,CAAe;EACzE,IAAIG,eAAe,GAAG,CAAC,EAAE;IACvB,OAAOA,eAAe;GACvB,MAAM,IAAIA,eAAe,GAAGH,SAAS,GAAGe,MAAM,CAACI,WAAW,EAAE;IAC3D,IAAInB,SAAS,GAAGe,MAAM,CAACI,WAAW,EAAE;MAClC,OAAOhB,eAAe;KACvB,MAAM;MACL,OAAOA,eAAe,GAAGH,SAAS,GAAGe,MAAM,CAACI,WAAW;;;EAG3D,OAAO,CAAC;AACV;AAEA;;;;;AAKA,OAAM,SAAUY,uBAAuBA,CAACC,GAAgB,EAAEC,gBAA8B;EACtF,MAAMC,YAAY,GAAGJ,eAAe,CAACE,GAAG,CAAC;EACzC,IAAIE,YAAY,EAAE;IAChB,CAACD,gBAAgB,IAAIlB,MAAM,EAAEoB,QAAQ,CAAC,CAAC,EAAED,YAAY,CAAC;;AAE1D;AAEA,OAAM,SAAUE,wBAAwBA,CAACxD,OAAoB;EAC3D,OACEF,WAAW,CAACE,OAAO,EAAEc,EAAE,IAAG;IACxB,MAAM2C,SAAS,GAAG3C,EAAE,CAACO,YAAY,GAAGP,EAAE,CAACiB,YAAY;IACnD,OAAO0B,SAAS,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAACC,QAAQ,CAACpD,gBAAgB,CAACQ,EAAE,CAAC,CAAC6C,SAAS,CAAC;EACjF,CAAC,CAAC,IAAIC,SAAS;AAEnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}