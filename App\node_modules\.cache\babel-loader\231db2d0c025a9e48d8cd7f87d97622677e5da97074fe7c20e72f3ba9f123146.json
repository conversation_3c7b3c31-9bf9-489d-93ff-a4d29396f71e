{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React from 'react';\nimport clsx from 'clsx';\nimport { InternalButton } from '../button/internal';\nimport { useInternalI18n } from '../i18n/context.js';\nimport InternalLiveRegion from '../live-region/internal';\nimport { TabButton } from './tab-button';\nimport { getStatusButtonId } from './util';\nimport styles from './styles.css.js';\nexport function StatusBar({\n  languageLabel,\n  cursorPosition,\n  paneStatus,\n  onErrorPaneToggle,\n  onWarningPaneToggle,\n  onTabFocus,\n  onTabBlur,\n  errorsTabRef,\n  warningsTabRef,\n  isTabFocused,\n  paneId,\n  onPreferencesOpen,\n  i18nStrings,\n  errorCount,\n  warningCount,\n  isRefresh\n}) {\n  const i18n = useInternalI18n('code-editor');\n  const errorText = `${i18n('i18nStrings.errorsTab', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.errorsTab)}: ${errorCount}`;\n  const warningText = `${i18n('i18nStrings.warningsTab', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.warningsTab)}: ${warningCount}`;\n  const errorButtonId = getStatusButtonId({\n    paneId,\n    paneStatus: 'error'\n  });\n  const warningButtonId = getStatusButtonId({\n    paneId,\n    paneStatus: 'warning'\n  });\n  return React.createElement(\"div\", {\n    className: clsx(styles['status-bar'], {\n      [styles['status-bar-with-hidden-pane']]: paneStatus === 'hidden'\n    })\n  }, React.createElement(\"div\", {\n    className: clsx(styles['status-bar__left'])\n  }, React.createElement(\"span\", {\n    className: styles['status-bar__language-mode']\n  }, languageLabel), React.createElement(\"span\", {\n    className: styles['status-bar__cursor-position']\n  }, cursorPosition), React.createElement(\"div\", {\n    className: styles['tab-list'],\n    role: \"tablist\"\n  }, React.createElement(TabButton, {\n    id: errorButtonId,\n    count: errorCount,\n    text: errorText,\n    className: styles['tab-button--errors'],\n    iconName: \"status-negative\",\n    disabled: errorCount === 0,\n    active: paneStatus === 'error',\n    onClick: onErrorPaneToggle,\n    onFocus: onTabFocus,\n    onBlur: onTabBlur,\n    ref: errorsTabRef,\n    ariaLabel: errorText,\n    paneId: paneId,\n    isRefresh: isRefresh\n  }), React.createElement(\"span\", {\n    className: styles['tab-button--divider']\n  }), React.createElement(TabButton, {\n    id: warningButtonId,\n    count: warningCount,\n    text: warningText,\n    className: styles['tab-button--warnings'],\n    iconName: \"status-warning\",\n    disabled: warningCount === 0,\n    active: paneStatus === 'warning',\n    onClick: onWarningPaneToggle,\n    onFocus: onTabFocus,\n    onBlur: onTabBlur,\n    ref: warningsTabRef,\n    tabIndex: paneStatus === 'error' && isTabFocused ? -1 : undefined,\n    ariaHidden: paneStatus === 'error' && isTabFocused ? true : undefined,\n    ariaLabel: warningText,\n    paneId: paneId,\n    isRefresh: isRefresh\n  })), React.createElement(InternalLiveRegion, {\n    assertive: true,\n    hidden: true,\n    tagName: \"span\"\n  }, React.createElement(\"span\", null, errorText, \" \"), React.createElement(\"span\", null, warningText))), React.createElement(\"div\", {\n    className: styles['status-bar__right']\n  }, React.createElement(\"div\", {\n    className: styles['status-bar__cog-button']\n  }, React.createElement(InternalButton, {\n    formAction: \"none\",\n    variant: \"icon\",\n    iconName: \"settings\",\n    iconAlt: \"Settings\",\n    ariaLabel: i18n('i18nStrings.preferencesButtonAriaLabel', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.preferencesButtonAriaLabel),\n    onClick: onPreferencesOpen,\n    __nativeAttributes: {\n      tabIndex: paneStatus !== 'hidden' && isTabFocused ? -1 : undefined,\n      'aria-hidden': paneStatus !== 'hidden' && isTabFocused ? true : undefined\n    }\n  }))));\n}", "map": {"version": 3, "names": ["React", "clsx", "InternalButton", "useInternalI18n", "InternalLiveRegion", "TabButton", "getStatusButtonId", "styles", "StatusBar", "languageLabel", "cursorPosition", "paneStatus", "onErrorPane<PERSON>oggle", "onWarningPaneToggle", "onTabFocus", "onTabBlur", "errorsTabRef", "warningsTabRef", "isTabFocused", "paneId", "onPreferencesOpen", "i18nStrings", "errorCount", "warningCount", "isRefresh", "i18n", "errorText", "errorsTab", "warningText", "warningsTab", "errorButtonId", "warningButtonId", "createElement", "className", "role", "id", "count", "text", "iconName", "disabled", "active", "onClick", "onFocus", "onBlur", "ref", "aria<PERSON><PERSON><PERSON>", "tabIndex", "undefined", "ariaHidden", "assertive", "hidden", "tagName", "formAction", "variant", "iconAlt", "preferencesButtonAriaLabel", "__nativeAttributes"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\code-editor\\status-bar.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React from 'react';\nimport clsx from 'clsx';\n\nimport { InternalButton } from '../button/internal';\nimport { useInternalI18n } from '../i18n/context.js';\nimport InternalLiveRegion from '../live-region/internal';\nimport { CodeEditorProps } from './interfaces';\nimport { TabButton } from './tab-button';\nimport { getStatusButtonId, PaneStatus } from './util';\n\nimport styles from './styles.css.js';\n\ninterface StatusBarProps {\n  languageLabel: string;\n  cursorPosition?: string;\n  paneStatus: PaneStatus;\n  isTabFocused: boolean;\n  paneId?: string;\n  i18nStrings?: CodeEditorProps.I18nStrings;\n  errorCount: number;\n  warningCount: number;\n  isRefresh: boolean;\n\n  errorsTabRef?: React.RefObject<HTMLButtonElement>;\n  warningsTabRef?: React.RefObject<HTMLButtonElement>;\n\n  onErrorPaneToggle: () => void;\n  onWarningPaneToggle: () => void;\n  onTabFocus?: React.FocusEventHandler<HTMLButtonElement>;\n  onTabBlur?: React.FocusEventHandler<HTMLButtonElement>;\n  onPreferencesOpen: () => void;\n  onHeightChange?: (height: number | null) => void;\n}\n\nexport function StatusBar({\n  languageLabel,\n  cursorPosition,\n  paneStatus,\n  onErrorPaneToggle,\n  onWarningPaneToggle,\n  onTabFocus,\n  onTabBlur,\n  errorsTabRef,\n  warningsTabRef,\n  isTabFocused,\n  paneId,\n  onPreferencesOpen,\n  i18nStrings,\n  errorCount,\n  warningCount,\n  isRefresh,\n}: StatusBarProps) {\n  const i18n = useInternalI18n('code-editor');\n  const errorText = `${i18n('i18nStrings.errorsTab', i18nStrings?.errorsTab)}: ${errorCount}`;\n  const warningText = `${i18n('i18nStrings.warningsTab', i18nStrings?.warningsTab)}: ${warningCount}`;\n  const errorButtonId = getStatusButtonId({ paneId, paneStatus: 'error' });\n  const warningButtonId = getStatusButtonId({ paneId, paneStatus: 'warning' });\n\n  return (\n    <div\n      className={clsx(styles['status-bar'], {\n        [styles['status-bar-with-hidden-pane']]: paneStatus === 'hidden',\n      })}\n    >\n      <div className={clsx(styles['status-bar__left'])}>\n        <span className={styles['status-bar__language-mode']}>{languageLabel}</span>\n        <span className={styles['status-bar__cursor-position']}>{cursorPosition}</span>\n\n        <div className={styles['tab-list']} role=\"tablist\">\n          <TabButton\n            id={errorButtonId}\n            count={errorCount}\n            text={errorText}\n            className={styles['tab-button--errors']}\n            iconName=\"status-negative\"\n            disabled={errorCount === 0}\n            active={paneStatus === 'error'}\n            onClick={onErrorPaneToggle}\n            onFocus={onTabFocus}\n            onBlur={onTabBlur}\n            ref={errorsTabRef}\n            ariaLabel={errorText}\n            paneId={paneId}\n            isRefresh={isRefresh}\n          />\n\n          <span className={styles['tab-button--divider']}></span>\n\n          <TabButton\n            id={warningButtonId}\n            count={warningCount}\n            text={warningText}\n            className={styles['tab-button--warnings']}\n            iconName=\"status-warning\"\n            disabled={warningCount === 0}\n            active={paneStatus === 'warning'}\n            onClick={onWarningPaneToggle}\n            onFocus={onTabFocus}\n            onBlur={onTabBlur}\n            ref={warningsTabRef}\n            tabIndex={paneStatus === 'error' && isTabFocused ? -1 : undefined}\n            ariaHidden={paneStatus === 'error' && isTabFocused ? true : undefined}\n            ariaLabel={warningText}\n            paneId={paneId}\n            isRefresh={isRefresh}\n          />\n        </div>\n        <InternalLiveRegion assertive={true} hidden={true} tagName=\"span\">\n          <span>{errorText} </span>\n          <span>{warningText}</span>\n        </InternalLiveRegion>\n      </div>\n\n      <div className={styles['status-bar__right']}>\n        <div className={styles['status-bar__cog-button']}>\n          <InternalButton\n            formAction=\"none\"\n            variant=\"icon\"\n            iconName=\"settings\"\n            iconAlt=\"Settings\"\n            ariaLabel={i18n('i18nStrings.preferencesButtonAriaLabel', i18nStrings?.preferencesButtonAriaLabel)}\n            onClick={onPreferencesOpen}\n            __nativeAttributes={{\n              tabIndex: paneStatus !== 'hidden' && isTabFocused ? -1 : undefined,\n              'aria-hidden': paneStatus !== 'hidden' && isTabFocused ? true : undefined,\n            }}\n          />\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "mappings": "AAAA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,MAAM;AAEvB,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAOC,kBAAkB,MAAM,yBAAyB;AAExD,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,iBAAiB,QAAoB,QAAQ;AAEtD,OAAOC,MAAM,MAAM,iBAAiB;AAwBpC,OAAM,SAAUC,SAASA,CAAC;EACxBC,aAAa;EACbC,cAAc;EACdC,UAAU;EACVC,iBAAiB;EACjBC,mBAAmB;EACnBC,UAAU;EACVC,SAAS;EACTC,YAAY;EACZC,cAAc;EACdC,YAAY;EACZC,MAAM;EACNC,iBAAiB;EACjBC,WAAW;EACXC,UAAU;EACVC,YAAY;EACZC;AAAS,CACM;EACf,MAAMC,IAAI,GAAGtB,eAAe,CAAC,aAAa,CAAC;EAC3C,MAAMuB,SAAS,GAAG,GAAGD,IAAI,CAAC,uBAAuB,EAAEJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEM,SAAS,CAAC,KAAKL,UAAU,EAAE;EAC3F,MAAMM,WAAW,GAAG,GAAGH,IAAI,CAAC,yBAAyB,EAAEJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEQ,WAAW,CAAC,KAAKN,YAAY,EAAE;EACnG,MAAMO,aAAa,GAAGxB,iBAAiB,CAAC;IAAEa,MAAM;IAAER,UAAU,EAAE;EAAO,CAAE,CAAC;EACxE,MAAMoB,eAAe,GAAGzB,iBAAiB,CAAC;IAAEa,MAAM;IAAER,UAAU,EAAE;EAAS,CAAE,CAAC;EAE5E,OACEX,KAAA,CAAAgC,aAAA;IACEC,SAAS,EAAEhC,IAAI,CAACM,MAAM,CAAC,YAAY,CAAC,EAAE;MACpC,CAACA,MAAM,CAAC,6BAA6B,CAAC,GAAGI,UAAU,KAAK;KACzD;EAAC,GAEFX,KAAA,CAAAgC,aAAA;IAAKC,SAAS,EAAEhC,IAAI,CAACM,MAAM,CAAC,kBAAkB,CAAC;EAAC,GAC9CP,KAAA,CAAAgC,aAAA;IAAMC,SAAS,EAAE1B,MAAM,CAAC,2BAA2B;EAAC,GAAGE,aAAa,CAAQ,EAC5ET,KAAA,CAAAgC,aAAA;IAAMC,SAAS,EAAE1B,MAAM,CAAC,6BAA6B;EAAC,GAAGG,cAAc,CAAQ,EAE/EV,KAAA,CAAAgC,aAAA;IAAKC,SAAS,EAAE1B,MAAM,CAAC,UAAU,CAAC;IAAE2B,IAAI,EAAC;EAAS,GAChDlC,KAAA,CAAAgC,aAAA,CAAC3B,SAAS;IACR8B,EAAE,EAAEL,aAAa;IACjBM,KAAK,EAAEd,UAAU;IACjBe,IAAI,EAAEX,SAAS;IACfO,SAAS,EAAE1B,MAAM,CAAC,oBAAoB,CAAC;IACvC+B,QAAQ,EAAC,iBAAiB;IAC1BC,QAAQ,EAAEjB,UAAU,KAAK,CAAC;IAC1BkB,MAAM,EAAE7B,UAAU,KAAK,OAAO;IAC9B8B,OAAO,EAAE7B,iBAAiB;IAC1B8B,OAAO,EAAE5B,UAAU;IACnB6B,MAAM,EAAE5B,SAAS;IACjB6B,GAAG,EAAE5B,YAAY;IACjB6B,SAAS,EAAEnB,SAAS;IACpBP,MAAM,EAAEA,MAAM;IACdK,SAAS,EAAEA;EAAS,EACpB,EAEFxB,KAAA,CAAAgC,aAAA;IAAMC,SAAS,EAAE1B,MAAM,CAAC,qBAAqB;EAAC,EAAS,EAEvDP,KAAA,CAAAgC,aAAA,CAAC3B,SAAS;IACR8B,EAAE,EAAEJ,eAAe;IACnBK,KAAK,EAAEb,YAAY;IACnBc,IAAI,EAAET,WAAW;IACjBK,SAAS,EAAE1B,MAAM,CAAC,sBAAsB,CAAC;IACzC+B,QAAQ,EAAC,gBAAgB;IACzBC,QAAQ,EAAEhB,YAAY,KAAK,CAAC;IAC5BiB,MAAM,EAAE7B,UAAU,KAAK,SAAS;IAChC8B,OAAO,EAAE5B,mBAAmB;IAC5B6B,OAAO,EAAE5B,UAAU;IACnB6B,MAAM,EAAE5B,SAAS;IACjB6B,GAAG,EAAE3B,cAAc;IACnB6B,QAAQ,EAAEnC,UAAU,KAAK,OAAO,IAAIO,YAAY,GAAG,CAAC,CAAC,GAAG6B,SAAS;IACjEC,UAAU,EAAErC,UAAU,KAAK,OAAO,IAAIO,YAAY,GAAG,IAAI,GAAG6B,SAAS;IACrEF,SAAS,EAAEjB,WAAW;IACtBT,MAAM,EAAEA,MAAM;IACdK,SAAS,EAAEA;EAAS,EACpB,CACE,EACNxB,KAAA,CAAAgC,aAAA,CAAC5B,kBAAkB;IAAC6C,SAAS,EAAE,IAAI;IAAEC,MAAM,EAAE,IAAI;IAAEC,OAAO,EAAC;EAAM,GAC/DnD,KAAA,CAAAgC,aAAA,eAAON,SAAS,E,IAAS,EACzB1B,KAAA,CAAAgC,aAAA,eAAOJ,WAAW,CAAQ,CACP,CACjB,EAEN5B,KAAA,CAAAgC,aAAA;IAAKC,SAAS,EAAE1B,MAAM,CAAC,mBAAmB;EAAC,GACzCP,KAAA,CAAAgC,aAAA;IAAKC,SAAS,EAAE1B,MAAM,CAAC,wBAAwB;EAAC,GAC9CP,KAAA,CAAAgC,aAAA,CAAC9B,cAAc;IACbkD,UAAU,EAAC,MAAM;IACjBC,OAAO,EAAC,MAAM;IACdf,QAAQ,EAAC,UAAU;IACnBgB,OAAO,EAAC,UAAU;IAClBT,SAAS,EAAEpB,IAAI,CAAC,wCAAwC,EAAEJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkC,0BAA0B,CAAC;IAClGd,OAAO,EAAErB,iBAAiB;IAC1BoC,kBAAkB,EAAE;MAClBV,QAAQ,EAAEnC,UAAU,KAAK,QAAQ,IAAIO,YAAY,GAAG,CAAC,CAAC,GAAG6B,SAAS;MAClE,aAAa,EAAEpC,UAAU,KAAK,QAAQ,IAAIO,YAAY,GAAG,IAAI,GAAG6B;;EACjE,EACD,CACE,CACF,CACF;AAEV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}