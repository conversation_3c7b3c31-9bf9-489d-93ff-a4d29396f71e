{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useEffect } from 'react';\nimport clsx from 'clsx';\nimport InternalAlert from '../../alert/internal';\nimport InternalBox from '../../box/internal';\nimport { InternalButton } from '../../button/internal';\nimport { useUniqueId } from '../../internal/hooks/use-unique-id/index.js';\nimport { scrollElementIntoView } from '../../internal/utils/scrollable-containers';\nimport { joinStrings } from '../../internal/utils/strings/join-strings.js';\nimport PopoverBody from '../../popover/body';\nimport PopoverContainer from '../../popover/container';\nimport InternalSpaceBetween from '../../space-between/internal';\nimport styles from './styles.css.js';\nconst arrow = position => React.createElement(\"div\", {\n  className: clsx(styles.arrow, styles[`arrow-position-${position}`])\n}, React.createElement(\"div\", {\n  className: styles['arrow-outer']\n}), React.createElement(\"div\", {\n  className: styles['arrow-inner']\n}));\nexport function AnnotationPopover({\n  title,\n  content,\n  alert,\n  direction = 'top',\n  taskLocalStepIndex,\n  totalLocalSteps,\n  showPreviousButton,\n  showFinishButton,\n  onDismiss,\n  nextButtonEnabled,\n  onNextButtonClick,\n  onFinish,\n  trackRef,\n  previousButtonEnabled,\n  onPreviousButtonClick,\n  i18nStrings\n}) {\n  useEffect(() => {\n    var _a;\n    scrollElementIntoView((_a = trackRef.current) !== null && _a !== void 0 ? _a : undefined);\n  }, [trackRef]);\n  const popoverHeaderId = useUniqueId('poppver-header-');\n  const stepCounterId = useUniqueId('step-counter-');\n  return React.createElement(PopoverContainer, {\n    size: \"medium\",\n    fixedWidth: false,\n    position: direction,\n    trackRef: trackRef,\n    trackKey: taskLocalStepIndex,\n    variant: \"annotation\",\n    arrow: arrow,\n    zIndex: 1000\n  }, React.createElement(PopoverBody, {\n    dismissButton: true,\n    dismissAriaLabel: i18nStrings.labelDismissAnnotation,\n    header: React.createElement(InternalBox, {\n      id: popoverHeaderId,\n      color: \"text-body-secondary\",\n      fontSize: \"body-s\",\n      margin: {\n        top: 'xxxs'\n      },\n      className: styles.header\n    }, title),\n    onDismiss: onDismiss,\n    className: styles.annotation,\n    variant: \"annotation\",\n    overflowVisible: \"content\",\n    // create new dialog to have the native dialog behavior of the screen readers\n    key: taskLocalStepIndex,\n    ariaLabelledby: joinStrings(popoverHeaderId, stepCounterId)\n  }, React.createElement(InternalSpaceBetween, {\n    size: \"s\"\n  }, React.createElement(\"div\", {\n    className: styles.description\n  }, React.createElement(InternalBox, {\n    className: styles.content\n  }, content)), alert && React.createElement(InternalAlert, {\n    type: \"warning\"\n  }, alert), React.createElement(InternalSpaceBetween, {\n    size: \"s\"\n  }, React.createElement(\"div\", {\n    className: styles.divider\n  }), React.createElement(\"div\", {\n    className: styles.actionBar\n  }, React.createElement(\"div\", {\n    className: styles.stepCounter\n  }, React.createElement(InternalBox, {\n    id: stepCounterId,\n    className: styles['step-counter-content'],\n    color: \"text-body-secondary\",\n    fontSize: \"body-s\"\n  }, i18nStrings.stepCounterText(taskLocalStepIndex !== null && taskLocalStepIndex !== void 0 ? taskLocalStepIndex : 0, totalLocalSteps !== null && totalLocalSteps !== void 0 ? totalLocalSteps : 0))), React.createElement(InternalSpaceBetween, {\n    size: \"xs\",\n    direction: \"horizontal\"\n  }, showPreviousButton && React.createElement(InternalButton, {\n    variant: \"link\",\n    onClick: onPreviousButtonClick,\n    disabled: !previousButtonEnabled,\n    formAction: \"none\",\n    ariaLabel: i18nStrings.previousButtonText,\n    className: styles['previous-button']\n  }, i18nStrings.previousButtonText), showFinishButton ? React.createElement(InternalButton, {\n    onClick: onFinish,\n    formAction: \"none\",\n    ariaLabel: i18nStrings.finishButtonText,\n    className: styles['finish-button']\n  }, i18nStrings.finishButtonText) : React.createElement(InternalButton, {\n    onClick: onNextButtonClick,\n    disabled: !nextButtonEnabled,\n    formAction: \"none\",\n    ariaLabel: i18nStrings.nextButtonText,\n    className: styles['next-button']\n  }, i18nStrings.nextButtonText)))))));\n}", "map": {"version": 3, "names": ["React", "useEffect", "clsx", "InternalAlert", "InternalBox", "InternalButton", "useUniqueId", "scrollElementIntoView", "joinStrings", "PopoverBody", "PopoverContainer", "InternalSpaceBetween", "styles", "arrow", "position", "createElement", "className", "AnnotationPopover", "title", "content", "alert", "direction", "taskLocalStepIndex", "totalLocalSteps", "showPreviousButton", "showFinishButton", "on<PERSON><PERSON><PERSON>", "nextButtonEnabled", "onNextButtonClick", "onFinish", "trackRef", "previousButtonEnabled", "onPreviousButtonClick", "i18nStrings", "_a", "current", "undefined", "popoverHeaderId", "stepCounterId", "size", "fixedWidth", "trackKey", "variant", "zIndex", "dismiss<PERSON><PERSON><PERSON>", "dismissAria<PERSON>abel", "labelDismissAnnotation", "header", "id", "color", "fontSize", "margin", "top", "annotation", "overflowVisible", "key", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description", "type", "divider", "actionBar", "<PERSON><PERSON><PERSON><PERSON>", "stepCounterText", "onClick", "disabled", "formAction", "aria<PERSON><PERSON><PERSON>", "previousButtonText", "finishButtonText", "nextButtonText"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\annotation-context\\annotation\\annotation-popover.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useEffect } from 'react';\nimport clsx from 'clsx';\n\nimport InternalAlert from '../../alert/internal';\nimport InternalBox from '../../box/internal';\nimport { InternalButton } from '../../button/internal';\nimport { HotspotProps } from '../../hotspot/interfaces';\nimport { useUniqueId } from '../../internal/hooks/use-unique-id/index.js';\nimport { scrollElementIntoView } from '../../internal/utils/scrollable-containers';\nimport { joinStrings } from '../../internal/utils/strings/join-strings.js';\nimport PopoverBody from '../../popover/body';\nimport PopoverContainer from '../../popover/container';\nimport { InternalPosition } from '../../popover/interfaces';\nimport InternalSpaceBetween from '../../space-between/internal';\nimport { AnnotationContextProps } from '../interfaces';\n\nimport styles from './styles.css.js';\n\ninterface AnnotationPopoverProps {\n  title: string;\n  content: React.ReactNode;\n  alert: React.ReactNode;\n\n  direction: HotspotProps['direction'];\n\n  nextButtonEnabled: boolean;\n  onNextButtonClick: () => void;\n\n  onFinish: () => void;\n\n  showPreviousButton: boolean;\n  previousButtonEnabled: boolean;\n  onPreviousButtonClick: () => void;\n\n  taskLocalStepIndex: number;\n\n  totalLocalSteps: number;\n\n  showFinishButton: boolean;\n\n  onDismiss: () => void;\n\n  trackRef: React.RefObject<HTMLElement>;\n\n  i18nStrings: AnnotationContextProps['i18nStrings'];\n}\n\nconst arrow = (position: InternalPosition | null) => (\n  <div className={clsx(styles.arrow, styles[`arrow-position-${position}`])}>\n    <div className={styles['arrow-outer']} />\n    <div className={styles['arrow-inner']} />\n  </div>\n);\n\nexport function AnnotationPopover({\n  title,\n  content,\n  alert,\n\n  direction = 'top',\n\n  taskLocalStepIndex,\n\n  totalLocalSteps,\n\n  showPreviousButton,\n  showFinishButton,\n\n  onDismiss,\n\n  nextButtonEnabled,\n  onNextButtonClick,\n\n  onFinish,\n\n  trackRef,\n\n  previousButtonEnabled,\n  onPreviousButtonClick,\n  i18nStrings,\n}: AnnotationPopoverProps) {\n  useEffect(() => {\n    scrollElementIntoView(trackRef.current ?? undefined);\n  }, [trackRef]);\n\n  const popoverHeaderId = useUniqueId('poppver-header-');\n  const stepCounterId = useUniqueId('step-counter-');\n\n  return (\n    <PopoverContainer\n      size=\"medium\"\n      fixedWidth={false}\n      position={direction}\n      trackRef={trackRef}\n      trackKey={taskLocalStepIndex}\n      variant=\"annotation\"\n      arrow={arrow}\n      zIndex={1000}\n    >\n      <PopoverBody\n        dismissButton={true}\n        dismissAriaLabel={i18nStrings.labelDismissAnnotation}\n        header={\n          <InternalBox\n            id={popoverHeaderId}\n            color=\"text-body-secondary\"\n            fontSize=\"body-s\"\n            margin={{ top: 'xxxs' }}\n            className={styles.header}\n          >\n            {title}\n          </InternalBox>\n        }\n        onDismiss={onDismiss}\n        className={styles.annotation}\n        variant=\"annotation\"\n        overflowVisible=\"content\"\n        // create new dialog to have the native dialog behavior of the screen readers\n        key={taskLocalStepIndex}\n        ariaLabelledby={joinStrings(popoverHeaderId, stepCounterId)}\n      >\n        <InternalSpaceBetween size=\"s\">\n          <div className={styles.description}>\n            <InternalBox className={styles.content}>{content}</InternalBox>\n          </div>\n\n          {alert && <InternalAlert type=\"warning\">{alert}</InternalAlert>}\n\n          <InternalSpaceBetween size=\"s\">\n            <div className={styles.divider} />\n\n            <div className={styles.actionBar}>\n              <div className={styles.stepCounter}>\n                <InternalBox\n                  id={stepCounterId}\n                  className={styles['step-counter-content']}\n                  color=\"text-body-secondary\"\n                  fontSize=\"body-s\"\n                >\n                  {i18nStrings.stepCounterText(taskLocalStepIndex ?? 0, totalLocalSteps ?? 0)}\n                </InternalBox>\n              </div>\n              <InternalSpaceBetween size=\"xs\" direction=\"horizontal\">\n                {showPreviousButton && (\n                  <InternalButton\n                    variant=\"link\"\n                    onClick={onPreviousButtonClick}\n                    disabled={!previousButtonEnabled}\n                    formAction=\"none\"\n                    ariaLabel={i18nStrings.previousButtonText}\n                    className={styles['previous-button']}\n                  >\n                    {i18nStrings.previousButtonText}\n                  </InternalButton>\n                )}\n\n                {showFinishButton ? (\n                  <InternalButton\n                    onClick={onFinish}\n                    formAction=\"none\"\n                    ariaLabel={i18nStrings.finishButtonText}\n                    className={styles['finish-button']}\n                  >\n                    {i18nStrings.finishButtonText}\n                  </InternalButton>\n                ) : (\n                  <InternalButton\n                    onClick={onNextButtonClick}\n                    disabled={!nextButtonEnabled}\n                    formAction=\"none\"\n                    ariaLabel={i18nStrings.nextButtonText}\n                    className={styles['next-button']}\n                  >\n                    {i18nStrings.nextButtonText}\n                  </InternalButton>\n                )}\n              </InternalSpaceBetween>\n            </div>\n          </InternalSpaceBetween>\n        </InternalSpaceBetween>\n      </PopoverBody>\n    </PopoverContainer>\n  );\n}\n"], "mappings": "AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,IAAI,MAAM,MAAM;AAEvB,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,SAASC,cAAc,QAAQ,uBAAuB;AAEtD,SAASC,WAAW,QAAQ,6CAA6C;AACzE,SAASC,qBAAqB,QAAQ,4CAA4C;AAClF,SAASC,WAAW,QAAQ,8CAA8C;AAC1E,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,gBAAgB,MAAM,yBAAyB;AAEtD,OAAOC,oBAAoB,MAAM,8BAA8B;AAG/D,OAAOC,MAAM,MAAM,iBAAiB;AA+BpC,MAAMC,KAAK,GAAIC,QAAiC,IAC9Cd,KAAA,CAAAe,aAAA;EAAKC,SAAS,EAAEd,IAAI,CAACU,MAAM,CAACC,KAAK,EAAED,MAAM,CAAC,kBAAkBE,QAAQ,EAAE,CAAC;AAAC,GACtEd,KAAA,CAAAe,aAAA;EAAKC,SAAS,EAAEJ,MAAM,CAAC,aAAa;AAAC,EAAI,EACzCZ,KAAA,CAAAe,aAAA;EAAKC,SAAS,EAAEJ,MAAM,CAAC,aAAa;AAAC,EAAI,CAE5C;AAED,OAAM,SAAUK,iBAAiBA,CAAC;EAChCC,KAAK;EACLC,OAAO;EACPC,KAAK;EAELC,SAAS,GAAG,KAAK;EAEjBC,kBAAkB;EAElBC,eAAe;EAEfC,kBAAkB;EAClBC,gBAAgB;EAEhBC,SAAS;EAETC,iBAAiB;EACjBC,iBAAiB;EAEjBC,QAAQ;EAERC,QAAQ;EAERC,qBAAqB;EACrBC,qBAAqB;EACrBC;AAAW,CACY;EACvBhC,SAAS,CAAC,MAAK;;IACbM,qBAAqB,CAAC,CAAA2B,EAAA,GAAAJ,QAAQ,CAACK,OAAO,cAAAD,EAAA,cAAAA,EAAA,GAAIE,SAAS,CAAC;EACtD,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;EAEd,MAAMO,eAAe,GAAG/B,WAAW,CAAC,iBAAiB,CAAC;EACtD,MAAMgC,aAAa,GAAGhC,WAAW,CAAC,eAAe,CAAC;EAElD,OACEN,KAAA,CAAAe,aAAA,CAACL,gBAAgB;IACf6B,IAAI,EAAC,QAAQ;IACbC,UAAU,EAAE,KAAK;IACjB1B,QAAQ,EAAEO,SAAS;IACnBS,QAAQ,EAAEA,QAAQ;IAClBW,QAAQ,EAAEnB,kBAAkB;IAC5BoB,OAAO,EAAC,YAAY;IACpB7B,KAAK,EAAEA,KAAK;IACZ8B,MAAM,EAAE;EAAI,GAEZ3C,KAAA,CAAAe,aAAA,CAACN,WAAW;IACVmC,aAAa,EAAE,IAAI;IACnBC,gBAAgB,EAAEZ,WAAW,CAACa,sBAAsB;IACpDC,MAAM,EACJ/C,KAAA,CAAAe,aAAA,CAACX,WAAW;MACV4C,EAAE,EAAEX,eAAe;MACnBY,KAAK,EAAC,qBAAqB;MAC3BC,QAAQ,EAAC,QAAQ;MACjBC,MAAM,EAAE;QAAEC,GAAG,EAAE;MAAM,CAAE;MACvBpC,SAAS,EAAEJ,MAAM,CAACmC;IAAM,GAEvB7B,KAAK,CACM;IAEhBQ,SAAS,EAAEA,SAAS;IACpBV,SAAS,EAAEJ,MAAM,CAACyC,UAAU;IAC5BX,OAAO,EAAC,YAAY;IACpBY,eAAe,EAAC,SAAS;IACzB;IACAC,GAAG,EAAEjC,kBAAkB;IACvBkC,cAAc,EAAEhD,WAAW,CAAC6B,eAAe,EAAEC,aAAa;EAAC,GAE3DtC,KAAA,CAAAe,aAAA,CAACJ,oBAAoB;IAAC4B,IAAI,EAAC;EAAG,GAC5BvC,KAAA,CAAAe,aAAA;IAAKC,SAAS,EAAEJ,MAAM,CAAC6C;EAAW,GAChCzD,KAAA,CAAAe,aAAA,CAACX,WAAW;IAACY,SAAS,EAAEJ,MAAM,CAACO;EAAO,GAAGA,OAAO,CAAe,CAC3D,EAELC,KAAK,IAAIpB,KAAA,CAAAe,aAAA,CAACZ,aAAa;IAACuD,IAAI,EAAC;EAAS,GAAEtC,KAAK,CAAiB,EAE/DpB,KAAA,CAAAe,aAAA,CAACJ,oBAAoB;IAAC4B,IAAI,EAAC;EAAG,GAC5BvC,KAAA,CAAAe,aAAA;IAAKC,SAAS,EAAEJ,MAAM,CAAC+C;EAAO,EAAI,EAElC3D,KAAA,CAAAe,aAAA;IAAKC,SAAS,EAAEJ,MAAM,CAACgD;EAAS,GAC9B5D,KAAA,CAAAe,aAAA;IAAKC,SAAS,EAAEJ,MAAM,CAACiD;EAAW,GAChC7D,KAAA,CAAAe,aAAA,CAACX,WAAW;IACV4C,EAAE,EAAEV,aAAa;IACjBtB,SAAS,EAAEJ,MAAM,CAAC,sBAAsB,CAAC;IACzCqC,KAAK,EAAC,qBAAqB;IAC3BC,QAAQ,EAAC;EAAQ,GAEhBjB,WAAW,CAAC6B,eAAe,CAACxC,kBAAkB,aAAlBA,kBAAkB,cAAlBA,kBAAkB,GAAI,CAAC,EAAEC,eAAe,aAAfA,eAAe,cAAfA,eAAe,GAAI,CAAC,CAAC,CAC/D,CACV,EACNvB,KAAA,CAAAe,aAAA,CAACJ,oBAAoB;IAAC4B,IAAI,EAAC,IAAI;IAAClB,SAAS,EAAC;EAAY,GACnDG,kBAAkB,IACjBxB,KAAA,CAAAe,aAAA,CAACV,cAAc;IACbqC,OAAO,EAAC,MAAM;IACdqB,OAAO,EAAE/B,qBAAqB;IAC9BgC,QAAQ,EAAE,CAACjC,qBAAqB;IAChCkC,UAAU,EAAC,MAAM;IACjBC,SAAS,EAAEjC,WAAW,CAACkC,kBAAkB;IACzCnD,SAAS,EAAEJ,MAAM,CAAC,iBAAiB;EAAC,GAEnCqB,WAAW,CAACkC,kBAAkB,CAElC,EAEA1C,gBAAgB,GACfzB,KAAA,CAAAe,aAAA,CAACV,cAAc;IACb0D,OAAO,EAAElC,QAAQ;IACjBoC,UAAU,EAAC,MAAM;IACjBC,SAAS,EAAEjC,WAAW,CAACmC,gBAAgB;IACvCpD,SAAS,EAAEJ,MAAM,CAAC,eAAe;EAAC,GAEjCqB,WAAW,CAACmC,gBAAgB,CACd,GAEjBpE,KAAA,CAAAe,aAAA,CAACV,cAAc;IACb0D,OAAO,EAAEnC,iBAAiB;IAC1BoC,QAAQ,EAAE,CAACrC,iBAAiB;IAC5BsC,UAAU,EAAC,MAAM;IACjBC,SAAS,EAAEjC,WAAW,CAACoC,cAAc;IACrCrD,SAAS,EAAEJ,MAAM,CAAC,aAAa;EAAC,GAE/BqB,WAAW,CAACoC,cAAc,CAE9B,CACoB,CACnB,CACe,CACF,CACX,CACG;AAEvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}