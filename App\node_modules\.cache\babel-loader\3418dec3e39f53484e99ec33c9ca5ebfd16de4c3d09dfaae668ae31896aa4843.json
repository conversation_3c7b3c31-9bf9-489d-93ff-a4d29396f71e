{"ast": null, "code": "import { __rest } from \"tslib\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useCallback, useRef } from 'react';\nimport { CSSTransition } from 'react-transition-group';\nimport clsx from 'clsx';\nimport { getBaseProps } from '../internal/base-component';\nimport { fireNonCancelableEvent } from '../internal/events';\nimport { useControllable } from '../internal/hooks/use-controllable';\nimport { useUniqueId } from '../internal/hooks/use-unique-id';\nimport { KeyCode } from '../internal/keycode';\nimport { ExpandableSectionContainer } from './expandable-section-container';\nimport { ExpandableSectionHeader } from './expandable-section-header';\nimport { variantSupportsDescription } from './utils';\nimport analyticsSelectors from './analytics-metadata/styles.css.js';\nimport styles from './styles.css.js';\nexport default function InternalExpandableSection(_a) {\n  var {\n      expanded: controlledExpanded,\n      defaultExpanded,\n      onChange,\n      variant = 'default',\n      children,\n      header,\n      headerText,\n      headerCounter,\n      headerDescription,\n      headerInfo,\n      headerActions,\n      headingTagOverride,\n      disableContentPaddings,\n      headerAriaLabel,\n      __internalRootRef,\n      __injectAnalyticsComponentMetadata\n    } = _a,\n    props = __rest(_a, [\"expanded\", \"defaultExpanded\", \"onChange\", \"variant\", \"children\", \"header\", \"headerText\", \"headerCounter\", \"headerDescription\", \"headerInfo\", \"headerActions\", \"headingTagOverride\", \"disableContentPaddings\", \"headerAriaLabel\", \"__internalRootRef\", \"__injectAnalyticsComponentMetadata\"]);\n  const ref = useRef(null);\n  const controlId = useUniqueId();\n  const triggerControlId = `${controlId}-trigger`;\n  const descriptionId = `${controlId}-description`;\n  const baseProps = getBaseProps(props);\n  const [expanded, setExpanded] = useControllable(controlledExpanded, onChange, defaultExpanded, {\n    componentName: 'ExpandableSection',\n    controlledProp: 'expanded',\n    changeHandler: 'onChange'\n  });\n  const onExpandChange = useCallback(expanded => {\n    setExpanded(expanded);\n    fireNonCancelableEvent(onChange, {\n      expanded\n    });\n  }, [onChange, setExpanded]);\n  const onClick = useCallback(() => {\n    onExpandChange(!expanded);\n  }, [onExpandChange, expanded]);\n  const onKeyUp = useCallback(event => {\n    const interactionKeys = [KeyCode.enter, KeyCode.space];\n    if (interactionKeys.indexOf(event.keyCode) !== -1) {\n      onExpandChange(!expanded);\n    }\n  }, [onExpandChange, expanded]);\n  const onKeyDown = useCallback(event => {\n    if (event.keyCode === KeyCode.space) {\n      // Prevent the page from scrolling when toggling the component with the space bar.\n      event.preventDefault();\n    }\n  }, []);\n  const triggerProps = {\n    ariaControls: controlId,\n    ariaLabel: headerAriaLabel,\n    ariaLabelledBy: headerAriaLabel ? undefined : triggerControlId,\n    onKeyUp,\n    onKeyDown,\n    onClick\n  };\n  // Map stacked variant to container to avoid code duplication\n  const baseVariant = variant === 'stacked' ? 'container' : variant;\n  return React.createElement(ExpandableSectionContainer, Object.assign({}, props, {\n    expanded: expanded,\n    className: clsx(baseProps.className, styles.root, analyticsSelectors.root),\n    variant: variant,\n    disableContentPaddings: disableContentPaddings,\n    __injectAnalyticsComponentMetadata: __injectAnalyticsComponentMetadata,\n    header: React.createElement(ExpandableSectionHeader, Object.assign({\n      id: triggerControlId,\n      descriptionId: descriptionId,\n      className: clsx(styles.header, styles[`header-${baseVariant}`]),\n      variant: baseVariant,\n      expanded: !!expanded,\n      header: header,\n      headerText: headerText,\n      headerDescription: headerDescription,\n      headerCounter: headerCounter,\n      headerInfo: headerInfo,\n      headerActions: headerActions,\n      headingTagOverride: headingTagOverride\n    }, triggerProps)),\n    __internalRootRef: __internalRootRef\n  }), React.createElement(CSSTransition, {\n    in: expanded,\n    timeout: 30,\n    classNames: {\n      enter: styles['content-enter']\n    },\n    nodeRef: ref\n  }, React.createElement(\"div\", {\n    id: controlId,\n    ref: ref,\n    className: clsx(styles.content, styles[`content-${baseVariant}`], expanded && styles['content-expanded']),\n    role: \"group\",\n    \"aria-label\": triggerProps.ariaLabel,\n    \"aria-labelledby\": triggerProps.ariaLabelledBy,\n    \"aria-describedby\": variantSupportsDescription(baseVariant) && headerDescription ? descriptionId : undefined\n  }, children)));\n}", "map": {"version": 3, "names": ["React", "useCallback", "useRef", "CSSTransition", "clsx", "getBaseProps", "fireNonCancelableEvent", "useControllable", "useUniqueId", "KeyCode", "ExpandableSectionContainer", "ExpandableSectionHeader", "variantSupportsDescription", "analyticsSelectors", "styles", "InternalExpandableSection", "_a", "expanded", "controlledExpanded", "defaultExpanded", "onChange", "variant", "children", "header", "headerText", "headerCounter", "headerDescription", "headerInfo", "headerActions", "headingTagOverride", "disableContentPaddings", "headerAriaLabel", "__internalRootRef", "__injectAnalyticsComponentMetadata", "props", "__rest", "ref", "controlId", "triggerControlId", "descriptionId", "baseProps", "setExpanded", "componentName", "controlledProp", "<PERSON><PERSON><PERSON><PERSON>", "onExpandChange", "onClick", "onKeyUp", "event", "<PERSON><PERSON><PERSON><PERSON>", "enter", "space", "indexOf", "keyCode", "onKeyDown", "preventDefault", "triggerProps", "ariaControls", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "undefined", "baseVariant", "createElement", "Object", "assign", "className", "root", "id", "in", "timeout", "classNames", "nodeRef", "content", "role"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\expandable-section\\internal.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { KeyboardEvent, useCallback, useRef } from 'react';\nimport { CSSTransition } from 'react-transition-group';\nimport clsx from 'clsx';\n\nimport { getBaseProps } from '../internal/base-component';\nimport { fireNonCancelableEvent } from '../internal/events';\nimport { InternalBaseComponentProps } from '../internal/hooks/use-base-component';\nimport { useControllable } from '../internal/hooks/use-controllable';\nimport { useUniqueId } from '../internal/hooks/use-unique-id';\nimport { KeyCode } from '../internal/keycode';\nimport { ExpandableSectionContainer } from './expandable-section-container';\nimport { ExpandableSectionHeader } from './expandable-section-header';\nimport { ExpandableSectionProps, InternalVariant } from './interfaces';\nimport { variantSupportsDescription } from './utils';\n\nimport analyticsSelectors from './analytics-metadata/styles.css.js';\nimport styles from './styles.css.js';\n\ntype InternalExpandableSectionProps = Omit<ExpandableSectionProps, 'variant'> &\n  InternalBaseComponentProps & {\n    variant?: InternalVariant;\n    __injectAnalyticsComponentMetadata?: boolean;\n  };\n\nexport default function InternalExpandableSection({\n  expanded: controlledExpanded,\n  defaultExpanded,\n  onChange,\n  variant = 'default',\n  children,\n  header,\n  headerText,\n  headerCounter,\n  headerDescription,\n  headerInfo,\n  headerActions,\n  headingTagOverride,\n  disableContentPaddings,\n  headerAriaLabel,\n  __internalRootRef,\n  __injectAnalyticsComponentMetadata,\n  ...props\n}: InternalExpandableSectionProps) {\n  const ref = useRef<HTMLDivElement>(null);\n  const controlId = useUniqueId();\n  const triggerControlId = `${controlId}-trigger`;\n  const descriptionId = `${controlId}-description`;\n\n  const baseProps = getBaseProps(props);\n  const [expanded, setExpanded] = useControllable(controlledExpanded, onChange, defaultExpanded, {\n    componentName: 'ExpandableSection',\n    controlledProp: 'expanded',\n    changeHandler: 'onChange',\n  });\n\n  const onExpandChange = useCallback(\n    (expanded: boolean) => {\n      setExpanded(expanded);\n      fireNonCancelableEvent(onChange, { expanded });\n    },\n    [onChange, setExpanded]\n  );\n\n  const onClick = useCallback(() => {\n    onExpandChange(!expanded);\n  }, [onExpandChange, expanded]);\n\n  const onKeyUp = useCallback(\n    (event: KeyboardEvent<Element>) => {\n      const interactionKeys = [KeyCode.enter, KeyCode.space];\n\n      if (interactionKeys.indexOf(event.keyCode) !== -1) {\n        onExpandChange(!expanded);\n      }\n    },\n    [onExpandChange, expanded]\n  );\n\n  const onKeyDown = useCallback((event: KeyboardEvent<Element>) => {\n    if (event.keyCode === KeyCode.space) {\n      // Prevent the page from scrolling when toggling the component with the space bar.\n      event.preventDefault();\n    }\n  }, []);\n\n  const triggerProps = {\n    ariaControls: controlId,\n    ariaLabel: headerAriaLabel,\n    ariaLabelledBy: headerAriaLabel ? undefined : triggerControlId,\n    onKeyUp,\n    onKeyDown,\n    onClick,\n  };\n\n  // Map stacked variant to container to avoid code duplication\n  const baseVariant: InternalVariant = variant === 'stacked' ? 'container' : variant;\n\n  return (\n    <ExpandableSectionContainer\n      {...props}\n      expanded={expanded}\n      className={clsx(baseProps.className, styles.root, analyticsSelectors.root)}\n      variant={variant}\n      disableContentPaddings={disableContentPaddings}\n      __injectAnalyticsComponentMetadata={__injectAnalyticsComponentMetadata}\n      header={\n        <ExpandableSectionHeader\n          id={triggerControlId}\n          descriptionId={descriptionId}\n          className={clsx(styles.header, styles[`header-${baseVariant}`])}\n          variant={baseVariant}\n          expanded={!!expanded}\n          header={header}\n          headerText={headerText}\n          headerDescription={headerDescription}\n          headerCounter={headerCounter}\n          headerInfo={headerInfo}\n          headerActions={headerActions}\n          headingTagOverride={headingTagOverride}\n          {...triggerProps}\n        />\n      }\n      __internalRootRef={__internalRootRef}\n    >\n      <CSSTransition in={expanded} timeout={30} classNames={{ enter: styles['content-enter'] }} nodeRef={ref}>\n        <div\n          id={controlId}\n          ref={ref}\n          className={clsx(styles.content, styles[`content-${baseVariant}`], expanded && styles['content-expanded'])}\n          role=\"group\"\n          aria-label={triggerProps.ariaLabel}\n          aria-labelledby={triggerProps.ariaLabelledBy}\n          aria-describedby={variantSupportsDescription(baseVariant) && headerDescription ? descriptionId : undefined}\n        >\n          {children}\n        </div>\n      </CSSTransition>\n    </ExpandableSectionContainer>\n  );\n}\n"], "mappings": ";AAAA;AACA;AACA,OAAOA,KAAK,IAAmBC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAOC,IAAI,MAAM,MAAM;AAEvB,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,sBAAsB,QAAQ,oBAAoB;AAE3D,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,WAAW,QAAQ,iCAAiC;AAC7D,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,uBAAuB,QAAQ,6BAA6B;AAErE,SAASC,0BAA0B,QAAQ,SAAS;AAEpD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,MAAM,MAAM,iBAAiB;AAQpC,eAAc,SAAUC,yBAAyBA,CAACC,EAkBjB;MAlBiB;MAChDC,QAAQ,EAAEC,kBAAkB;MAC5BC,eAAe;MACfC,QAAQ;MACRC,OAAO,GAAG,SAAS;MACnBC,QAAQ;MACRC,MAAM;MACNC,UAAU;MACVC,aAAa;MACbC,iBAAiB;MACjBC,UAAU;MACVC,aAAa;MACbC,kBAAkB;MAClBC,sBAAsB;MACtBC,eAAe;MACfC,iBAAiB;MACjBC;IAAkC,IAAAjB,EAEH;IAD5BkB,KAAK,GAAAC,MAAA,CAAAnB,EAAA,EAjBwC,6RAkBjD,CADS;EAER,MAAMoB,GAAG,GAAGlC,MAAM,CAAiB,IAAI,CAAC;EACxC,MAAMmC,SAAS,GAAG7B,WAAW,EAAE;EAC/B,MAAM8B,gBAAgB,GAAG,GAAGD,SAAS,UAAU;EAC/C,MAAME,aAAa,GAAG,GAAGF,SAAS,cAAc;EAEhD,MAAMG,SAAS,GAAGnC,YAAY,CAAC6B,KAAK,CAAC;EACrC,MAAM,CAACjB,QAAQ,EAAEwB,WAAW,CAAC,GAAGlC,eAAe,CAACW,kBAAkB,EAAEE,QAAQ,EAAED,eAAe,EAAE;IAC7FuB,aAAa,EAAE,mBAAmB;IAClCC,cAAc,EAAE,UAAU;IAC1BC,aAAa,EAAE;GAChB,CAAC;EAEF,MAAMC,cAAc,GAAG5C,WAAW,CAC/BgB,QAAiB,IAAI;IACpBwB,WAAW,CAACxB,QAAQ,CAAC;IACrBX,sBAAsB,CAACc,QAAQ,EAAE;MAAEH;IAAQ,CAAE,CAAC;EAChD,CAAC,EACD,CAACG,QAAQ,EAAEqB,WAAW,CAAC,CACxB;EAED,MAAMK,OAAO,GAAG7C,WAAW,CAAC,MAAK;IAC/B4C,cAAc,CAAC,CAAC5B,QAAQ,CAAC;EAC3B,CAAC,EAAE,CAAC4B,cAAc,EAAE5B,QAAQ,CAAC,CAAC;EAE9B,MAAM8B,OAAO,GAAG9C,WAAW,CACxB+C,KAA6B,IAAI;IAChC,MAAMC,eAAe,GAAG,CAACxC,OAAO,CAACyC,KAAK,EAAEzC,OAAO,CAAC0C,KAAK,CAAC;IAEtD,IAAIF,eAAe,CAACG,OAAO,CAACJ,KAAK,CAACK,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;MACjDR,cAAc,CAAC,CAAC5B,QAAQ,CAAC;;EAE7B,CAAC,EACD,CAAC4B,cAAc,EAAE5B,QAAQ,CAAC,CAC3B;EAED,MAAMqC,SAAS,GAAGrD,WAAW,CAAE+C,KAA6B,IAAI;IAC9D,IAAIA,KAAK,CAACK,OAAO,KAAK5C,OAAO,CAAC0C,KAAK,EAAE;MACnC;MACAH,KAAK,CAACO,cAAc,EAAE;;EAE1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAG;IACnBC,YAAY,EAAEpB,SAAS;IACvBqB,SAAS,EAAE3B,eAAe;IAC1B4B,cAAc,EAAE5B,eAAe,GAAG6B,SAAS,GAAGtB,gBAAgB;IAC9DS,OAAO;IACPO,SAAS;IACTR;GACD;EAED;EACA,MAAMe,WAAW,GAAoBxC,OAAO,KAAK,SAAS,GAAG,WAAW,GAAGA,OAAO;EAElF,OACErB,KAAA,CAAA8D,aAAA,CAACpD,0BAA0B,EAAAqD,MAAA,CAAAC,MAAA,KACrB9B,KAAK;IACTjB,QAAQ,EAAEA,QAAQ;IAClBgD,SAAS,EAAE7D,IAAI,CAACoC,SAAS,CAACyB,SAAS,EAAEnD,MAAM,CAACoD,IAAI,EAAErD,kBAAkB,CAACqD,IAAI,CAAC;IAC1E7C,OAAO,EAAEA,OAAO;IAChBS,sBAAsB,EAAEA,sBAAsB;IAC9CG,kCAAkC,EAAEA,kCAAkC;IACtEV,MAAM,EACJvB,KAAA,CAAA8D,aAAA,CAACnD,uBAAuB,EAAAoD,MAAA,CAAAC,MAAA;MACtBG,EAAE,EAAE7B,gBAAgB;MACpBC,aAAa,EAAEA,aAAa;MAC5B0B,SAAS,EAAE7D,IAAI,CAACU,MAAM,CAACS,MAAM,EAAET,MAAM,CAAC,UAAU+C,WAAW,EAAE,CAAC,CAAC;MAC/DxC,OAAO,EAAEwC,WAAW;MACpB5C,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBM,MAAM,EAAEA,MAAM;MACdC,UAAU,EAAEA,UAAU;MACtBE,iBAAiB,EAAEA,iBAAiB;MACpCD,aAAa,EAAEA,aAAa;MAC5BE,UAAU,EAAEA,UAAU;MACtBC,aAAa,EAAEA,aAAa;MAC5BC,kBAAkB,EAAEA;IAAkB,GAClC2B,YAAY,EAChB;IAEJxB,iBAAiB,EAAEA;EAAiB,IAEpChC,KAAA,CAAA8D,aAAA,CAAC3D,aAAa;IAACiE,EAAE,EAAEnD,QAAQ;IAAEoD,OAAO,EAAE,EAAE;IAAEC,UAAU,EAAE;MAAEpB,KAAK,EAAEpC,MAAM,CAAC,eAAe;IAAC,CAAE;IAAEyD,OAAO,EAAEnC;EAAG,GACpGpC,KAAA,CAAA8D,aAAA;IACEK,EAAE,EAAE9B,SAAS;IACbD,GAAG,EAAEA,GAAG;IACR6B,SAAS,EAAE7D,IAAI,CAACU,MAAM,CAAC0D,OAAO,EAAE1D,MAAM,CAAC,WAAW+C,WAAW,EAAE,CAAC,EAAE5C,QAAQ,IAAIH,MAAM,CAAC,kBAAkB,CAAC,CAAC;IACzG2D,IAAI,EAAC,OAAO;IAAA,cACAjB,YAAY,CAACE,SAAS;IAAA,mBACjBF,YAAY,CAACG,cAAc;IAAA,oBAC1B/C,0BAA0B,CAACiD,WAAW,CAAC,IAAInC,iBAAiB,GAAGa,aAAa,GAAGqB;EAAS,GAEzGtC,QAAQ,CACL,CACQ,CACW;AAEjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}