{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport { shouldSplitPanelBeForcedToBottom } from '../split-panel/split-panel-forced-position';\nexport const CONTENT_PADDING = 2 * 24; // space-xl\nexport function computeHorizontalLayout({\n  navigationOpen,\n  navigationWidth,\n  placement,\n  minContentWidth,\n  activeDrawerSize,\n  splitPanelOpen,\n  splitPanelPosition,\n  splitPanelSize,\n  isMobile,\n  activeGlobalDrawersSizes\n}) {\n  const activeNavigationWidth = navigationOpen ? navigationWidth : 0;\n  let resizableSpaceAvailable = Math.max(0, placement.inlineSize - minContentWidth - CONTENT_PADDING - activeNavigationWidth);\n  const totalActiveGlobalDrawersSize = Object.values(activeGlobalDrawersSizes).reduce((acc, size) => acc + size, 0);\n  const availableWidthForSplitPanel = resizableSpaceAvailable - activeDrawerSize;\n  const splitPanelForcedPosition = shouldSplitPanelBeForcedToBottom({\n    isMobile,\n    availableWidthForSplitPanel\n  });\n  const resolvedSplitPanelPosition = splitPanelForcedPosition ? 'bottom' : splitPanelPosition !== null && splitPanelPosition !== void 0 ? splitPanelPosition : 'bottom';\n  const sideSplitPanelSize = resolvedSplitPanelPosition === 'side' && splitPanelOpen ? splitPanelSize !== null && splitPanelSize !== void 0 ? splitPanelSize : 0 : 0;\n  const maxSplitPanelSize = Math.max(resizableSpaceAvailable - totalActiveGlobalDrawersSize - activeDrawerSize, 0);\n  resizableSpaceAvailable -= sideSplitPanelSize;\n  const maxDrawerSize = resizableSpaceAvailable - totalActiveGlobalDrawersSize;\n  const maxGlobalDrawersSizes = Object.keys(activeGlobalDrawersSizes).reduce((acc, drawerId) => {\n    return Object.assign(Object.assign({}, acc), {\n      [drawerId]: resizableSpaceAvailable - activeDrawerSize - totalActiveGlobalDrawersSize + activeGlobalDrawersSizes[drawerId]\n    });\n  }, {});\n  return {\n    splitPanelPosition: resolvedSplitPanelPosition,\n    splitPanelForcedPosition,\n    sideSplitPanelSize,\n    maxSplitPanelSize,\n    maxDrawerSize,\n    maxGlobalDrawersSizes,\n    totalActiveGlobalDrawersSize,\n    resizableSpaceAvailable\n  };\n}\nexport function computeVerticalLayout({\n  topOffset,\n  hasVisibleToolbar,\n  toolbarHeight,\n  stickyNotifications,\n  notificationsHeight\n}) {\n  const toolbar = topOffset;\n  let notifications = topOffset;\n  let drawers = topOffset;\n  if (hasVisibleToolbar) {\n    notifications += toolbarHeight;\n    drawers += toolbarHeight;\n  }\n  let header = notifications;\n  if (stickyNotifications) {\n    header += notificationsHeight;\n  }\n  return {\n    toolbar,\n    notifications,\n    header,\n    drawers\n  };\n}\nexport function computeSplitPanelOffsets({\n  hasSplitPanel,\n  splitPanelPosition,\n  placement,\n  splitPanelOpen,\n  splitPanelFullHeight,\n  splitPanelHeaderHeight\n}) {\n  if (!hasSplitPanel || splitPanelPosition !== 'bottom') {\n    return {\n      stickyVerticalBottomOffset: placement.insetBlockEnd,\n      mainContentPaddingBlockEnd: undefined\n    };\n  }\n  const mainContentBottomOffset = splitPanelOpen ? splitPanelFullHeight : splitPanelHeaderHeight;\n  return {\n    stickyVerticalBottomOffset: mainContentBottomOffset + placement.insetBlockEnd,\n    mainContentPaddingBlockEnd: mainContentBottomOffset\n  };\n}\nexport function getDrawerStyles(verticalOffsets, isMobile, placement) {\n  var _a;\n  const drawerTopOffset = isMobile ? verticalOffsets.toolbar : (_a = verticalOffsets.drawers) !== null && _a !== void 0 ? _a : placement.insetBlockStart;\n  const drawerHeight = `calc(100vh - ${drawerTopOffset}px - ${placement.insetBlockEnd}px)`;\n  return {\n    drawerTopOffset,\n    drawerHeight\n  };\n}", "map": {"version": 3, "names": ["shouldSplitPanelBeForcedToBottom", "CONTENT_PADDING", "computeHorizontalLayout", "navigationOpen", "navigationWidth", "placement", "minC<PERSON>nt<PERSON>id<PERSON>", "activeDrawerSize", "splitPanelOpen", "splitPanelPosition", "splitPanelSize", "isMobile", "activeGlobalDrawersSizes", "activeNavigationWidth", "resizableSpaceAvailable", "Math", "max", "inlineSize", "totalActiveGlobalDrawersSize", "Object", "values", "reduce", "acc", "size", "availableWidthForSplitPanel", "splitPanelForcedPosition", "resolvedSplitPanelPosition", "sideSplitPanelSize", "maxSplitPanelSize", "maxDrawerSize", "maxGlobalDrawersSizes", "keys", "drawerId", "assign", "computeVerticalLayout", "topOffset", "hasVisibleToolbar", "toolbarHeight", "stickyNotifications", "notificationsHeight", "toolbar", "notifications", "drawers", "header", "computeSplitPanelOffsets", "hasSplitPanel", "splitPanelFullHeight", "splitPanelHeaderHeight", "stickyVerticalBottomOffset", "insetBlockEnd", "mainContentPaddingBlockEnd", "undefined", "mainContentBottomOffset", "getDrawerStyles", "verticalOffsets", "drawerTopOffset", "_a", "insetBlockStart", "drawerHeight"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\app-layout\\visual-refresh-toolbar\\compute-layout.ts"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { AppLayoutPropsWithDefaults } from '../interfaces';\nimport { shouldSplitPanelBeForcedToBottom } from '../split-panel/split-panel-forced-position';\n\ninterface HorizontalLayoutInput {\n  navigationOpen: boolean;\n  navigationWidth: number;\n  placement: AppLayoutPropsWithDefaults['placement'];\n  minContentWidth: number;\n  activeDrawerSize: number;\n  splitPanelOpen: boolean;\n  splitPanelPosition: 'side' | 'bottom' | undefined;\n  splitPanelSize: number;\n  isMobile: boolean;\n  activeGlobalDrawersSizes: Record<string, number>;\n}\n\nexport const CONTENT_PADDING = 2 * 24; // space-xl\n\nexport function computeHorizontalLayout({\n  navigationOpen,\n  navigationWidth,\n  placement,\n  minContentWidth,\n  activeDrawerSize,\n  splitPanelOpen,\n  splitPanelPosition,\n  splitPanelSize,\n  isMobile,\n  activeGlobalDrawersSizes,\n}: HorizontalLayoutInput) {\n  const activeNavigationWidth = navigationOpen ? navigationWidth : 0;\n\n  let resizableSpaceAvailable = Math.max(\n    0,\n    placement.inlineSize - minContentWidth - CONTENT_PADDING - activeNavigationWidth\n  );\n  const totalActiveGlobalDrawersSize = Object.values(activeGlobalDrawersSizes).reduce((acc, size) => acc + size, 0);\n\n  const availableWidthForSplitPanel = resizableSpaceAvailable - activeDrawerSize;\n  const splitPanelForcedPosition = shouldSplitPanelBeForcedToBottom({\n    isMobile,\n    availableWidthForSplitPanel,\n  });\n  const resolvedSplitPanelPosition = splitPanelForcedPosition ? 'bottom' : splitPanelPosition ?? 'bottom';\n  const sideSplitPanelSize = resolvedSplitPanelPosition === 'side' && splitPanelOpen ? splitPanelSize ?? 0 : 0;\n  const maxSplitPanelSize = Math.max(resizableSpaceAvailable - totalActiveGlobalDrawersSize - activeDrawerSize, 0);\n  resizableSpaceAvailable -= sideSplitPanelSize;\n  const maxDrawerSize = resizableSpaceAvailable - totalActiveGlobalDrawersSize;\n  const maxGlobalDrawersSizes: Record<string, number> = Object.keys(activeGlobalDrawersSizes).reduce(\n    (acc, drawerId) => {\n      return {\n        ...acc,\n        [drawerId]:\n          resizableSpaceAvailable -\n          activeDrawerSize -\n          totalActiveGlobalDrawersSize +\n          activeGlobalDrawersSizes[drawerId],\n      };\n    },\n    {}\n  );\n\n  return {\n    splitPanelPosition: resolvedSplitPanelPosition,\n    splitPanelForcedPosition,\n    sideSplitPanelSize,\n    maxSplitPanelSize,\n    maxDrawerSize,\n    maxGlobalDrawersSizes,\n    totalActiveGlobalDrawersSize,\n    resizableSpaceAvailable,\n  };\n}\n\ninterface VerticalLayoutInput {\n  topOffset: number;\n  hasVisibleToolbar: boolean;\n  toolbarHeight: number;\n  stickyNotifications: boolean;\n  notificationsHeight: number;\n}\n\nexport interface VerticalLayoutOutput {\n  toolbar: number;\n  notifications: number;\n  header: number;\n  drawers: number;\n}\n\nexport function computeVerticalLayout({\n  topOffset,\n  hasVisibleToolbar,\n  toolbarHeight,\n  stickyNotifications,\n  notificationsHeight,\n}: VerticalLayoutInput): VerticalLayoutOutput {\n  const toolbar = topOffset;\n  let notifications = topOffset;\n  let drawers = topOffset;\n\n  if (hasVisibleToolbar) {\n    notifications += toolbarHeight;\n    drawers += toolbarHeight;\n  }\n  let header = notifications;\n  if (stickyNotifications) {\n    header += notificationsHeight;\n  }\n\n  return { toolbar, notifications, header, drawers };\n}\n\ninterface SplitPanelOffsetInput {\n  hasSplitPanel: boolean;\n  placement: AppLayoutPropsWithDefaults['placement'];\n  splitPanelPosition: 'bottom' | 'side';\n  splitPanelOpen: boolean;\n  splitPanelHeaderHeight: number;\n  splitPanelFullHeight: number;\n}\n\nexport function computeSplitPanelOffsets({\n  hasSplitPanel,\n  splitPanelPosition,\n  placement,\n  splitPanelOpen,\n  splitPanelFullHeight,\n  splitPanelHeaderHeight,\n}: SplitPanelOffsetInput) {\n  if (!hasSplitPanel || splitPanelPosition !== 'bottom') {\n    return {\n      stickyVerticalBottomOffset: placement.insetBlockEnd,\n      mainContentPaddingBlockEnd: undefined,\n    };\n  }\n  const mainContentBottomOffset = splitPanelOpen ? splitPanelFullHeight : splitPanelHeaderHeight;\n  return {\n    stickyVerticalBottomOffset: mainContentBottomOffset + placement.insetBlockEnd,\n    mainContentPaddingBlockEnd: mainContentBottomOffset,\n  };\n}\n\nexport function getDrawerStyles(\n  verticalOffsets: VerticalLayoutOutput,\n  isMobile: boolean,\n  placement: AppLayoutPropsWithDefaults['placement']\n): {\n  drawerTopOffset: number;\n  drawerHeight: string;\n} {\n  const drawerTopOffset = isMobile ? verticalOffsets.toolbar : verticalOffsets.drawers ?? placement.insetBlockStart;\n  const drawerHeight = `calc(100vh - ${drawerTopOffset}px - ${placement.insetBlockEnd}px)`;\n  return { drawerTopOffset, drawerHeight };\n}\n"], "mappings": "AAAA;AACA;AAGA,SAASA,gCAAgC,QAAQ,4CAA4C;AAe7F,OAAO,MAAMC,eAAe,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;AAEvC,OAAM,SAAUC,uBAAuBA,CAAC;EACtCC,cAAc;EACdC,eAAe;EACfC,SAAS;EACTC,eAAe;EACfC,gBAAgB;EAChBC,cAAc;EACdC,kBAAkB;EAClBC,cAAc;EACdC,QAAQ;EACRC;AAAwB,CACF;EACtB,MAAMC,qBAAqB,GAAGV,cAAc,GAAGC,eAAe,GAAG,CAAC;EAElE,IAAIU,uBAAuB,GAAGC,IAAI,CAACC,GAAG,CACpC,CAAC,EACDX,SAAS,CAACY,UAAU,GAAGX,eAAe,GAAGL,eAAe,GAAGY,qBAAqB,CACjF;EACD,MAAMK,4BAA4B,GAAGC,MAAM,CAACC,MAAM,CAACR,wBAAwB,CAAC,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,EAAE,CAAC,CAAC;EAEjH,MAAMC,2BAA2B,GAAGV,uBAAuB,GAAGP,gBAAgB;EAC9E,MAAMkB,wBAAwB,GAAGzB,gCAAgC,CAAC;IAChEW,QAAQ;IACRa;GACD,CAAC;EACF,MAAME,0BAA0B,GAAGD,wBAAwB,GAAG,QAAQ,GAAGhB,kBAAkB,aAAlBA,kBAAkB,cAAlBA,kBAAkB,GAAI,QAAQ;EACvG,MAAMkB,kBAAkB,GAAGD,0BAA0B,KAAK,MAAM,IAAIlB,cAAc,GAAGE,cAAc,aAAdA,cAAc,cAAdA,cAAc,GAAI,CAAC,GAAG,CAAC;EAC5G,MAAMkB,iBAAiB,GAAGb,IAAI,CAACC,GAAG,CAACF,uBAAuB,GAAGI,4BAA4B,GAAGX,gBAAgB,EAAE,CAAC,CAAC;EAChHO,uBAAuB,IAAIa,kBAAkB;EAC7C,MAAME,aAAa,GAAGf,uBAAuB,GAAGI,4BAA4B;EAC5E,MAAMY,qBAAqB,GAA2BX,MAAM,CAACY,IAAI,CAACnB,wBAAwB,CAAC,CAACS,MAAM,CAChG,CAACC,GAAG,EAAEU,QAAQ,KAAI;IAChB,OAAAb,MAAA,CAAAc,MAAA,CAAAd,MAAA,CAAAc,MAAA,KACKX,GAAG;MACN,CAACU,QAAQ,GACPlB,uBAAuB,GACvBP,gBAAgB,GAChBW,4BAA4B,GAC5BN,wBAAwB,CAACoB,QAAQ;IAAC;EAExC,CAAC,EACD,EAAE,CACH;EAED,OAAO;IACLvB,kBAAkB,EAAEiB,0BAA0B;IAC9CD,wBAAwB;IACxBE,kBAAkB;IAClBC,iBAAiB;IACjBC,aAAa;IACbC,qBAAqB;IACrBZ,4BAA4B;IAC5BJ;GACD;AACH;AAiBA,OAAM,SAAUoB,qBAAqBA,CAAC;EACpCC,SAAS;EACTC,iBAAiB;EACjBC,aAAa;EACbC,mBAAmB;EACnBC;AAAmB,CACC;EACpB,MAAMC,OAAO,GAAGL,SAAS;EACzB,IAAIM,aAAa,GAAGN,SAAS;EAC7B,IAAIO,OAAO,GAAGP,SAAS;EAEvB,IAAIC,iBAAiB,EAAE;IACrBK,aAAa,IAAIJ,aAAa;IAC9BK,OAAO,IAAIL,aAAa;;EAE1B,IAAIM,MAAM,GAAGF,aAAa;EAC1B,IAAIH,mBAAmB,EAAE;IACvBK,MAAM,IAAIJ,mBAAmB;;EAG/B,OAAO;IAAEC,OAAO;IAAEC,aAAa;IAAEE,MAAM;IAAED;EAAO,CAAE;AACpD;AAWA,OAAM,SAAUE,wBAAwBA,CAAC;EACvCC,aAAa;EACbpC,kBAAkB;EAClBJ,SAAS;EACTG,cAAc;EACdsC,oBAAoB;EACpBC;AAAsB,CACA;EACtB,IAAI,CAACF,aAAa,IAAIpC,kBAAkB,KAAK,QAAQ,EAAE;IACrD,OAAO;MACLuC,0BAA0B,EAAE3C,SAAS,CAAC4C,aAAa;MACnDC,0BAA0B,EAAEC;KAC7B;;EAEH,MAAMC,uBAAuB,GAAG5C,cAAc,GAAGsC,oBAAoB,GAAGC,sBAAsB;EAC9F,OAAO;IACLC,0BAA0B,EAAEI,uBAAuB,GAAG/C,SAAS,CAAC4C,aAAa;IAC7EC,0BAA0B,EAAEE;GAC7B;AACH;AAEA,OAAM,SAAUC,eAAeA,CAC7BC,eAAqC,EACrC3C,QAAiB,EACjBN,SAAkD;;EAKlD,MAAMkD,eAAe,GAAG5C,QAAQ,GAAG2C,eAAe,CAACd,OAAO,GAAG,CAAAgB,EAAA,GAAAF,eAAe,CAACZ,OAAO,cAAAc,EAAA,cAAAA,EAAA,GAAInD,SAAS,CAACoD,eAAe;EACjH,MAAMC,YAAY,GAAG,gBAAgBH,eAAe,QAAQlD,SAAS,CAAC4C,aAAa,KAAK;EACxF,OAAO;IAAEM,eAAe;IAAEG;EAAY,CAAE;AAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}