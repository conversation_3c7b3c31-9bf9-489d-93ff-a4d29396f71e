{"ast": null, "code": "var _s = $RefreshSig$();\n// src/hooks/useNavigation.js\nimport { useState, useEffect } from 'react';\nconst LAST_SELECTED_MENU = 'last_selected_menu';\nexport function useNavigation() {\n  _s();\n  const [items, setItems] = useState([]);\n  const [routes, setRoutes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [lastSelectedItem, setLastSelectedItem] = useState(() => {\n    // Initialize from localStorage\n    const saved = localStorage.getItem(LAST_SELECTED_MENU);\n    return saved ? JSON.parse(saved) : null;\n  });\n  function transformNavigationData(data) {\n    const items = typeof data.body === 'string' ? JSON.parse(data.body) : data.body;\n\n    // First, let's separate the Documentation item\n    const documentationItem = items.find(item => item.text === \"Documentation\");\n    const regularItems = items.filter(item => item.text !== \"Documentation\");\n    const buildNavigationTree = (parentId = null) => {\n      const children = regularItems.filter(item => item.parentId === parentId).sort((a, b) => a.orderIndex - b.orderIndex);\n\n      // Create the main navigation items\n      let navigationItems = children.map(item => {\n        const subItems = buildNavigationTree(item.id);\n        if (subItems.length > 0) {\n          return {\n            type: \"expandable-link-group\",\n            text: item.text,\n            href: item.href,\n            id: item.id,\n            items: subItems\n          };\n        }\n        return {\n          type: \"link\",\n          text: item.text,\n          href: item.href,\n          id: item.id\n        };\n      });\n      return navigationItems;\n    };\n\n    // Build the main navigation tree\n    let navigationItems = buildNavigationTree(null);\n\n    // Add divider and Documentation link at the very end\n    if (documentationItem) {\n      navigationItems.push({\n        type: \"divider\"\n      }, {\n        type: \"link\",\n        text: documentationItem.text,\n        href: documentationItem.href,\n        id: documentationItem.id\n      });\n    }\n    const extractRoutes = items => {\n      return items.reduce((acc, item) => {\n        acc.push({\n          path: item.href,\n          title: item.text,\n          id: item.id\n        });\n        return acc;\n      }, []);\n    };\n    return {\n      navigationItems: navigationItems,\n      routes: extractRoutes(items)\n    };\n  }\n\n  // Function to find parent IDs of an item\n  const findParentIds = (items, targetId) => {\n    const parentIds = [];\n    const findParent = (items, targetId) => {\n      for (const item of items) {\n        if (item.items) {\n          if (item.items.some(subItem => subItem.id === targetId)) {\n            parentIds.push(item.id);\n          }\n          findParent(item.items, targetId);\n        }\n      }\n    };\n    findParent(items, targetId);\n    return parentIds;\n  };\n\n  // Save last selected item\n  const saveLastSelected = item => {\n    setLastSelectedItem(item);\n    localStorage.setItem(LAST_SELECTED_MENU, JSON.stringify(item));\n  };\n  useEffect(() => {\n    const fetchNavigation = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('https://9dn7gxcgyf.execute-api.us-east-1.amazonaws.com/dev/Navigation');\n        if (!response.ok) {\n          throw new Error('Network response was not ok');\n        }\n        const data = await response.json();\n        const {\n          navigationItems,\n          routes\n        } = transformNavigationData(data);\n        setItems(navigationItems);\n        setRoutes(routes);\n      } catch (err) {\n        setError(err.message);\n        console.error('Error fetching navigation:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchNavigation();\n  }, []);\n  return {\n    items,\n    routes,\n    loading,\n    error,\n    lastSelectedItem,\n    saveLastSelected,\n    findParentIds\n  };\n}\n_s(useNavigation, \"0bknbUlsXXsOsj/hkcxIRAtewAA=\");", "map": {"version": 3, "names": ["useState", "useEffect", "LAST_SELECTED_MENU", "useNavigation", "_s", "items", "setItems", "routes", "setRoutes", "loading", "setLoading", "error", "setError", "lastSelectedItem", "setLastSelectedItem", "saved", "localStorage", "getItem", "JSON", "parse", "transformNavigationData", "data", "body", "documentationItem", "find", "item", "text", "regularItems", "filter", "buildNavigationTree", "parentId", "children", "sort", "a", "b", "orderIndex", "navigationItems", "map", "subItems", "id", "length", "type", "href", "push", "extractRoutes", "reduce", "acc", "path", "title", "findParentIds", "targetId", "parentIds", "findParent", "some", "subItem", "saveLastSelected", "setItem", "stringify", "fetchNavigation", "response", "fetch", "ok", "Error", "json", "err", "message", "console"], "sources": ["C:/Repos2025/Amazonians_App/App/src/hooks/useNavigation.js"], "sourcesContent": ["// src/hooks/useNavigation.js\r\nimport { useState, useEffect } from 'react';\r\n\r\nconst LAST_SELECTED_MENU = 'last_selected_menu';\r\n\r\nexport function useNavigation() {\r\n  const [items, setItems] = useState([]);\r\n  const [routes, setRoutes] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [lastSelectedItem, setLastSelectedItem] = useState(() => {\r\n    // Initialize from localStorage\r\n    const saved = localStorage.getItem(LAST_SELECTED_MENU);\r\n    return saved ? JSON.parse(saved) : null;\r\n  });\r\n\r\n  function transformNavigationData(data) {\r\n    const items = typeof data.body === 'string' ? JSON.parse(data.body) : data.body;\r\n    \r\n    // First, let's separate the Documentation item\r\n    const documentationItem = items.find(item => item.text === \"Documentation\");\r\n    const regularItems = items.filter(item => item.text !== \"Documentation\");\r\n    \r\n    const buildNavigationTree = (parentId = null) => {\r\n      const children = regularItems\r\n        .filter(item => item.parentId === parentId)\r\n        .sort((a, b) => a.orderIndex - b.orderIndex);\r\n  \r\n      // Create the main navigation items\r\n      let navigationItems = children.map(item => {\r\n        const subItems = buildNavigationTree(item.id);\r\n        \r\n        if (subItems.length > 0) {\r\n          return {\r\n            type: \"expandable-link-group\",\r\n            text: item.text,\r\n            href: item.href,\r\n            id: item.id,\r\n            items: subItems\r\n          };\r\n        }\r\n        \r\n        return {\r\n          type: \"link\",\r\n          text: item.text,\r\n          href: item.href,\r\n          id: item.id\r\n        };\r\n      });\r\n  \r\n      return navigationItems;\r\n    };\r\n  \r\n    // Build the main navigation tree\r\n    let navigationItems = buildNavigationTree(null);\r\n  \r\n    // Add divider and Documentation link at the very end\r\n    if (documentationItem) {\r\n      navigationItems.push(\r\n        {\r\n          type: \"divider\"\r\n        },\r\n        {\r\n          type: \"link\",\r\n          text: documentationItem.text,\r\n          href: documentationItem.href,\r\n          id: documentationItem.id\r\n        }\r\n      );\r\n    }\r\n  \r\n    const extractRoutes = (items) => {\r\n      return items.reduce((acc, item) => {\r\n        acc.push({\r\n          path: item.href,\r\n          title: item.text,\r\n          id: item.id\r\n        });\r\n        return acc;\r\n      }, []);\r\n    };\r\n  \r\n    return {\r\n      navigationItems: navigationItems,\r\n      routes: extractRoutes(items)\r\n    };\r\n  }\r\n\r\n  // Function to find parent IDs of an item\r\n  const findParentIds = (items, targetId) => {\r\n    const parentIds = [];\r\n    \r\n    const findParent = (items, targetId) => {\r\n      for (const item of items) {\r\n        if (item.items) {\r\n          if (item.items.some(subItem => subItem.id === targetId)) {\r\n            parentIds.push(item.id);\r\n          }\r\n          findParent(item.items, targetId);\r\n        }\r\n      }\r\n    };\r\n\r\n    findParent(items, targetId);\r\n    return parentIds;\r\n  };\r\n\r\n  // Save last selected item\r\n  const saveLastSelected = (item) => {\r\n    setLastSelectedItem(item);\r\n    localStorage.setItem(LAST_SELECTED_MENU, JSON.stringify(item));\r\n  };\r\n\r\n  useEffect(() => {\r\n    const fetchNavigation = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await fetch('https://9dn7gxcgyf.execute-api.us-east-1.amazonaws.com/dev/Navigation');\r\n        if (!response.ok) {\r\n          throw new Error('Network response was not ok');\r\n        }\r\n        const data = await response.json();\r\n        const { navigationItems, routes } = transformNavigationData(data);\r\n        setItems(navigationItems);\r\n        setRoutes(routes);\r\n      } catch (err) {\r\n        setError(err.message);\r\n        console.error('Error fetching navigation:', err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchNavigation();\r\n  }, []);\r\n\r\n  return { \r\n    items, \r\n    routes, \r\n    loading, \r\n    error, \r\n    lastSelectedItem,\r\n    saveLastSelected,\r\n    findParentIds \r\n  };\r\n}\r\n"], "mappings": ";AAAA;AACA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAE3C,MAAMC,kBAAkB,GAAG,oBAAoB;AAE/C,OAAO,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EAC9B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGN,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACa,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGd,QAAQ,CAAC,MAAM;IAC7D;IACA,MAAMe,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACf,kBAAkB,CAAC;IACtD,OAAOa,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,IAAI;EACzC,CAAC,CAAC;EAEF,SAASK,uBAAuBA,CAACC,IAAI,EAAE;IACrC,MAAMhB,KAAK,GAAG,OAAOgB,IAAI,CAACC,IAAI,KAAK,QAAQ,GAAGJ,IAAI,CAACC,KAAK,CAACE,IAAI,CAACC,IAAI,CAAC,GAAGD,IAAI,CAACC,IAAI;;IAE/E;IACA,MAAMC,iBAAiB,GAAGlB,KAAK,CAACmB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,eAAe,CAAC;IAC3E,MAAMC,YAAY,GAAGtB,KAAK,CAACuB,MAAM,CAACH,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAK,eAAe,CAAC;IAExE,MAAMG,mBAAmB,GAAGA,CAACC,QAAQ,GAAG,IAAI,KAAK;MAC/C,MAAMC,QAAQ,GAAGJ,YAAY,CAC1BC,MAAM,CAACH,IAAI,IAAIA,IAAI,CAACK,QAAQ,KAAKA,QAAQ,CAAC,CAC1CE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,UAAU,GAAGD,CAAC,CAACC,UAAU,CAAC;;MAE9C;MACA,IAAIC,eAAe,GAAGL,QAAQ,CAACM,GAAG,CAACZ,IAAI,IAAI;QACzC,MAAMa,QAAQ,GAAGT,mBAAmB,CAACJ,IAAI,CAACc,EAAE,CAAC;QAE7C,IAAID,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;UACvB,OAAO;YACLC,IAAI,EAAE,uBAAuB;YAC7Bf,IAAI,EAAED,IAAI,CAACC,IAAI;YACfgB,IAAI,EAAEjB,IAAI,CAACiB,IAAI;YACfH,EAAE,EAAEd,IAAI,CAACc,EAAE;YACXlC,KAAK,EAAEiC;UACT,CAAC;QACH;QAEA,OAAO;UACLG,IAAI,EAAE,MAAM;UACZf,IAAI,EAAED,IAAI,CAACC,IAAI;UACfgB,IAAI,EAAEjB,IAAI,CAACiB,IAAI;UACfH,EAAE,EAAEd,IAAI,CAACc;QACX,CAAC;MACH,CAAC,CAAC;MAEF,OAAOH,eAAe;IACxB,CAAC;;IAED;IACA,IAAIA,eAAe,GAAGP,mBAAmB,CAAC,IAAI,CAAC;;IAE/C;IACA,IAAIN,iBAAiB,EAAE;MACrBa,eAAe,CAACO,IAAI,CAClB;QACEF,IAAI,EAAE;MACR,CAAC,EACD;QACEA,IAAI,EAAE,MAAM;QACZf,IAAI,EAAEH,iBAAiB,CAACG,IAAI;QAC5BgB,IAAI,EAAEnB,iBAAiB,CAACmB,IAAI;QAC5BH,EAAE,EAAEhB,iBAAiB,CAACgB;MACxB,CACF,CAAC;IACH;IAEA,MAAMK,aAAa,GAAIvC,KAAK,IAAK;MAC/B,OAAOA,KAAK,CAACwC,MAAM,CAAC,CAACC,GAAG,EAAErB,IAAI,KAAK;QACjCqB,GAAG,CAACH,IAAI,CAAC;UACPI,IAAI,EAAEtB,IAAI,CAACiB,IAAI;UACfM,KAAK,EAAEvB,IAAI,CAACC,IAAI;UAChBa,EAAE,EAAEd,IAAI,CAACc;QACX,CAAC,CAAC;QACF,OAAOO,GAAG;MACZ,CAAC,EAAE,EAAE,CAAC;IACR,CAAC;IAED,OAAO;MACLV,eAAe,EAAEA,eAAe;MAChC7B,MAAM,EAAEqC,aAAa,CAACvC,KAAK;IAC7B,CAAC;EACH;;EAEA;EACA,MAAM4C,aAAa,GAAGA,CAAC5C,KAAK,EAAE6C,QAAQ,KAAK;IACzC,MAAMC,SAAS,GAAG,EAAE;IAEpB,MAAMC,UAAU,GAAGA,CAAC/C,KAAK,EAAE6C,QAAQ,KAAK;MACtC,KAAK,MAAMzB,IAAI,IAAIpB,KAAK,EAAE;QACxB,IAAIoB,IAAI,CAACpB,KAAK,EAAE;UACd,IAAIoB,IAAI,CAACpB,KAAK,CAACgD,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACf,EAAE,KAAKW,QAAQ,CAAC,EAAE;YACvDC,SAAS,CAACR,IAAI,CAAClB,IAAI,CAACc,EAAE,CAAC;UACzB;UACAa,UAAU,CAAC3B,IAAI,CAACpB,KAAK,EAAE6C,QAAQ,CAAC;QAClC;MACF;IACF,CAAC;IAEDE,UAAU,CAAC/C,KAAK,EAAE6C,QAAQ,CAAC;IAC3B,OAAOC,SAAS;EAClB,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAI9B,IAAI,IAAK;IACjCX,mBAAmB,CAACW,IAAI,CAAC;IACzBT,YAAY,CAACwC,OAAO,CAACtD,kBAAkB,EAAEgB,IAAI,CAACuC,SAAS,CAAChC,IAAI,CAAC,CAAC;EAChE,CAAC;EAEDxB,SAAS,CAAC,MAAM;IACd,MAAMyD,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACFhD,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMiD,QAAQ,GAAG,MAAMC,KAAK,CAAC,uEAAuE,CAAC;QACrG,IAAI,CAACD,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;QAChD;QACA,MAAMzC,IAAI,GAAG,MAAMsC,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClC,MAAM;UAAE3B,eAAe;UAAE7B;QAAO,CAAC,GAAGa,uBAAuB,CAACC,IAAI,CAAC;QACjEf,QAAQ,CAAC8B,eAAe,CAAC;QACzB5B,SAAS,CAACD,MAAM,CAAC;MACnB,CAAC,CAAC,OAAOyD,GAAG,EAAE;QACZpD,QAAQ,CAACoD,GAAG,CAACC,OAAO,CAAC;QACrBC,OAAO,CAACvD,KAAK,CAAC,4BAA4B,EAAEqD,GAAG,CAAC;MAClD,CAAC,SAAS;QACRtD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDgD,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLrD,KAAK;IACLE,MAAM;IACNE,OAAO;IACPE,KAAK;IACLE,gBAAgB;IAChB0C,gBAAgB;IAChBN;EACF,CAAC;AACH;AAAC7C,EAAA,CA5IeD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}