{"ast": null, "code": "import { __rest } from \"tslib\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React from 'react';\nimport clsx from 'clsx';\nimport { warnOnce } from '@cloudscape-design/component-toolkit/internal';\nimport { getBaseProps } from '../../base-component';\nimport { isDevelopment } from '../../is-development';\nimport { Description, FilteringTags, Label, LabelTag, OptionIcon, Tags } from './option-parts';\nimport styles from './styles.css.js';\nfunction validateStringValue(value, propertyName) {\n  if (typeof value !== 'undefined' && typeof value !== 'string') {\n    warnOnce('DropdownOption', `This component only supports string values, but \"option.${propertyName}\" has ${typeof value} type. The component may work incorrectly.`);\n  }\n}\nconst Option = _a => {\n  var _b, _c, _d, _e;\n  var {\n      option,\n      highlightText,\n      triggerVariant = false,\n      isGroupOption = false,\n      isGenericGroup = true,\n      highlightedOption = false,\n      selectedOption = false\n    } = _a,\n    restProps = __rest(_a, [\"option\", \"highlightText\", \"triggerVariant\", \"isGroupOption\", \"isGenericGroup\", \"highlightedOption\", \"selectedOption\"]);\n  if (!option) {\n    return null;\n  }\n  const {\n    disabled\n  } = option;\n  const baseProps = getBaseProps(restProps);\n  if (isDevelopment) {\n    validateStringValue(option.label, 'label');\n    validateStringValue(option.description, 'description');\n    validateStringValue(option.labelTag, 'labelTag');\n    (_b = option.tags) === null || _b === void 0 ? void 0 : _b.forEach((tag, index) => {\n      validateStringValue(tag, `tags[${index}]`);\n    });\n    (_c = option.filteringTags) === null || _c === void 0 ? void 0 : _c.forEach((tag, index) => {\n      validateStringValue(tag, `filteringTags[${index}]`);\n    });\n  }\n  const className = clsx(styles.option, disabled && styles.disabled, isGroupOption && styles.parent, highlightedOption && styles.highlighted);\n  const icon = option.__customIcon || React.createElement(OptionIcon, {\n    name: option.iconName,\n    url: option.iconUrl,\n    svg: option.iconSvg,\n    alt: option.iconAlt,\n    size: option.description || option.tags ? 'big' : 'normal'\n  });\n  // The option is conditionally assigned 'title' and 'aria-disabled' attributes to ensure it is viewed as a (generic) group  by assistive technology only when necessary.\n  // Omitting the props might be necessary if they are provided on the parent element to avoid nested groups.\n  // See https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/generic_role\n  const genericGroupProps = isGenericGroup ? {\n    title: (_d = option.label) !== null && _d !== void 0 ? _d : option.value,\n    'aria-disabled': disabled\n  } : undefined;\n  return React.createElement(\"span\", Object.assign({\n    \"data-value\": option.value,\n    className: className,\n    lang: option.lang\n  }, genericGroupProps, baseProps), icon, React.createElement(\"span\", {\n    className: styles.content\n  }, React.createElement(\"span\", {\n    className: styles['label-content']\n  }, React.createElement(Label, {\n    label: (_e = option.label) !== null && _e !== void 0 ? _e : option.value,\n    prefix: option.__labelPrefix,\n    highlightText: highlightText,\n    triggerVariant: triggerVariant\n  }), React.createElement(LabelTag, {\n    labelTag: option.labelTag,\n    highlightText: highlightText,\n    triggerVariant: triggerVariant\n  })), React.createElement(Description, {\n    description: option.description,\n    highlightedOption: highlightedOption,\n    selectedOption: selectedOption,\n    highlightText: highlightText,\n    triggerVariant: triggerVariant\n  }), React.createElement(Tags, {\n    tags: option.tags,\n    highlightedOption: highlightedOption,\n    selectedOption: selectedOption,\n    highlightText: highlightText,\n    triggerVariant: triggerVariant\n  }), React.createElement(FilteringTags, {\n    filteringTags: option.filteringTags,\n    highlightedOption: highlightedOption,\n    selectedOption: selectedOption,\n    highlightText: highlightText,\n    triggerVariant: triggerVariant\n  })));\n};\nexport default Option;", "map": {"version": 3, "names": ["React", "clsx", "warnOnce", "getBaseProps", "isDevelopment", "Description", "FilteringTags", "Label", "LabelTag", "OptionIcon", "Tags", "styles", "validateStringValue", "value", "propertyName", "Option", "_a", "option", "highlightText", "triggerVariant", "isGroupOption", "isGenericGroup", "highlightedOption", "selectedOption", "restProps", "__rest", "disabled", "baseProps", "label", "description", "labelTag", "_b", "tags", "for<PERSON>ach", "tag", "index", "_c", "filteringTags", "className", "parent", "highlighted", "icon", "__customIcon", "createElement", "name", "iconName", "url", "iconUrl", "svg", "iconSvg", "alt", "iconAlt", "size", "genericGroupProps", "title", "_d", "undefined", "Object", "assign", "lang", "content", "_e", "prefix", "__labelPrefix"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\internal\\components\\option\\index.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React from 'react';\nimport clsx from 'clsx';\n\nimport { warnOnce } from '@cloudscape-design/component-toolkit/internal';\n\nimport { getBaseProps } from '../../base-component';\nimport { isDevelopment } from '../../is-development';\nimport { OptionProps } from './interfaces';\nimport { Description, FilteringTags, Label, LabelTag, OptionIcon, Tags } from './option-parts';\n\nimport styles from './styles.css.js';\n\nexport { OptionProps };\n\nfunction validateStringValue(value: string | undefined, propertyName: string) {\n  if (typeof value !== 'undefined' && typeof value !== 'string') {\n    warnOnce(\n      'DropdownOption',\n      `This component only supports string values, but \"option.${propertyName}\" has ${typeof value} type. The component may work incorrectly.`\n    );\n  }\n}\n\nconst Option = ({\n  option,\n  highlightText,\n  triggerVariant = false,\n  isGroupOption = false,\n  isGenericGroup = true,\n  highlightedOption = false,\n  selectedOption = false,\n  ...restProps\n}: OptionProps) => {\n  if (!option) {\n    return null;\n  }\n  const { disabled } = option;\n  const baseProps = getBaseProps(restProps);\n\n  if (isDevelopment) {\n    validateStringValue(option.label, 'label');\n    validateStringValue(option.description, 'description');\n    validateStringValue(option.labelTag, 'labelTag');\n    option.tags?.forEach((tag, index) => {\n      validateStringValue(tag, `tags[${index}]`);\n    });\n    option.filteringTags?.forEach((tag, index) => {\n      validateStringValue(tag, `filteringTags[${index}]`);\n    });\n  }\n\n  const className = clsx(\n    styles.option,\n    disabled && styles.disabled,\n    isGroupOption && styles.parent,\n    highlightedOption && styles.highlighted\n  );\n\n  const icon = option.__customIcon || (\n    <OptionIcon\n      name={option.iconName}\n      url={option.iconUrl}\n      svg={option.iconSvg}\n      alt={option.iconAlt}\n      size={option.description || option.tags ? 'big' : 'normal'}\n    />\n  );\n\n  // The option is conditionally assigned 'title' and 'aria-disabled' attributes to ensure it is viewed as a (generic) group  by assistive technology only when necessary.\n  // Omitting the props might be necessary if they are provided on the parent element to avoid nested groups.\n  // See https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/generic_role\n  const genericGroupProps = isGenericGroup\n    ? {\n        title: option.label ?? option.value,\n        'aria-disabled': disabled,\n      }\n    : undefined;\n\n  return (\n    <span data-value={option.value} className={className} lang={option.lang} {...genericGroupProps} {...baseProps}>\n      {icon}\n      <span className={styles.content}>\n        <span className={styles['label-content']}>\n          <Label\n            label={option.label ?? option.value}\n            prefix={option.__labelPrefix}\n            highlightText={highlightText}\n            triggerVariant={triggerVariant}\n          />\n          <LabelTag labelTag={option.labelTag} highlightText={highlightText} triggerVariant={triggerVariant} />\n        </span>\n        <Description\n          description={option.description}\n          highlightedOption={highlightedOption}\n          selectedOption={selectedOption}\n          highlightText={highlightText}\n          triggerVariant={triggerVariant}\n        />\n        <Tags\n          tags={option.tags}\n          highlightedOption={highlightedOption}\n          selectedOption={selectedOption}\n          highlightText={highlightText}\n          triggerVariant={triggerVariant}\n        />\n        <FilteringTags\n          filteringTags={option.filteringTags}\n          highlightedOption={highlightedOption}\n          selectedOption={selectedOption}\n          highlightText={highlightText}\n          triggerVariant={triggerVariant}\n        />\n      </span>\n    </span>\n  );\n};\n\nexport default Option;\n"], "mappings": ";AAAA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,MAAM;AAEvB,SAASC,QAAQ,QAAQ,+CAA+C;AAExE,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,aAAa,QAAQ,sBAAsB;AAEpD,SAASC,WAAW,EAAEC,aAAa,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,IAAI,QAAQ,gBAAgB;AAE9F,OAAOC,MAAM,MAAM,iBAAiB;AAIpC,SAASC,mBAAmBA,CAACC,KAAyB,EAAEC,YAAoB;EAC1E,IAAI,OAAOD,KAAK,KAAK,WAAW,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7DX,QAAQ,CACN,gBAAgB,EAChB,2DAA2DY,YAAY,SAAS,OAAOD,KAAK,4CAA4C,CACzI;;AAEL;AAEA,MAAME,MAAM,GAAIC,EASF,IAAI;;MATF;MACdC,MAAM;MACNC,aAAa;MACbC,cAAc,GAAG,KAAK;MACtBC,aAAa,GAAG,KAAK;MACrBC,cAAc,GAAG,IAAI;MACrBC,iBAAiB,GAAG,KAAK;MACzBC,cAAc,GAAG;IAAK,IAAAP,EAEV;IADTQ,SAAS,GAAAC,MAAA,CAAAT,EAAA,EARE,uHASf,CADa;EAEZ,IAAI,CAACC,MAAM,EAAE;IACX,OAAO,IAAI;;EAEb,MAAM;IAAES;EAAQ,CAAE,GAAGT,MAAM;EAC3B,MAAMU,SAAS,GAAGxB,YAAY,CAACqB,SAAS,CAAC;EAEzC,IAAIpB,aAAa,EAAE;IACjBQ,mBAAmB,CAACK,MAAM,CAACW,KAAK,EAAE,OAAO,CAAC;IAC1ChB,mBAAmB,CAACK,MAAM,CAACY,WAAW,EAAE,aAAa,CAAC;IACtDjB,mBAAmB,CAACK,MAAM,CAACa,QAAQ,EAAE,UAAU,CAAC;IAChD,CAAAC,EAAA,GAAAd,MAAM,CAACe,IAAI,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,OAAO,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAI;MAClCvB,mBAAmB,CAACsB,GAAG,EAAE,QAAQC,KAAK,GAAG,CAAC;IAC5C,CAAC,CAAC;IACF,CAAAC,EAAA,GAAAnB,MAAM,CAACoB,aAAa,cAAAD,EAAA,uBAAAA,EAAA,CAAEH,OAAO,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAI;MAC3CvB,mBAAmB,CAACsB,GAAG,EAAE,iBAAiBC,KAAK,GAAG,CAAC;IACrD,CAAC,CAAC;;EAGJ,MAAMG,SAAS,GAAGrC,IAAI,CACpBU,MAAM,CAACM,MAAM,EACbS,QAAQ,IAAIf,MAAM,CAACe,QAAQ,EAC3BN,aAAa,IAAIT,MAAM,CAAC4B,MAAM,EAC9BjB,iBAAiB,IAAIX,MAAM,CAAC6B,WAAW,CACxC;EAED,MAAMC,IAAI,GAAGxB,MAAM,CAACyB,YAAY,IAC9B1C,KAAA,CAAA2C,aAAA,CAAClC,UAAU;IACTmC,IAAI,EAAE3B,MAAM,CAAC4B,QAAQ;IACrBC,GAAG,EAAE7B,MAAM,CAAC8B,OAAO;IACnBC,GAAG,EAAE/B,MAAM,CAACgC,OAAO;IACnBC,GAAG,EAAEjC,MAAM,CAACkC,OAAO;IACnBC,IAAI,EAAEnC,MAAM,CAACY,WAAW,IAAIZ,MAAM,CAACe,IAAI,GAAG,KAAK,GAAG;EAAQ,EAE7D;EAED;EACA;EACA;EACA,MAAMqB,iBAAiB,GAAGhC,cAAc,GACpC;IACEiC,KAAK,EAAE,CAAAC,EAAA,GAAAtC,MAAM,CAACW,KAAK,cAAA2B,EAAA,cAAAA,EAAA,GAAItC,MAAM,CAACJ,KAAK;IACnC,eAAe,EAAEa;GAClB,GACD8B,SAAS;EAEb,OACExD,KAAA,CAAA2C,aAAA,SAAAc,MAAA,CAAAC,MAAA;IAAA,cAAkBzC,MAAM,CAACJ,KAAK;IAAEyB,SAAS,EAAEA,SAAS;IAAEqB,IAAI,EAAE1C,MAAM,CAAC0C;EAAI,GAAMN,iBAAiB,EAAM1B,SAAS,GAC1Gc,IAAI,EACLzC,KAAA,CAAA2C,aAAA;IAAML,SAAS,EAAE3B,MAAM,CAACiD;EAAO,GAC7B5D,KAAA,CAAA2C,aAAA;IAAML,SAAS,EAAE3B,MAAM,CAAC,eAAe;EAAC,GACtCX,KAAA,CAAA2C,aAAA,CAACpC,KAAK;IACJqB,KAAK,EAAE,CAAAiC,EAAA,GAAA5C,MAAM,CAACW,KAAK,cAAAiC,EAAA,cAAAA,EAAA,GAAI5C,MAAM,CAACJ,KAAK;IACnCiD,MAAM,EAAE7C,MAAM,CAAC8C,aAAa;IAC5B7C,aAAa,EAAEA,aAAa;IAC5BC,cAAc,EAAEA;EAAc,EAC9B,EACFnB,KAAA,CAAA2C,aAAA,CAACnC,QAAQ;IAACsB,QAAQ,EAAEb,MAAM,CAACa,QAAQ;IAAEZ,aAAa,EAAEA,aAAa;IAAEC,cAAc,EAAEA;EAAc,EAAI,CAChG,EACPnB,KAAA,CAAA2C,aAAA,CAACtC,WAAW;IACVwB,WAAW,EAAEZ,MAAM,CAACY,WAAW;IAC/BP,iBAAiB,EAAEA,iBAAiB;IACpCC,cAAc,EAAEA,cAAc;IAC9BL,aAAa,EAAEA,aAAa;IAC5BC,cAAc,EAAEA;EAAc,EAC9B,EACFnB,KAAA,CAAA2C,aAAA,CAACjC,IAAI;IACHsB,IAAI,EAAEf,MAAM,CAACe,IAAI;IACjBV,iBAAiB,EAAEA,iBAAiB;IACpCC,cAAc,EAAEA,cAAc;IAC9BL,aAAa,EAAEA,aAAa;IAC5BC,cAAc,EAAEA;EAAc,EAC9B,EACFnB,KAAA,CAAA2C,aAAA,CAACrC,aAAa;IACZ+B,aAAa,EAAEpB,MAAM,CAACoB,aAAa;IACnCf,iBAAiB,EAAEA,iBAAiB;IACpCC,cAAc,EAAEA,cAAc;IAC9BL,aAAa,EAAEA,aAAa;IAC5BC,cAAc,EAAEA;EAAc,EAC9B,CACG,CACF;AAEX,CAAC;AAED,eAAeJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}