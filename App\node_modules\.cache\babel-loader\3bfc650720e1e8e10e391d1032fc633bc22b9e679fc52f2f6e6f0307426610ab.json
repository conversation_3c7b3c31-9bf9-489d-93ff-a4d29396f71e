{"ast": null, "code": "import { __rest } from \"tslib\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { forwardRef, memo, useEffect, useRef } from 'react';\nimport clsx from 'clsx';\nimport InternalExpandableSection from '../../../expandable-section/internal';\nimport { getBaseProps } from '../../base-component';\nimport { useMergeRefs } from '../../hooks/use-merge-refs';\nimport ChartSeriesMarker from '../chart-series-marker';\nimport getSeriesDetailsText from './series-details-text';\nimport styles from './styles.css.js';\nexport default memo(forwardRef(ChartSeriesDetails));\nfunction ChartSeriesDetails(_a, ref) {\n  var {\n      details,\n      expandedSeries,\n      setPopoverText,\n      setExpandedState,\n      compactList\n    } = _a,\n    restProps = __rest(_a, [\"details\", \"expandedSeries\", \"setPopoverText\", \"setExpandedState\", \"compactList\"]);\n  const baseProps = getBaseProps(restProps);\n  const className = clsx(baseProps.className, styles.root);\n  const detailsRef = useRef(null);\n  const mergedRef = useMergeRefs(ref, detailsRef);\n  // Once the component has rendered, pass its content in plain text\n  // so that it can be used by screen readers.\n  useEffect(() => {\n    if (setPopoverText) {\n      if (detailsRef.current) {\n        setPopoverText(getSeriesDetailsText(detailsRef.current));\n      }\n      return () => {\n        setPopoverText('');\n      };\n    }\n  }, [details, setPopoverText]);\n  const isExpanded = seriesTitle => !!expandedSeries && expandedSeries.has(seriesTitle);\n  return React.createElement(\"div\", Object.assign({}, baseProps, {\n    className: className,\n    ref: mergedRef\n  }), React.createElement(\"ul\", {\n    className: clsx(styles.list, compactList && styles.compact)\n  }, details.map(({\n    key,\n    value,\n    markerType,\n    color,\n    isDimmed,\n    subItems,\n    expandableId\n  }, index) => React.createElement(\"li\", {\n    key: index,\n    className: clsx({\n      [styles.dimmed]: isDimmed,\n      [styles['list-item']]: true,\n      [styles['with-sub-items']]: subItems === null || subItems === void 0 ? void 0 : subItems.length,\n      [styles.expandable]: !!expandableId\n    })\n  }, (subItems === null || subItems === void 0 ? void 0 : subItems.length) && !!expandableId ? React.createElement(ExpandableSeries, {\n    itemKey: key,\n    value: value,\n    markerType: markerType,\n    color: color,\n    subItems: subItems,\n    expanded: isExpanded(expandableId),\n    setExpandedState: state => setExpandedState && setExpandedState(expandableId, state)\n  }) : React.createElement(NonExpandableSeries, {\n    itemKey: key,\n    value: value,\n    markerType: markerType,\n    color: color,\n    subItems: subItems\n  })))));\n}\nfunction SubItems({\n  items,\n  expandable,\n  expanded\n}) {\n  return React.createElement(\"ul\", {\n    className: clsx(styles['sub-items'], expandable && styles.expandable)\n  }, items.map(({\n    key,\n    value\n  }, index) => React.createElement(\"li\", {\n    key: index,\n    className: clsx(styles['inner-list-item'], styles['key-value-pair'], (expanded || !expandable) && styles.announced)\n  }, React.createElement(\"span\", {\n    className: styles.key\n  }, key), React.createElement(\"span\", {\n    className: styles.value\n  }, value))));\n}\nfunction ExpandableSeries({\n  itemKey,\n  value,\n  subItems,\n  markerType,\n  color,\n  expanded,\n  setExpandedState\n}) {\n  return React.createElement(\"div\", {\n    className: styles['expandable-section']\n  }, markerType && color && React.createElement(ChartSeriesMarker, {\n    type: markerType,\n    color: color\n  }), React.createElement(\"div\", {\n    className: styles['full-width']\n  }, React.createElement(InternalExpandableSection, {\n    variant: \"compact\",\n    headerText: itemKey,\n    headerActions: React.createElement(\"span\", {\n      className: clsx(styles.value, styles.expandable)\n    }, value),\n    expanded: expanded,\n    onChange: ({\n      detail\n    }) => setExpandedState(detail.expanded)\n  }, React.createElement(SubItems, {\n    items: subItems,\n    expandable: true,\n    expanded: expanded\n  }))));\n}\nfunction NonExpandableSeries({\n  itemKey,\n  value,\n  subItems,\n  markerType,\n  color\n}) {\n  return React.createElement(React.Fragment, null, React.createElement(\"div\", {\n    className: clsx(styles['key-value-pair'], styles.announced)\n  }, React.createElement(\"div\", {\n    className: styles.key\n  }, markerType && color && React.createElement(ChartSeriesMarker, {\n    type: markerType,\n    color: color\n  }), React.createElement(\"span\", null, itemKey)), React.createElement(\"span\", {\n    className: styles.value\n  }, value)), subItems && React.createElement(SubItems, {\n    items: subItems\n  }));\n}", "map": {"version": 3, "names": ["React", "forwardRef", "memo", "useEffect", "useRef", "clsx", "InternalExpandableSection", "getBaseProps", "useMergeRefs", "ChartSeriesMarker", "getSeriesDetailsText", "styles", "ChartSeriesDetails", "_a", "ref", "details", "expandedSeries", "setPopoverText", "setExpandedState", "compactList", "restProps", "__rest", "baseProps", "className", "root", "detailsRef", "mergedRef", "current", "isExpanded", "seriesTitle", "has", "createElement", "Object", "assign", "list", "compact", "map", "key", "value", "markerType", "color", "isDimmed", "subItems", "expandableId", "index", "dimmed", "length", "expandable", "ExpandableSeries", "itemKey", "expanded", "state", "NonExpandableSeries", "SubItems", "items", "announced", "type", "variant", "headerText", "headerActions", "onChange", "detail", "Fragment"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\internal\\components\\chart-series-details\\index.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { forwardRef, memo, ReactNode, useEffect, useRef } from 'react';\nimport clsx from 'clsx';\n\nimport InternalExpandableSection from '../../../expandable-section/internal';\nimport { BaseComponentProps, getBaseProps } from '../../base-component';\nimport { useMergeRefs } from '../../hooks/use-merge-refs';\nimport ChartSeriesMarker, { ChartSeriesMarkerType } from '../chart-series-marker';\nimport getSeriesDetailsText from './series-details-text';\n\nimport styles from './styles.css.js';\n\ninterface ChartDetailPair {\n  key: ReactNode;\n  value: ReactNode;\n}\n\ninterface ListItemProps {\n  itemKey: ReactNode;\n  value: ReactNode;\n  subItems?: ReadonlyArray<ChartDetailPair>;\n  markerType?: ChartSeriesMarkerType;\n  color?: string;\n}\n\nexport interface ChartSeriesDetailItem extends ChartDetailPair {\n  markerType?: ChartSeriesMarkerType;\n  color?: string;\n  isDimmed?: boolean;\n  subItems?: ReadonlyArray<ChartDetailPair>;\n  expandableId?: string;\n}\nexport type ExpandedSeries = Set<string>;\n\ninterface ChartSeriesDetailsProps extends BaseComponentProps {\n  details: ReadonlyArray<ChartSeriesDetailItem>;\n  expandedSeries?: ExpandedSeries;\n  setPopoverText?: (s: string) => void;\n  setExpandedState?: (seriesTitle: string, state: boolean) => void;\n  compactList?: boolean;\n}\n\nexport default memo(forwardRef(ChartSeriesDetails));\n\nfunction ChartSeriesDetails(\n  { details, expandedSeries, setPopoverText, setExpandedState, compactList, ...restProps }: ChartSeriesDetailsProps,\n  ref: React.Ref<HTMLDivElement>\n) {\n  const baseProps = getBaseProps(restProps);\n  const className = clsx(baseProps.className, styles.root);\n  const detailsRef = useRef<HTMLDivElement | null>(null);\n  const mergedRef = useMergeRefs(ref, detailsRef);\n\n  // Once the component has rendered, pass its content in plain text\n  // so that it can be used by screen readers.\n  useEffect(() => {\n    if (setPopoverText) {\n      if (detailsRef.current) {\n        setPopoverText(getSeriesDetailsText(detailsRef.current));\n      }\n      return () => {\n        setPopoverText('');\n      };\n    }\n  }, [details, setPopoverText]);\n\n  const isExpanded = (seriesTitle: string) => !!expandedSeries && expandedSeries.has(seriesTitle);\n\n  return (\n    <div {...baseProps} className={className} ref={mergedRef}>\n      <ul className={clsx(styles.list, compactList && styles.compact)}>\n        {details.map(({ key, value, markerType, color, isDimmed, subItems, expandableId }, index) => (\n          <li\n            key={index}\n            className={clsx({\n              [styles.dimmed]: isDimmed,\n              [styles['list-item']]: true,\n              [styles['with-sub-items']]: subItems?.length,\n              [styles.expandable]: !!expandableId,\n            })}\n          >\n            {subItems?.length && !!expandableId ? (\n              <ExpandableSeries\n                itemKey={key}\n                value={value}\n                markerType={markerType}\n                color={color}\n                subItems={subItems}\n                expanded={isExpanded(expandableId)}\n                setExpandedState={state => setExpandedState && setExpandedState(expandableId, state)}\n              />\n            ) : (\n              <NonExpandableSeries\n                itemKey={key}\n                value={value}\n                markerType={markerType}\n                color={color}\n                subItems={subItems}\n              />\n            )}\n          </li>\n        ))}\n      </ul>\n    </div>\n  );\n}\n\nfunction SubItems({\n  items,\n  expandable,\n  expanded,\n}: {\n  items: ReadonlyArray<ChartDetailPair>;\n  expandable?: boolean;\n  expanded?: boolean;\n}) {\n  return (\n    <ul className={clsx(styles['sub-items'], expandable && styles.expandable)}>\n      {items.map(({ key, value }, index) => (\n        <li\n          key={index}\n          className={clsx(\n            styles['inner-list-item'],\n            styles['key-value-pair'],\n            (expanded || !expandable) && styles.announced\n          )}\n        >\n          <span className={styles.key}>{key}</span>\n          <span className={styles.value}>{value}</span>\n        </li>\n      ))}\n    </ul>\n  );\n}\n\nfunction ExpandableSeries({\n  itemKey,\n  value,\n  subItems,\n  markerType,\n  color,\n  expanded,\n  setExpandedState,\n}: ListItemProps &\n  Required<Pick<ListItemProps, 'subItems'>> & {\n    expanded: boolean;\n    setExpandedState: (state: boolean) => void;\n  }) {\n  return (\n    <div className={styles['expandable-section']}>\n      {markerType && color && <ChartSeriesMarker type={markerType} color={color} />}\n      <div className={styles['full-width']}>\n        <InternalExpandableSection\n          variant=\"compact\"\n          headerText={itemKey}\n          headerActions={<span className={clsx(styles.value, styles.expandable)}>{value}</span>}\n          expanded={expanded}\n          onChange={({ detail }) => setExpandedState(detail.expanded)}\n        >\n          <SubItems items={subItems} expandable={true} expanded={expanded} />\n        </InternalExpandableSection>\n      </div>\n    </div>\n  );\n}\n\nfunction NonExpandableSeries({ itemKey, value, subItems, markerType, color }: ListItemProps) {\n  return (\n    <>\n      <div className={clsx(styles['key-value-pair'], styles.announced)}>\n        <div className={styles.key}>\n          {markerType && color && <ChartSeriesMarker type={markerType} color={color} />}\n          <span>{itemKey}</span>\n        </div>\n        <span className={styles.value}>{value}</span>\n      </div>\n      {subItems && <SubItems items={subItems} />}\n    </>\n  );\n}\n"], "mappings": ";AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,IAAI,EAAaC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC7E,OAAOC,IAAI,MAAM,MAAM;AAEvB,OAAOC,yBAAyB,MAAM,sCAAsC;AAC5E,SAA6BC,YAAY,QAAQ,sBAAsB;AACvE,SAASC,YAAY,QAAQ,4BAA4B;AACzD,OAAOC,iBAA4C,MAAM,wBAAwB;AACjF,OAAOC,oBAAoB,MAAM,uBAAuB;AAExD,OAAOC,MAAM,MAAM,iBAAiB;AAgCpC,eAAeT,IAAI,CAACD,UAAU,CAACW,kBAAkB,CAAC,CAAC;AAEnD,SAASA,kBAAkBA,CACzBC,EAAiH,EACjHC,GAA8B;MAD9B;MAAEC,OAAO;MAAEC,cAAc;MAAEC,cAAc;MAAEC,gBAAgB;MAAEC;IAAW,IAAAN,EAAyC;IAApCO,SAAS,GAAAC,MAAA,CAAAR,EAAA,EAAtF,kFAAwF,CAAF;EAGtF,MAAMS,SAAS,GAAGf,YAAY,CAACa,SAAS,CAAC;EACzC,MAAMG,SAAS,GAAGlB,IAAI,CAACiB,SAAS,CAACC,SAAS,EAAEZ,MAAM,CAACa,IAAI,CAAC;EACxD,MAAMC,UAAU,GAAGrB,MAAM,CAAwB,IAAI,CAAC;EACtD,MAAMsB,SAAS,GAAGlB,YAAY,CAACM,GAAG,EAAEW,UAAU,CAAC;EAE/C;EACA;EACAtB,SAAS,CAAC,MAAK;IACb,IAAIc,cAAc,EAAE;MAClB,IAAIQ,UAAU,CAACE,OAAO,EAAE;QACtBV,cAAc,CAACP,oBAAoB,CAACe,UAAU,CAACE,OAAO,CAAC,CAAC;;MAE1D,OAAO,MAAK;QACVV,cAAc,CAAC,EAAE,CAAC;MACpB,CAAC;;EAEL,CAAC,EAAE,CAACF,OAAO,EAAEE,cAAc,CAAC,CAAC;EAE7B,MAAMW,UAAU,GAAIC,WAAmB,IAAK,CAAC,CAACb,cAAc,IAAIA,cAAc,CAACc,GAAG,CAACD,WAAW,CAAC;EAE/F,OACE7B,KAAA,CAAA+B,aAAA,QAAAC,MAAA,CAAAC,MAAA,KAASX,SAAS;IAAEC,SAAS,EAAEA,SAAS;IAAET,GAAG,EAAEY;EAAS,IACtD1B,KAAA,CAAA+B,aAAA;IAAIR,SAAS,EAAElB,IAAI,CAACM,MAAM,CAACuB,IAAI,EAAEf,WAAW,IAAIR,MAAM,CAACwB,OAAO;EAAC,GAC5DpB,OAAO,CAACqB,GAAG,CAAC,CAAC;IAAEC,GAAG;IAAEC,KAAK;IAAEC,UAAU;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,QAAQ;IAAEC;EAAY,CAAE,EAAEC,KAAK,KACtF5C,KAAA,CAAA+B,aAAA;IACEM,GAAG,EAAEO,KAAK;IACVrB,SAAS,EAAElB,IAAI,CAAC;MACd,CAACM,MAAM,CAACkC,MAAM,GAAGJ,QAAQ;MACzB,CAAC9B,MAAM,CAAC,WAAW,CAAC,GAAG,IAAI;MAC3B,CAACA,MAAM,CAAC,gBAAgB,CAAC,GAAG+B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,MAAM;MAC5C,CAACnC,MAAM,CAACoC,UAAU,GAAG,CAAC,CAACJ;KACxB;EAAC,GAED,CAAAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEI,MAAM,KAAI,CAAC,CAACH,YAAY,GACjC3C,KAAA,CAAA+B,aAAA,CAACiB,gBAAgB;IACfC,OAAO,EAAEZ,GAAG;IACZC,KAAK,EAAEA,KAAK;IACZC,UAAU,EAAEA,UAAU;IACtBC,KAAK,EAAEA,KAAK;IACZE,QAAQ,EAAEA,QAAQ;IAClBQ,QAAQ,EAAEtB,UAAU,CAACe,YAAY,CAAC;IAClCzB,gBAAgB,EAAEiC,KAAK,IAAIjC,gBAAgB,IAAIA,gBAAgB,CAACyB,YAAY,EAAEQ,KAAK;EAAC,EACpF,GAEFnD,KAAA,CAAA+B,aAAA,CAACqB,mBAAmB;IAClBH,OAAO,EAAEZ,GAAG;IACZC,KAAK,EAAEA,KAAK;IACZC,UAAU,EAAEA,UAAU;IACtBC,KAAK,EAAEA,KAAK;IACZE,QAAQ,EAAEA;EAAQ,EAErB,CAEJ,CAAC,CACC,CACD;AAEV;AAEA,SAASW,QAAQA,CAAC;EAChBC,KAAK;EACLP,UAAU;EACVG;AAAQ,CAKT;EACC,OACElD,KAAA,CAAA+B,aAAA;IAAIR,SAAS,EAAElB,IAAI,CAACM,MAAM,CAAC,WAAW,CAAC,EAAEoC,UAAU,IAAIpC,MAAM,CAACoC,UAAU;EAAC,GACtEO,KAAK,CAAClB,GAAG,CAAC,CAAC;IAAEC,GAAG;IAAEC;EAAK,CAAE,EAAEM,KAAK,KAC/B5C,KAAA,CAAA+B,aAAA;IACEM,GAAG,EAAEO,KAAK;IACVrB,SAAS,EAAElB,IAAI,CACbM,MAAM,CAAC,iBAAiB,CAAC,EACzBA,MAAM,CAAC,gBAAgB,CAAC,EACxB,CAACuC,QAAQ,IAAI,CAACH,UAAU,KAAKpC,MAAM,CAAC4C,SAAS;EAC9C,GAEDvD,KAAA,CAAA+B,aAAA;IAAMR,SAAS,EAAEZ,MAAM,CAAC0B;EAAG,GAAGA,GAAG,CAAQ,EACzCrC,KAAA,CAAA+B,aAAA;IAAMR,SAAS,EAAEZ,MAAM,CAAC2B;EAAK,GAAGA,KAAK,CAAQ,CAEhD,CAAC,CACC;AAET;AAEA,SAASU,gBAAgBA,CAAC;EACxBC,OAAO;EACPX,KAAK;EACLI,QAAQ;EACRH,UAAU;EACVC,KAAK;EACLU,QAAQ;EACRhC;AAAgB,CAKf;EACD,OACElB,KAAA,CAAA+B,aAAA;IAAKR,SAAS,EAAEZ,MAAM,CAAC,oBAAoB;EAAC,GACzC4B,UAAU,IAAIC,KAAK,IAAIxC,KAAA,CAAA+B,aAAA,CAACtB,iBAAiB;IAAC+C,IAAI,EAAEjB,UAAU;IAAEC,KAAK,EAAEA;EAAK,EAAI,EAC7ExC,KAAA,CAAA+B,aAAA;IAAKR,SAAS,EAAEZ,MAAM,CAAC,YAAY;EAAC,GAClCX,KAAA,CAAA+B,aAAA,CAACzB,yBAAyB;IACxBmD,OAAO,EAAC,SAAS;IACjBC,UAAU,EAAET,OAAO;IACnBU,aAAa,EAAE3D,KAAA,CAAA+B,aAAA;MAAMR,SAAS,EAAElB,IAAI,CAACM,MAAM,CAAC2B,KAAK,EAAE3B,MAAM,CAACoC,UAAU;IAAC,GAAGT,KAAK,CAAQ;IACrFY,QAAQ,EAAEA,QAAQ;IAClBU,QAAQ,EAAEA,CAAC;MAAEC;IAAM,CAAE,KAAK3C,gBAAgB,CAAC2C,MAAM,CAACX,QAAQ;EAAC,GAE3DlD,KAAA,CAAA+B,aAAA,CAACsB,QAAQ;IAACC,KAAK,EAAEZ,QAAQ;IAAEK,UAAU,EAAE,IAAI;IAAEG,QAAQ,EAAEA;EAAQ,EAAI,CACzC,CACxB,CACF;AAEV;AAEA,SAASE,mBAAmBA,CAAC;EAAEH,OAAO;EAAEX,KAAK;EAAEI,QAAQ;EAAEH,UAAU;EAAEC;AAAK,CAAiB;EACzF,OACExC,KAAA,CAAA+B,aAAA,CAAA/B,KAAA,CAAA8D,QAAA,QACE9D,KAAA,CAAA+B,aAAA;IAAKR,SAAS,EAAElB,IAAI,CAACM,MAAM,CAAC,gBAAgB,CAAC,EAAEA,MAAM,CAAC4C,SAAS;EAAC,GAC9DvD,KAAA,CAAA+B,aAAA;IAAKR,SAAS,EAAEZ,MAAM,CAAC0B;EAAG,GACvBE,UAAU,IAAIC,KAAK,IAAIxC,KAAA,CAAA+B,aAAA,CAACtB,iBAAiB;IAAC+C,IAAI,EAAEjB,UAAU;IAAEC,KAAK,EAAEA;EAAK,EAAI,EAC7ExC,KAAA,CAAA+B,aAAA,eAAOkB,OAAO,CAAQ,CAClB,EACNjD,KAAA,CAAA+B,aAAA;IAAMR,SAAS,EAAEZ,MAAM,CAAC2B;EAAK,GAAGA,KAAK,CAAQ,CACzC,EACLI,QAAQ,IAAI1C,KAAA,CAAA+B,aAAA,CAACsB,QAAQ;IAACC,KAAK,EAAEZ;EAAQ,EAAI,CACzC;AAEP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}