{"ast": null, "code": "import { __awaiter } from \"tslib\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useEffect, useRef, useState } from 'react';\nimport But<PERSON> from '../../button/internal';\nimport <PERSON>Field from '../../form-field/internal';\nimport { useInternalI18n } from '../../i18n/context';\nimport FocusLock from '../../internal/components/focus-lock';\nimport InternalLiveRegion from '../../live-region/internal';\nimport SpaceBetween from '../../space-between/internal';\nimport { useClickAway } from './click-away';\nimport styles from './styles.css.js';\n// A function that does nothing\nconst noop = () => undefined;\nexport function InlineEditor({\n  ariaLabels,\n  item,\n  column,\n  onEditEnd,\n  submitEdit,\n  __onRender\n}) {\n  var _a, _b, _c, _d;\n  const [currentEditLoading, setCurrentEditLoading] = useState(false);\n  const [currentEditValue, setCurrentEditValue] = useState();\n  const i18n = useInternalI18n('table');\n  const focusLockRef = useRef(null);\n  const cellContext = {\n    currentValue: currentEditValue,\n    setValue: setCurrentEditValue\n  };\n  function finishEdit({\n    cancelled = false,\n    refocusCell = true\n  } = {}) {\n    if (!cancelled) {\n      setCurrentEditValue(undefined);\n    }\n    onEditEnd({\n      cancelled,\n      refocusCell: refocusCell\n    });\n  }\n  function onSubmitClick(evt) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function* () {\n      evt.preventDefault();\n      if (currentEditValue === undefined) {\n        finishEdit();\n        return;\n      }\n      setCurrentEditLoading(true);\n      try {\n        yield submitEdit(item, column, currentEditValue);\n        setCurrentEditLoading(false);\n        finishEdit();\n      } catch (e) {\n        setCurrentEditLoading(false);\n        (_a = focusLockRef.current) === null || _a === void 0 ? void 0 : _a.focusFirst();\n      }\n    });\n  }\n  function onCancel({\n    reFocusEditedCell = true\n  } = {}) {\n    if (currentEditLoading) {\n      return;\n    }\n    finishEdit({\n      cancelled: true,\n      refocusCell: reFocusEditedCell\n    });\n  }\n  function handleEscape(event) {\n    if (event.key === 'Escape') {\n      onCancel();\n    }\n  }\n  const clickAwayRef = useClickAway(() => onCancel({\n    reFocusEditedCell: false\n  }));\n  useEffect(() => {\n    if (__onRender) {\n      const timer = setTimeout(__onRender, 1);\n      return () => clearTimeout(timer);\n    }\n  }, [__onRender]);\n  // asserting non-undefined editConfig here because this component is unreachable otherwise\n  const {\n    ariaLabel = undefined,\n    validation = noop,\n    errorIconAriaLabel,\n    constraintText,\n    editingCell\n  } = column.editConfig;\n  return React.createElement(FocusLock, {\n    restoreFocus: true,\n    ref: focusLockRef\n  }, React.createElement(\"div\", {\n    role: \"dialog\",\n    ref: clickAwayRef,\n    \"aria-label\": (_a = ariaLabels === null || ariaLabels === void 0 ? void 0 : ariaLabels.activateEditLabel) === null || _a === void 0 ? void 0 : _a.call(ariaLabels, column, item),\n    onKeyDown: handleEscape\n  }, React.createElement(\"form\", {\n    onSubmit: onSubmitClick\n  }, React.createElement(FormField, {\n    stretch: true,\n    label: ariaLabel,\n    constraintText: constraintText,\n    __hideLabel: true,\n    __disableGutters: true,\n    i18nStrings: {\n      errorIconAriaLabel\n    },\n    errorText: validation(item, currentEditValue)\n  }, React.createElement(\"div\", {\n    className: styles['body-cell-editor-row']\n  }, React.createElement(\"div\", {\n    className: styles['body-cell-editor-row-editor']\n  }, editingCell(item, cellContext)), React.createElement(\"span\", {\n    className: styles['body-cell-editor-controls']\n  }, React.createElement(SpaceBetween, {\n    direction: \"horizontal\",\n    size: \"xxs\"\n  }, !currentEditLoading ? React.createElement(Button, {\n    ariaLabel: (_b = ariaLabels === null || ariaLabels === void 0 ? void 0 : ariaLabels.cancelEditLabel) === null || _b === void 0 ? void 0 : _b.call(ariaLabels, column),\n    formAction: \"none\",\n    iconName: \"close\",\n    variant: \"inline-icon\",\n    onClick: () => onCancel()\n  }) : null, React.createElement(Button, {\n    ariaLabel: (_c = ariaLabels === null || ariaLabels === void 0 ? void 0 : ariaLabels.submitEditLabel) === null || _c === void 0 ? void 0 : _c.call(ariaLabels, column),\n    formAction: \"submit\",\n    iconName: \"check\",\n    variant: \"inline-icon\",\n    loading: currentEditLoading\n  })), React.createElement(InternalLiveRegion, {\n    tagName: \"span\",\n    hidden: true\n  }, currentEditLoading ? i18n('ariaLabels.submittingEditText', (_d = ariaLabels === null || ariaLabels === void 0 ? void 0 : ariaLabels.submittingEditText) === null || _d === void 0 ? void 0 : _d.call(ariaLabels, column)) : '')))))));\n}", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "<PERSON><PERSON>", "FormField", "useInternalI18n", "FocusLock", "InternalLiveRegion", "SpaceBetween", "useClickAway", "styles", "noop", "undefined", "InlineEditor", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item", "column", "onEditEnd", "submitEdit", "__onRender", "currentEditLoading", "setCurrentEditLoading", "currentEditValue", "setCurrentEditValue", "i18n", "focusLockRef", "cellContext", "currentValue", "setValue", "finishEdit", "cancelled", "refocusCell", "onSubmitClick", "evt", "preventDefault", "e", "_a", "current", "focusFirst", "onCancel", "reFocusEditedCell", "handleEscape", "event", "key", "clickAwayRef", "timer", "setTimeout", "clearTimeout", "aria<PERSON><PERSON><PERSON>", "validation", "errorIconAriaLabel", "constraintText", "editingCell", "editConfig", "createElement", "restoreFocus", "ref", "role", "activateEditLabel", "call", "onKeyDown", "onSubmit", "stretch", "label", "__<PERSON><PERSON><PERSON><PERSON>", "__disableGutters", "i18nStrings", "errorText", "className", "direction", "size", "_b", "cancelEditLabel", "formAction", "iconName", "variant", "onClick", "_c", "submitEditLabel", "loading", "tagName", "hidden", "_d", "submittingEditText"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\table\\body-cell\\inline-editor.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useEffect, useRef, useState } from 'react';\n\nimport Button from '../../button/internal';\nimport FormField from '../../form-field/internal';\nimport { useInternalI18n } from '../../i18n/context';\nimport FocusLock, { FocusLockRef } from '../../internal/components/focus-lock';\nimport { Optional } from '../../internal/types';\nimport InternalLiveRegion from '../../live-region/internal';\nimport SpaceBetween from '../../space-between/internal';\nimport { TableProps } from '../interfaces';\nimport { useClickAway } from './click-away';\n\nimport styles from './styles.css.js';\n\n// A function that does nothing\nconst noop = () => undefined;\n\ninterface OnEditEndOptions {\n  cancelled: boolean;\n  refocusCell: boolean;\n}\n\ninterface InlineEditorProps<ItemType> {\n  ariaLabels: TableProps['ariaLabels'];\n  column: TableProps.ColumnDefinition<ItemType>;\n  item: ItemType;\n  onEditEnd: (options: OnEditEndOptions) => void;\n  submitEdit: TableProps.SubmitEditFunction<ItemType>;\n  __onRender?: () => void;\n}\n\nexport function InlineEditor<ItemType>({\n  ariaLabels,\n  item,\n  column,\n  onEditEnd,\n  submitEdit,\n  __onRender,\n}: InlineEditorProps<ItemType>) {\n  const [currentEditLoading, setCurrentEditLoading] = useState(false);\n  const [currentEditValue, setCurrentEditValue] = useState<Optional<any>>();\n  const i18n = useInternalI18n('table');\n\n  const focusLockRef = useRef<FocusLockRef>(null);\n\n  const cellContext = {\n    currentValue: currentEditValue,\n    setValue: setCurrentEditValue,\n  };\n\n  function finishEdit({ cancelled = false, refocusCell = true }: Partial<OnEditEndOptions> = {}) {\n    if (!cancelled) {\n      setCurrentEditValue(undefined);\n    }\n    onEditEnd({ cancelled, refocusCell: refocusCell });\n  }\n\n  async function onSubmitClick(evt: React.FormEvent) {\n    evt.preventDefault();\n    if (currentEditValue === undefined) {\n      finishEdit();\n      return;\n    }\n\n    setCurrentEditLoading(true);\n    try {\n      await submitEdit(item, column, currentEditValue);\n      setCurrentEditLoading(false);\n      finishEdit();\n    } catch (e) {\n      setCurrentEditLoading(false);\n      focusLockRef.current?.focusFirst();\n    }\n  }\n\n  function onCancel({ reFocusEditedCell = true } = {}) {\n    if (currentEditLoading) {\n      return;\n    }\n    finishEdit({ cancelled: true, refocusCell: reFocusEditedCell });\n  }\n\n  function handleEscape(event: React.KeyboardEvent): void {\n    if (event.key === 'Escape') {\n      onCancel();\n    }\n  }\n\n  const clickAwayRef = useClickAway(() => onCancel({ reFocusEditedCell: false }));\n\n  useEffect(() => {\n    if (__onRender) {\n      const timer = setTimeout(__onRender, 1);\n      return () => clearTimeout(timer);\n    }\n  }, [__onRender]);\n\n  // asserting non-undefined editConfig here because this component is unreachable otherwise\n  const {\n    ariaLabel = undefined,\n    validation = noop,\n    errorIconAriaLabel,\n    constraintText,\n    editingCell,\n  } = column.editConfig!;\n\n  return (\n    <FocusLock restoreFocus={true} ref={focusLockRef}>\n      <div\n        role=\"dialog\"\n        ref={clickAwayRef}\n        aria-label={ariaLabels?.activateEditLabel?.(column, item)}\n        onKeyDown={handleEscape}\n      >\n        <form onSubmit={onSubmitClick}>\n          <FormField\n            stretch={true}\n            label={ariaLabel}\n            constraintText={constraintText}\n            __hideLabel={true}\n            __disableGutters={true}\n            i18nStrings={{ errorIconAriaLabel }}\n            errorText={validation(item, currentEditValue)}\n          >\n            <div className={styles['body-cell-editor-row']}>\n              <div className={styles['body-cell-editor-row-editor']}>{editingCell(item, cellContext)}</div>\n              <span className={styles['body-cell-editor-controls']}>\n                <SpaceBetween direction=\"horizontal\" size=\"xxs\">\n                  {!currentEditLoading ? (\n                    <Button\n                      ariaLabel={ariaLabels?.cancelEditLabel?.(column)}\n                      formAction=\"none\"\n                      iconName=\"close\"\n                      variant=\"inline-icon\"\n                      onClick={() => onCancel()}\n                    />\n                  ) : null}\n                  <Button\n                    ariaLabel={ariaLabels?.submitEditLabel?.(column)}\n                    formAction=\"submit\"\n                    iconName=\"check\"\n                    variant=\"inline-icon\"\n                    loading={currentEditLoading}\n                  />\n                </SpaceBetween>\n                <InternalLiveRegion tagName=\"span\" hidden={true}>\n                  {currentEditLoading\n                    ? i18n('ariaLabels.submittingEditText', ariaLabels?.submittingEditText?.(column))\n                    : ''}\n                </InternalLiveRegion>\n              </span>\n            </div>\n          </FormField>\n        </form>\n      </div>\n    </FocusLock>\n  );\n}\n"], "mappings": ";AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAE1D,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,SAAS,MAAM,2BAA2B;AACjD,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAOC,SAA2B,MAAM,sCAAsC;AAE9E,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,YAAY,MAAM,8BAA8B;AAEvD,SAASC,YAAY,QAAQ,cAAc;AAE3C,OAAOC,MAAM,MAAM,iBAAiB;AAEpC;AACA,MAAMC,IAAI,GAAGA,CAAA,KAAMC,SAAS;AAgB5B,OAAM,SAAUC,YAAYA,CAAW;EACrCC,UAAU;EACVC,IAAI;EACJC,MAAM;EACNC,SAAS;EACTC,UAAU;EACVC;AAAU,CACkB;;EAC5B,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,EAAiB;EACzE,MAAMsB,IAAI,GAAGnB,eAAe,CAAC,OAAO,CAAC;EAErC,MAAMoB,YAAY,GAAGxB,MAAM,CAAe,IAAI,CAAC;EAE/C,MAAMyB,WAAW,GAAG;IAClBC,YAAY,EAAEL,gBAAgB;IAC9BM,QAAQ,EAAEL;GACX;EAED,SAASM,UAAUA,CAAC;IAAEC,SAAS,GAAG,KAAK;IAAEC,WAAW,GAAG;EAAI,IAAgC,EAAE;IAC3F,IAAI,CAACD,SAAS,EAAE;MACdP,mBAAmB,CAACX,SAAS,CAAC;;IAEhCK,SAAS,CAAC;MAAEa,SAAS;MAAEC,WAAW,EAAEA;IAAW,CAAE,CAAC;EACpD;EAEA,SAAeC,aAAaA,CAACC,GAAoB;;;MAC/CA,GAAG,CAACC,cAAc,EAAE;MACpB,IAAIZ,gBAAgB,KAAKV,SAAS,EAAE;QAClCiB,UAAU,EAAE;QACZ;;MAGFR,qBAAqB,CAAC,IAAI,CAAC;MAC3B,IAAI;QACF,MAAMH,UAAU,CAACH,IAAI,EAAEC,MAAM,EAAEM,gBAAgB,CAAC;QAChDD,qBAAqB,CAAC,KAAK,CAAC;QAC5BQ,UAAU,EAAE;OACb,CAAC,OAAOM,CAAC,EAAE;QACVd,qBAAqB,CAAC,KAAK,CAAC;QAC5B,CAAAe,EAAA,GAAAX,YAAY,CAACY,OAAO,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,UAAU,EAAE;;;;EAItC,SAASC,QAAQA,CAAC;IAAEC,iBAAiB,GAAG;EAAI,CAAE,GAAG,EAAE;IACjD,IAAIpB,kBAAkB,EAAE;MACtB;;IAEFS,UAAU,CAAC;MAAEC,SAAS,EAAE,IAAI;MAAEC,WAAW,EAAES;IAAiB,CAAE,CAAC;EACjE;EAEA,SAASC,YAAYA,CAACC,KAA0B;IAC9C,IAAIA,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC1BJ,QAAQ,EAAE;;EAEd;EAEA,MAAMK,YAAY,GAAGnC,YAAY,CAAC,MAAM8B,QAAQ,CAAC;IAAEC,iBAAiB,EAAE;EAAK,CAAE,CAAC,CAAC;EAE/ExC,SAAS,CAAC,MAAK;IACb,IAAImB,UAAU,EAAE;MACd,MAAM0B,KAAK,GAAGC,UAAU,CAAC3B,UAAU,EAAE,CAAC,CAAC;MACvC,OAAO,MAAM4B,YAAY,CAACF,KAAK,CAAC;;EAEpC,CAAC,EAAE,CAAC1B,UAAU,CAAC,CAAC;EAEhB;EACA,MAAM;IACJ6B,SAAS,GAAGpC,SAAS;IACrBqC,UAAU,GAAGtC,IAAI;IACjBuC,kBAAkB;IAClBC,cAAc;IACdC;EAAW,CACZ,GAAGpC,MAAM,CAACqC,UAAW;EAEtB,OACEtD,KAAA,CAAAuD,aAAA,CAAChD,SAAS;IAACiD,YAAY,EAAE,IAAI;IAAEC,GAAG,EAAE/B;EAAY,GAC9C1B,KAAA,CAAAuD,aAAA;IACEG,IAAI,EAAC,QAAQ;IACbD,GAAG,EAAEZ,YAAY;IAAA,cACL,CAAAR,EAAA,GAAAtB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE4C,iBAAiB,cAAAtB,EAAA,uBAAAA,EAAA,CAAAuB,IAAA,CAAA7C,UAAA,EAAGE,MAAM,EAAED,IAAI,CAAC;IACzD6C,SAAS,EAAEnB;EAAY,GAEvB1C,KAAA,CAAAuD,aAAA;IAAMO,QAAQ,EAAE7B;EAAa,GAC3BjC,KAAA,CAAAuD,aAAA,CAAClD,SAAS;IACR0D,OAAO,EAAE,IAAI;IACbC,KAAK,EAAEf,SAAS;IAChBG,cAAc,EAAEA,cAAc;IAC9Ba,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,IAAI;IACtBC,WAAW,EAAE;MAAEhB;IAAkB,CAAE;IACnCiB,SAAS,EAAElB,UAAU,CAAClC,IAAI,EAAEO,gBAAgB;EAAC,GAE7CvB,KAAA,CAAAuD,aAAA;IAAKc,SAAS,EAAE1D,MAAM,CAAC,sBAAsB;EAAC,GAC5CX,KAAA,CAAAuD,aAAA;IAAKc,SAAS,EAAE1D,MAAM,CAAC,6BAA6B;EAAC,GAAG0C,WAAW,CAACrC,IAAI,EAAEW,WAAW,CAAC,CAAO,EAC7F3B,KAAA,CAAAuD,aAAA;IAAMc,SAAS,EAAE1D,MAAM,CAAC,2BAA2B;EAAC,GAClDX,KAAA,CAAAuD,aAAA,CAAC9C,YAAY;IAAC6D,SAAS,EAAC,YAAY;IAACC,IAAI,EAAC;EAAK,GAC5C,CAAClD,kBAAkB,GAClBrB,KAAA,CAAAuD,aAAA,CAACnD,MAAM;IACL6C,SAAS,EAAE,CAAAuB,EAAA,GAAAzD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0D,eAAe,cAAAD,EAAA,uBAAAA,EAAA,CAAAZ,IAAA,CAAA7C,UAAA,EAAGE,MAAM,CAAC;IAChDyD,UAAU,EAAC,MAAM;IACjBC,QAAQ,EAAC,OAAO;IAChBC,OAAO,EAAC,aAAa;IACrBC,OAAO,EAAEA,CAAA,KAAMrC,QAAQ;EAAE,EACzB,GACA,IAAI,EACRxC,KAAA,CAAAuD,aAAA,CAACnD,MAAM;IACL6C,SAAS,EAAE,CAAA6B,EAAA,GAAA/D,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEgE,eAAe,cAAAD,EAAA,uBAAAA,EAAA,CAAAlB,IAAA,CAAA7C,UAAA,EAAGE,MAAM,CAAC;IAChDyD,UAAU,EAAC,QAAQ;IACnBC,QAAQ,EAAC,OAAO;IAChBC,OAAO,EAAC,aAAa;IACrBI,OAAO,EAAE3D;EAAkB,EAC3B,CACW,EACfrB,KAAA,CAAAuD,aAAA,CAAC/C,kBAAkB;IAACyE,OAAO,EAAC,MAAM;IAACC,MAAM,EAAE;EAAI,GAC5C7D,kBAAkB,GACfI,IAAI,CAAC,+BAA+B,EAAE,CAAA0D,EAAA,GAAApE,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEqE,kBAAkB,cAAAD,EAAA,uBAAAA,EAAA,CAAAvB,IAAA,CAAA7C,UAAA,EAAGE,MAAM,CAAC,CAAC,GAC/E,EAAE,CACa,CAChB,CACH,CACI,CACP,CACH,CACI;AAEhB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}