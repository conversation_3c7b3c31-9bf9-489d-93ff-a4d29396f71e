{"ast": null, "code": "import { __rest } from \"tslib\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useContext } from 'react';\nimport clsx from 'clsx';\nimport { getAnalyticsLabelAttribute } from '@cloudscape-design/component-toolkit/internal/analytics-metadata';\nimport { StickyHeaderContext } from '../container/use-sticky-header';\nimport { DATA_ATTR_FUNNEL_KEY, FUNNEL_KEY_SUBSTEP_NAME } from '../internal/analytics/selectors';\nimport { getBaseProps } from '../internal/base-component';\nimport { CollectionLabelContext } from '../internal/context/collection-label-context';\nimport { useContainerHeader } from '../internal/context/container-header';\nimport { InfoLinkLabelContext } from '../internal/context/info-link-label-context';\nimport { useMobile } from '../internal/hooks/use-mobile';\nimport { useUniqueId } from '../internal/hooks/use-unique-id';\nimport { useVisualRefresh } from '../internal/hooks/use-visual-mode';\nimport analyticsSelectors from './analytics-metadata/styles.css.js';\nimport styles from './styles.css.js';\nexport default function InternalHeader(_a) {\n  var {\n      variant,\n      headingTagOverride,\n      children,\n      actions,\n      counter,\n      description,\n      info,\n      __internalRootRef = null,\n      __disableActionsWrapping,\n      __headingTagRef,\n      __headingTagTabIndex\n    } = _a,\n    restProps = __rest(_a, [\"variant\", \"headingTagOverride\", \"children\", \"actions\", \"counter\", \"description\", \"info\", \"__internalRootRef\", \"__disableActionsWrapping\", \"__headingTagRef\", \"__headingTagTabIndex\"]);\n  const isMobile = useMobile();\n  const HeadingTag = headingTagOverride !== null && headingTagOverride !== void 0 ? headingTagOverride : variant === 'awsui-h1-sticky' ? 'h1' : variant;\n  const {\n    isStuck\n  } = useContext(StickyHeaderContext);\n  const baseProps = getBaseProps(restProps);\n  const isRefresh = useVisualRefresh();\n  const assignHeaderId = useContext(CollectionLabelContext).assignId;\n  const isInContainer = useContainerHeader();\n  const headingId = useUniqueId('heading');\n  if (assignHeaderId !== undefined) {\n    assignHeaderId(headingId);\n  }\n  // If is mobile there is no need to have the dynamic variant because it's scrolled out of view\n  const dynamicVariant = !isMobile && isStuck ? 'h2' : 'h1';\n  const variantOverride = variant === 'awsui-h1-sticky' ? isRefresh ? dynamicVariant : 'h2' : variant;\n  return React.createElement(\"div\", Object.assign({}, baseProps, {\n    className: clsx(styles.root, baseProps.className, styles[`root-variant-${variantOverride}`], isRefresh && styles.refresh, !actions && [styles[`root-no-actions`]], description && [styles[`root-has-description`]]),\n    ref: __internalRootRef\n  }), React.createElement(\"div\", {\n    className: clsx(styles.main, styles[`main-variant-${variantOverride}`], isRefresh && styles.refresh, __disableActionsWrapping && [styles['no-wrap']])\n  }, React.createElement(\"div\", {\n    className: clsx(styles.title, styles[`title-variant-${variantOverride}`], isRefresh && styles.refresh)\n  }, React.createElement(HeadingTag, Object.assign({\n    className: clsx(styles.heading, styles[`heading-variant-${variantOverride}`]),\n    ref: __headingTagRef,\n    tabIndex: __headingTagTabIndex\n  }, getAnalyticsLabelAttribute(`.${analyticsSelectors['heading-text']}`)), React.createElement(\"span\", Object.assign({}, isInContainer ? {\n    [DATA_ATTR_FUNNEL_KEY]: FUNNEL_KEY_SUBSTEP_NAME\n  } : {}, {\n    className: clsx(styles['heading-text'], analyticsSelectors['heading-text'], styles[`heading-text-variant-${variantOverride}`]),\n    id: headingId\n  }), children), counter !== undefined && React.createElement(\"span\", {\n    className: styles.counter\n  }, \" \", counter)), info && React.createElement(InfoLinkLabelContext.Provider, {\n    value: headingId\n  }, React.createElement(\"span\", {\n    className: styles['virtual-space']\n  }, \" \\u00A0\"), React.createElement(\"span\", {\n    className: styles.info\n  }, info))), actions && React.createElement(\"div\", {\n    className: clsx(styles.actions, styles[`actions-variant-${variantOverride}`], isRefresh && styles.refresh, !__disableActionsWrapping && [styles['actions-centered']])\n  }, actions)), React.createElement(Description, {\n    variantOverride: variantOverride\n  }, description));\n}\nexport function Description({\n  children,\n  variantOverride\n}) {\n  const isRefresh = useVisualRefresh();\n  return children && React.createElement(\"p\", {\n    className: clsx(styles.description, styles[`description-variant-${variantOverride}`], isRefresh && styles.refresh)\n  }, children) || null;\n}", "map": {"version": 3, "names": ["React", "useContext", "clsx", "getAnalyticsLabelAttribute", "StickyHeaderContext", "DATA_ATTR_FUNNEL_KEY", "FUNNEL_KEY_SUBSTEP_NAME", "getBaseProps", "CollectionLabelContext", "useContainerHeader", "InfoLinkLabelContext", "useMobile", "useUniqueId", "useVisualRefresh", "analyticsSelectors", "styles", "InternalHeader", "_a", "variant", "headingTagOverride", "children", "actions", "counter", "description", "info", "__internalRootRef", "__disableActionsWrapping", "__headingTagRef", "__headingTagTabIndex", "restProps", "__rest", "isMobile", "HeadingTag", "isStuck", "baseProps", "isRefresh", "assignHeaderId", "assignId", "isInContainer", "headingId", "undefined", "dynamicVariant", "variantOverride", "createElement", "Object", "assign", "className", "root", "refresh", "ref", "main", "title", "heading", "tabIndex", "id", "Provider", "value", "Description"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\header\\internal.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { MutableRefObject, useContext } from 'react';\nimport clsx from 'clsx';\n\nimport { getAnalyticsLabelAttribute } from '@cloudscape-design/component-toolkit/internal/analytics-metadata';\n\nimport { StickyHeaderContext } from '../container/use-sticky-header';\nimport { DATA_ATTR_FUNNEL_KEY, FUNNEL_KEY_SUBSTEP_NAME } from '../internal/analytics/selectors';\nimport { getBaseProps } from '../internal/base-component';\nimport { CollectionLabelContext } from '../internal/context/collection-label-context';\nimport { useContainerHeader } from '../internal/context/container-header';\nimport { InfoLinkLabelContext } from '../internal/context/info-link-label-context';\nimport { InternalBaseComponentProps } from '../internal/hooks/use-base-component';\nimport { useMobile } from '../internal/hooks/use-mobile';\nimport { useUniqueId } from '../internal/hooks/use-unique-id';\nimport { useVisualRefresh } from '../internal/hooks/use-visual-mode';\nimport { SomeRequired } from '../internal/types';\nimport { HeaderProps } from './interfaces';\n\nimport analyticsSelectors from './analytics-metadata/styles.css.js';\nimport styles from './styles.css.js';\n\ninterface InternalHeaderProps extends SomeRequired<HeaderProps, 'variant'>, InternalBaseComponentProps {\n  __disableActionsWrapping?: boolean;\n  __headingTagRef?: MutableRefObject<HTMLHeadingElement | null>;\n  __headingTagTabIndex?: number;\n}\n\nexport default function InternalHeader({\n  variant,\n  headingTagOverride,\n  children,\n  actions,\n  counter,\n  description,\n  info,\n  __internalRootRef = null,\n  __disableActionsWrapping,\n  __headingTagRef,\n  __headingTagTabIndex,\n  ...restProps\n}: InternalHeaderProps) {\n  const isMobile = useMobile();\n  const HeadingTag = headingTagOverride ?? (variant === 'awsui-h1-sticky' ? 'h1' : variant);\n  const { isStuck } = useContext(StickyHeaderContext);\n  const baseProps = getBaseProps(restProps);\n  const isRefresh = useVisualRefresh();\n  const assignHeaderId = useContext(CollectionLabelContext).assignId;\n  const isInContainer = useContainerHeader();\n  const headingId = useUniqueId('heading');\n  if (assignHeaderId !== undefined) {\n    assignHeaderId(headingId);\n  }\n  // If is mobile there is no need to have the dynamic variant because it's scrolled out of view\n  const dynamicVariant = !isMobile && isStuck ? 'h2' : 'h1';\n  const variantOverride = variant === 'awsui-h1-sticky' ? (isRefresh ? dynamicVariant : 'h2') : variant;\n\n  return (\n    <div\n      {...baseProps}\n      className={clsx(\n        styles.root,\n        baseProps.className,\n        styles[`root-variant-${variantOverride}`],\n        isRefresh && styles.refresh,\n        !actions && [styles[`root-no-actions`]],\n        description && [styles[`root-has-description`]]\n      )}\n      ref={__internalRootRef}\n    >\n      <div\n        className={clsx(\n          styles.main,\n          styles[`main-variant-${variantOverride}`],\n          isRefresh && styles.refresh,\n          __disableActionsWrapping && [styles['no-wrap']]\n        )}\n      >\n        <div className={clsx(styles.title, styles[`title-variant-${variantOverride}`], isRefresh && styles.refresh)}>\n          <HeadingTag\n            className={clsx(styles.heading, styles[`heading-variant-${variantOverride}`])}\n            ref={__headingTagRef}\n            tabIndex={__headingTagTabIndex}\n            {...getAnalyticsLabelAttribute(`.${analyticsSelectors['heading-text']}`)}\n          >\n            <span\n              {...(isInContainer ? { [DATA_ATTR_FUNNEL_KEY]: FUNNEL_KEY_SUBSTEP_NAME } : {})}\n              className={clsx(\n                styles['heading-text'],\n                analyticsSelectors['heading-text'],\n                styles[`heading-text-variant-${variantOverride}`]\n              )}\n              id={headingId}\n            >\n              {children}\n            </span>\n            {counter !== undefined && <span className={styles.counter}> {counter}</span>}\n          </HeadingTag>\n          {info && (\n            <InfoLinkLabelContext.Provider value={headingId}>\n              {/* Exists to create a space between heading text and info so that a double-click selection on the last word of the heading doesn't also include info */}\n              <span className={styles['virtual-space']}> &nbsp;</span>\n              <span className={styles.info}>{info}</span>\n            </InfoLinkLabelContext.Provider>\n          )}\n        </div>\n        {actions && (\n          <div\n            className={clsx(\n              styles.actions,\n              styles[`actions-variant-${variantOverride}`],\n              isRefresh && styles.refresh,\n              !__disableActionsWrapping && [styles['actions-centered']]\n            )}\n          >\n            {actions}\n          </div>\n        )}\n      </div>\n      <Description variantOverride={variantOverride}>{description}</Description>\n    </div>\n  );\n}\n\nexport function Description({ children, variantOverride }: { children: React.ReactNode; variantOverride: string }) {\n  const isRefresh = useVisualRefresh();\n  return (\n    (children && (\n      <p\n        className={clsx(\n          styles.description,\n          styles[`description-variant-${variantOverride}`],\n          isRefresh && styles.refresh\n        )}\n      >\n        {children}\n      </p>\n    )) ||\n    null\n  );\n}\n"], "mappings": ";AAAA;AACA;AACA,OAAOA,KAAK,IAAsBC,UAAU,QAAQ,OAAO;AAC3D,OAAOC,IAAI,MAAM,MAAM;AAEvB,SAASC,0BAA0B,QAAQ,kEAAkE;AAE7G,SAASC,mBAAmB,QAAQ,gCAAgC;AACpE,SAASC,oBAAoB,EAAEC,uBAAuB,QAAQ,iCAAiC;AAC/F,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,sBAAsB,QAAQ,8CAA8C;AACrF,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,oBAAoB,QAAQ,6CAA6C;AAElF,SAASC,SAAS,QAAQ,8BAA8B;AACxD,SAASC,WAAW,QAAQ,iCAAiC;AAC7D,SAASC,gBAAgB,QAAQ,mCAAmC;AAIpE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,MAAM,MAAM,iBAAiB;AAQpC,eAAc,SAAUC,cAAcA,CAACC,EAajB;MAbiB;MACrCC,OAAO;MACPC,kBAAkB;MAClBC,QAAQ;MACRC,OAAO;MACPC,OAAO;MACPC,WAAW;MACXC,IAAI;MACJC,iBAAiB,GAAG,IAAI;MACxBC,wBAAwB;MACxBC,eAAe;MACfC;IAAoB,IAAAX,EAEA;IADjBY,SAAS,GAAAC,MAAA,CAAAb,EAAA,EAZyB,sLAatC,CADa;EAEZ,MAAMc,QAAQ,GAAGpB,SAAS,EAAE;EAC5B,MAAMqB,UAAU,GAAGb,kBAAkB,aAAlBA,kBAAkB,cAAlBA,kBAAkB,GAAKD,OAAO,KAAK,iBAAiB,GAAG,IAAI,GAAGA,OAAQ;EACzF,MAAM;IAAEe;EAAO,CAAE,GAAGhC,UAAU,CAACG,mBAAmB,CAAC;EACnD,MAAM8B,SAAS,GAAG3B,YAAY,CAACsB,SAAS,CAAC;EACzC,MAAMM,SAAS,GAAGtB,gBAAgB,EAAE;EACpC,MAAMuB,cAAc,GAAGnC,UAAU,CAACO,sBAAsB,CAAC,CAAC6B,QAAQ;EAClE,MAAMC,aAAa,GAAG7B,kBAAkB,EAAE;EAC1C,MAAM8B,SAAS,GAAG3B,WAAW,CAAC,SAAS,CAAC;EACxC,IAAIwB,cAAc,KAAKI,SAAS,EAAE;IAChCJ,cAAc,CAACG,SAAS,CAAC;;EAE3B;EACA,MAAME,cAAc,GAAG,CAACV,QAAQ,IAAIE,OAAO,GAAG,IAAI,GAAG,IAAI;EACzD,MAAMS,eAAe,GAAGxB,OAAO,KAAK,iBAAiB,GAAIiB,SAAS,GAAGM,cAAc,GAAG,IAAI,GAAIvB,OAAO;EAErG,OACElB,KAAA,CAAA2C,aAAA,QAAAC,MAAA,CAAAC,MAAA,KACMX,SAAS;IACbY,SAAS,EAAE5C,IAAI,CACba,MAAM,CAACgC,IAAI,EACXb,SAAS,CAACY,SAAS,EACnB/B,MAAM,CAAC,gBAAgB2B,eAAe,EAAE,CAAC,EACzCP,SAAS,IAAIpB,MAAM,CAACiC,OAAO,EAC3B,CAAC3B,OAAO,IAAI,CAACN,MAAM,CAAC,iBAAiB,CAAC,CAAC,EACvCQ,WAAW,IAAI,CAACR,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAChD;IACDkC,GAAG,EAAExB;EAAiB,IAEtBzB,KAAA,CAAA2C,aAAA;IACEG,SAAS,EAAE5C,IAAI,CACba,MAAM,CAACmC,IAAI,EACXnC,MAAM,CAAC,gBAAgB2B,eAAe,EAAE,CAAC,EACzCP,SAAS,IAAIpB,MAAM,CAACiC,OAAO,EAC3BtB,wBAAwB,IAAI,CAACX,MAAM,CAAC,SAAS,CAAC,CAAC;EAChD,GAEDf,KAAA,CAAA2C,aAAA;IAAKG,SAAS,EAAE5C,IAAI,CAACa,MAAM,CAACoC,KAAK,EAAEpC,MAAM,CAAC,iBAAiB2B,eAAe,EAAE,CAAC,EAAEP,SAAS,IAAIpB,MAAM,CAACiC,OAAO;EAAC,GACzGhD,KAAA,CAAA2C,aAAA,CAACX,UAAU,EAAAY,MAAA,CAAAC,MAAA;IACTC,SAAS,EAAE5C,IAAI,CAACa,MAAM,CAACqC,OAAO,EAAErC,MAAM,CAAC,mBAAmB2B,eAAe,EAAE,CAAC,CAAC;IAC7EO,GAAG,EAAEtB,eAAe;IACpB0B,QAAQ,EAAEzB;EAAoB,GAC1BzB,0BAA0B,CAAC,IAAIW,kBAAkB,CAAC,cAAc,CAAC,EAAE,CAAC,GAExEd,KAAA,CAAA2C,aAAA,SAAAC,MAAA,CAAAC,MAAA,KACOP,aAAa,GAAG;IAAE,CAACjC,oBAAoB,GAAGC;EAAuB,CAAE,GAAG,EAAE,EAAC;IAC9EwC,SAAS,EAAE5C,IAAI,CACba,MAAM,CAAC,cAAc,CAAC,EACtBD,kBAAkB,CAAC,cAAc,CAAC,EAClCC,MAAM,CAAC,wBAAwB2B,eAAe,EAAE,CAAC,CAClD;IACDY,EAAE,EAAEf;EAAS,IAEZnB,QAAQ,CACJ,EACNE,OAAO,KAAKkB,SAAS,IAAIxC,KAAA,CAAA2C,aAAA;IAAMG,SAAS,EAAE/B,MAAM,CAACO;EAAO,G,KAAIA,OAAO,CAAQ,CACjE,EACZE,IAAI,IACHxB,KAAA,CAAA2C,aAAA,CAACjC,oBAAoB,CAAC6C,QAAQ;IAACC,KAAK,EAAEjB;EAAS,GAE7CvC,KAAA,CAAA2C,aAAA;IAAMG,SAAS,EAAE/B,MAAM,CAAC,eAAe;EAAC,aAAgB,EACxDf,KAAA,CAAA2C,aAAA;IAAMG,SAAS,EAAE/B,MAAM,CAACS;EAAI,GAAGA,IAAI,CAAQ,CAE9C,CACG,EACLH,OAAO,IACNrB,KAAA,CAAA2C,aAAA;IACEG,SAAS,EAAE5C,IAAI,CACba,MAAM,CAACM,OAAO,EACdN,MAAM,CAAC,mBAAmB2B,eAAe,EAAE,CAAC,EAC5CP,SAAS,IAAIpB,MAAM,CAACiC,OAAO,EAC3B,CAACtB,wBAAwB,IAAI,CAACX,MAAM,CAAC,kBAAkB,CAAC,CAAC;EAC1D,GAEAM,OAAO,CAEX,CACG,EACNrB,KAAA,CAAA2C,aAAA,CAACc,WAAW;IAACf,eAAe,EAAEA;EAAe,GAAGnB,WAAW,CAAe,CACtE;AAEV;AAEA,OAAM,SAAUkC,WAAWA,CAAC;EAAErC,QAAQ;EAAEsB;AAAe,CAA0D;EAC/G,MAAMP,SAAS,GAAGtB,gBAAgB,EAAE;EACpC,OACGO,QAAQ,IACPpB,KAAA,CAAA2C,aAAA;IACEG,SAAS,EAAE5C,IAAI,CACba,MAAM,CAACQ,WAAW,EAClBR,MAAM,CAAC,uBAAuB2B,eAAe,EAAE,CAAC,EAChDP,SAAS,IAAIpB,MAAM,CAACiC,OAAO;EAC5B,GAEA5B,QAAQ,CAEZ,IACD,IAAI;AAER", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}