{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n// Finds the longest property the filtering text starts from.\nexport function matchFilteringProperty(filteringProperties, filteringText) {\n  let maxLength = 0;\n  let matchedProperty = null;\n  for (const property of filteringProperties) {\n    if (property.propertyLabel.length >= maxLength && startsWith(filteringText, property.propertyLabel) || property.propertyLabel.length > maxLength && startsWith(filteringText.toLowerCase(), property.propertyLabel.toLowerCase())) {\n      maxLength = property.propertyLabel.length;\n      matchedProperty = property;\n    }\n  }\n  return matchedProperty;\n}\n// Finds the longest operator the filtering text starts from.\nexport function matchOperator(allowedOperators, filteringText) {\n  filteringText = filteringText.toLowerCase();\n  let maxLength = 0;\n  let matchedOperator = null;\n  for (const operator of allowedOperators) {\n    if (operator.length > maxLength && startsWith(filteringText, operator.toLowerCase())) {\n      maxLength = operator.length;\n      matchedOperator = operator;\n    }\n  }\n  return matchedOperator;\n}\n// Finds if the filtering text matches any operator prefix.\nexport function matchOperatorPrefix(allowedOperators, filteringText) {\n  if (filteringText.trim().length === 0) {\n    return '';\n  }\n  for (const operator of allowedOperators) {\n    if (startsWith(operator.toLowerCase(), filteringText.toLowerCase())) {\n      return filteringText;\n    }\n  }\n  return null;\n}\nexport function matchTokenValue({\n  property,\n  operator,\n  value\n}, filteringOptions) {\n  var _a, _b;\n  const tokenType = property === null || property === void 0 ? void 0 : property.getTokenType(operator);\n  const propertyOptions = filteringOptions.filter(option => option.property === property);\n  const castValue = value => {\n    if (value === null) {\n      return tokenType === 'enum' ? [] : null;\n    }\n    return tokenType === 'enum' && !Array.isArray(value) ? [value] : value;\n  };\n  const bestMatch = {\n    propertyKey: property === null || property === void 0 ? void 0 : property.propertyKey,\n    operator,\n    value: castValue(value)\n  };\n  for (const option of propertyOptions) {\n    if (option.label && option.label === value || !option.label && option.value === value) {\n      // exact match found: return it\n      return {\n        propertyKey: property === null || property === void 0 ? void 0 : property.propertyKey,\n        operator,\n        value: castValue(option.value)\n      };\n    }\n    // By default, the token value is a string, but when a custom property is used,\n    // the token value can be any, therefore we need to check for its type before calling toLowerCase()\n    if (typeof value === 'string' && value.toLowerCase() === ((_b = (_a = option.label) !== null && _a !== void 0 ? _a : option.value) !== null && _b !== void 0 ? _b : '').toLowerCase()) {\n      // non-exact match: save and keep running in case exact match found later\n      bestMatch.value = castValue(option.value);\n    }\n  }\n  return bestMatch;\n}\nexport function trimStart(source) {\n  let spacesLength = 0;\n  for (let i = 0; i < source.length; i++) {\n    if (source[i] === ' ') {\n      spacesLength++;\n    } else {\n      break;\n    }\n  }\n  return source.slice(spacesLength);\n}\nfunction trimFirstSpace(source) {\n  return source[0] === ' ' ? source.slice(1) : source;\n}\nexport function removeOperator(source, operator) {\n  const operatorLastIndex = source.indexOf(operator) + operator.length;\n  const textWithoutOperator = source.slice(operatorLastIndex);\n  // We need to remove the first leading space in case the user presses space\n  // after the operator, for example: Owner: admin, will result in value of ` admin`\n  // and we need to remove the first space, if the user added any more spaces only the\n  // first one will be removed.\n  return trimFirstSpace(textWithoutOperator);\n}\nfunction startsWith(source, target) {\n  return source.indexOf(target) === 0;\n}\n/**\n * Transforms query token groups to tokens (only taking 1 level of nesting).\n */\nexport function tokenGroupToTokens(tokenGroups) {\n  const tokens = [];\n  for (const tokenOrGroup of tokenGroups) {\n    if ('operator' in tokenOrGroup) {\n      tokens.push(tokenOrGroup);\n    } else {\n      for (const nestedTokenOrGroup of tokenOrGroup.tokens) {\n        if ('operator' in nestedTokenOrGroup) {\n          tokens.push(nestedTokenOrGroup);\n        } else {\n          // Ignore deeply nested tokens\n        }\n      }\n    }\n  }\n  return tokens;\n}", "map": {"version": 3, "names": ["matchFilteringProperty", "filteringProperties", "filteringText", "max<PERSON><PERSON><PERSON>", "matchedProperty", "property", "propertyLabel", "length", "startsWith", "toLowerCase", "matchOperator", "allowedOperators", "matchedOperator", "operator", "matchOperatorPrefix", "trim", "matchTokenValue", "value", "filteringOptions", "tokenType", "getTokenType", "propertyOptions", "filter", "option", "cast<PERSON><PERSON><PERSON>", "Array", "isArray", "bestMatch", "propertyKey", "label", "_b", "_a", "trimStart", "source", "spacesLength", "i", "slice", "trimFirstSpace", "removeOperator", "operatorLastIndex", "indexOf", "textWithoutOperator", "target", "tokenGroupToTokens", "tokenGroups", "tokens", "tokenOrGroup", "push", "nestedTokenOrGroup"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\property-filter\\utils.ts"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\n\nimport {\n  ComparisonOperator,\n  InternalFilteringOption,\n  InternalFilteringProperty,\n  InternalToken,\n  Token,\n} from './interfaces';\n\n// Finds the longest property the filtering text starts from.\nexport function matchFilteringProperty(\n  filteringProperties: readonly InternalFilteringProperty[],\n  filteringText: string\n): null | InternalFilteringProperty {\n  let maxLength = 0;\n  let matchedProperty: null | InternalFilteringProperty = null;\n\n  for (const property of filteringProperties) {\n    if (\n      (property.propertyLabel.length >= maxLength && startsWith(filteringText, property.propertyLabel)) ||\n      (property.propertyLabel.length > maxLength &&\n        startsWith(filteringText.toLowerCase(), property.propertyLabel.toLowerCase()))\n    ) {\n      maxLength = property.propertyLabel.length;\n      matchedProperty = property;\n    }\n  }\n\n  return matchedProperty;\n}\n\n// Finds the longest operator the filtering text starts from.\nexport function matchOperator(\n  allowedOperators: readonly ComparisonOperator[],\n  filteringText: string\n): null | ComparisonOperator {\n  filteringText = filteringText.toLowerCase();\n\n  let maxLength = 0;\n  let matchedOperator: null | ComparisonOperator = null;\n\n  for (const operator of allowedOperators) {\n    if (operator.length > maxLength && startsWith(filteringText, operator.toLowerCase())) {\n      maxLength = operator.length;\n      matchedOperator = operator;\n    }\n  }\n\n  return matchedOperator;\n}\n\n// Finds if the filtering text matches any operator prefix.\nexport function matchOperatorPrefix(\n  allowedOperators: readonly ComparisonOperator[],\n  filteringText: string\n): null | string {\n  if (filteringText.trim().length === 0) {\n    return '';\n  }\n  for (const operator of allowedOperators) {\n    if (startsWith(operator.toLowerCase(), filteringText.toLowerCase())) {\n      return filteringText;\n    }\n  }\n  return null;\n}\n\nexport function matchTokenValue(\n  { property, operator, value }: InternalToken,\n  filteringOptions: readonly InternalFilteringOption[]\n): Token {\n  const tokenType = property?.getTokenType(operator);\n  const propertyOptions = filteringOptions.filter(option => option.property === property);\n  const castValue = (value: unknown) => {\n    if (value === null) {\n      return tokenType === 'enum' ? [] : null;\n    }\n    return tokenType === 'enum' && !Array.isArray(value) ? [value] : value;\n  };\n  const bestMatch: Token = { propertyKey: property?.propertyKey, operator, value: castValue(value) };\n\n  for (const option of propertyOptions) {\n    if ((option.label && option.label === value) || (!option.label && option.value === value)) {\n      // exact match found: return it\n      return { propertyKey: property?.propertyKey, operator, value: castValue(option.value) };\n    }\n\n    // By default, the token value is a string, but when a custom property is used,\n    // the token value can be any, therefore we need to check for its type before calling toLowerCase()\n    if (typeof value === 'string' && value.toLowerCase() === (option.label ?? option.value ?? '').toLowerCase()) {\n      // non-exact match: save and keep running in case exact match found later\n      bestMatch.value = castValue(option.value);\n    }\n  }\n\n  return bestMatch;\n}\n\nexport function trimStart(source: string): string {\n  let spacesLength = 0;\n  for (let i = 0; i < source.length; i++) {\n    if (source[i] === ' ') {\n      spacesLength++;\n    } else {\n      break;\n    }\n  }\n  return source.slice(spacesLength);\n}\n\nfunction trimFirstSpace(source: string): string {\n  return source[0] === ' ' ? source.slice(1) : source;\n}\n\nexport function removeOperator(source: string, operator: string) {\n  const operatorLastIndex = source.indexOf(operator) + operator.length;\n  const textWithoutOperator = source.slice(operatorLastIndex);\n  // We need to remove the first leading space in case the user presses space\n  // after the operator, for example: Owner: admin, will result in value of ` admin`\n  // and we need to remove the first space, if the user added any more spaces only the\n  // first one will be removed.\n  return trimFirstSpace(textWithoutOperator);\n}\n\nfunction startsWith(source: string, target: string): boolean {\n  return source.indexOf(target) === 0;\n}\n\ninterface AbstractToken {\n  operator: any;\n}\n\ninterface AbstractTokenGroup<T extends AbstractToken> {\n  operation: any;\n  tokens: readonly (T | AbstractTokenGroup<T>)[];\n}\n\n/**\n * Transforms query token groups to tokens (only taking 1 level of nesting).\n */\nexport function tokenGroupToTokens<T extends AbstractToken>(tokenGroups: readonly (T | AbstractTokenGroup<T>)[]): T[] {\n  const tokens: T[] = [];\n  for (const tokenOrGroup of tokenGroups) {\n    if ('operator' in tokenOrGroup) {\n      tokens.push(tokenOrGroup);\n    } else {\n      for (const nestedTokenOrGroup of tokenOrGroup.tokens) {\n        if ('operator' in nestedTokenOrGroup) {\n          tokens.push(nestedTokenOrGroup);\n        } else {\n          // Ignore deeply nested tokens\n        }\n      }\n    }\n  }\n  return tokens;\n}\n"], "mappings": "AAAA;AACA;AAUA;AACA,OAAM,SAAUA,sBAAsBA,CACpCC,mBAAyD,EACzDC,aAAqB;EAErB,IAAIC,SAAS,GAAG,CAAC;EACjB,IAAIC,eAAe,GAAqC,IAAI;EAE5D,KAAK,MAAMC,QAAQ,IAAIJ,mBAAmB,EAAE;IAC1C,IACGI,QAAQ,CAACC,aAAa,CAACC,MAAM,IAAIJ,SAAS,IAAIK,UAAU,CAACN,aAAa,EAAEG,QAAQ,CAACC,aAAa,CAAC,IAC/FD,QAAQ,CAACC,aAAa,CAACC,MAAM,GAAGJ,SAAS,IACxCK,UAAU,CAACN,aAAa,CAACO,WAAW,EAAE,EAAEJ,QAAQ,CAACC,aAAa,CAACG,WAAW,EAAE,CAAE,EAChF;MACAN,SAAS,GAAGE,QAAQ,CAACC,aAAa,CAACC,MAAM;MACzCH,eAAe,GAAGC,QAAQ;;;EAI9B,OAAOD,eAAe;AACxB;AAEA;AACA,OAAM,SAAUM,aAAaA,CAC3BC,gBAA+C,EAC/CT,aAAqB;EAErBA,aAAa,GAAGA,aAAa,CAACO,WAAW,EAAE;EAE3C,IAAIN,SAAS,GAAG,CAAC;EACjB,IAAIS,eAAe,GAA8B,IAAI;EAErD,KAAK,MAAMC,QAAQ,IAAIF,gBAAgB,EAAE;IACvC,IAAIE,QAAQ,CAACN,MAAM,GAAGJ,SAAS,IAAIK,UAAU,CAACN,aAAa,EAAEW,QAAQ,CAACJ,WAAW,EAAE,CAAC,EAAE;MACpFN,SAAS,GAAGU,QAAQ,CAACN,MAAM;MAC3BK,eAAe,GAAGC,QAAQ;;;EAI9B,OAAOD,eAAe;AACxB;AAEA;AACA,OAAM,SAAUE,mBAAmBA,CACjCH,gBAA+C,EAC/CT,aAAqB;EAErB,IAAIA,aAAa,CAACa,IAAI,EAAE,CAACR,MAAM,KAAK,CAAC,EAAE;IACrC,OAAO,EAAE;;EAEX,KAAK,MAAMM,QAAQ,IAAIF,gBAAgB,EAAE;IACvC,IAAIH,UAAU,CAACK,QAAQ,CAACJ,WAAW,EAAE,EAAEP,aAAa,CAACO,WAAW,EAAE,CAAC,EAAE;MACnE,OAAOP,aAAa;;;EAGxB,OAAO,IAAI;AACb;AAEA,OAAM,SAAUc,eAAeA,CAC7B;EAAEX,QAAQ;EAAEQ,QAAQ;EAAEI;AAAK,CAAiB,EAC5CC,gBAAoD;;EAEpD,MAAMC,SAAS,GAAGd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,YAAY,CAACP,QAAQ,CAAC;EAClD,MAAMQ,eAAe,GAAGH,gBAAgB,CAACI,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAClB,QAAQ,KAAKA,QAAQ,CAAC;EACvF,MAAMmB,SAAS,GAAIP,KAAc,IAAI;IACnC,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClB,OAAOE,SAAS,KAAK,MAAM,GAAG,EAAE,GAAG,IAAI;;IAEzC,OAAOA,SAAS,KAAK,MAAM,IAAI,CAACM,KAAK,CAACC,OAAO,CAACT,KAAK,CAAC,GAAG,CAACA,KAAK,CAAC,GAAGA,KAAK;EACxE,CAAC;EACD,MAAMU,SAAS,GAAU;IAAEC,WAAW,EAAEvB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuB,WAAW;IAAEf,QAAQ;IAAEI,KAAK,EAAEO,SAAS,CAACP,KAAK;EAAC,CAAE;EAElG,KAAK,MAAMM,MAAM,IAAIF,eAAe,EAAE;IACpC,IAAKE,MAAM,CAACM,KAAK,IAAIN,MAAM,CAACM,KAAK,KAAKZ,KAAK,IAAM,CAACM,MAAM,CAACM,KAAK,IAAIN,MAAM,CAACN,KAAK,KAAKA,KAAM,EAAE;MACzF;MACA,OAAO;QAAEW,WAAW,EAAEvB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuB,WAAW;QAAEf,QAAQ;QAAEI,KAAK,EAAEO,SAAS,CAACD,MAAM,CAACN,KAAK;MAAC,CAAE;;IAGzF;IACA;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACR,WAAW,EAAE,KAAK,CAAC,CAAAqB,EAAA,IAAAC,EAAA,GAAAR,MAAM,CAACM,KAAK,cAAAE,EAAA,cAAAA,EAAA,GAAIR,MAAM,CAACN,KAAK,cAAAa,EAAA,cAAAA,EAAA,GAAI,EAAE,EAAErB,WAAW,EAAE,EAAE;MAC3G;MACAkB,SAAS,CAACV,KAAK,GAAGO,SAAS,CAACD,MAAM,CAACN,KAAK,CAAC;;;EAI7C,OAAOU,SAAS;AAClB;AAEA,OAAM,SAAUK,SAASA,CAACC,MAAc;EACtC,IAAIC,YAAY,GAAG,CAAC;EACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAAC1B,MAAM,EAAE4B,CAAC,EAAE,EAAE;IACtC,IAAIF,MAAM,CAACE,CAAC,CAAC,KAAK,GAAG,EAAE;MACrBD,YAAY,EAAE;KACf,MAAM;MACL;;;EAGJ,OAAOD,MAAM,CAACG,KAAK,CAACF,YAAY,CAAC;AACnC;AAEA,SAASG,cAAcA,CAACJ,MAAc;EACpC,OAAOA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGA,MAAM,CAACG,KAAK,CAAC,CAAC,CAAC,GAAGH,MAAM;AACrD;AAEA,OAAM,SAAUK,cAAcA,CAACL,MAAc,EAAEpB,QAAgB;EAC7D,MAAM0B,iBAAiB,GAAGN,MAAM,CAACO,OAAO,CAAC3B,QAAQ,CAAC,GAAGA,QAAQ,CAACN,MAAM;EACpE,MAAMkC,mBAAmB,GAAGR,MAAM,CAACG,KAAK,CAACG,iBAAiB,CAAC;EAC3D;EACA;EACA;EACA;EACA,OAAOF,cAAc,CAACI,mBAAmB,CAAC;AAC5C;AAEA,SAASjC,UAAUA,CAACyB,MAAc,EAAES,MAAc;EAChD,OAAOT,MAAM,CAACO,OAAO,CAACE,MAAM,CAAC,KAAK,CAAC;AACrC;AAWA;;;AAGA,OAAM,SAAUC,kBAAkBA,CAA0BC,WAAmD;EAC7G,MAAMC,MAAM,GAAQ,EAAE;EACtB,KAAK,MAAMC,YAAY,IAAIF,WAAW,EAAE;IACtC,IAAI,UAAU,IAAIE,YAAY,EAAE;MAC9BD,MAAM,CAACE,IAAI,CAACD,YAAY,CAAC;KAC1B,MAAM;MACL,KAAK,MAAME,kBAAkB,IAAIF,YAAY,CAACD,MAAM,EAAE;QACpD,IAAI,UAAU,IAAIG,kBAAkB,EAAE;UACpCH,MAAM,CAACE,IAAI,CAACC,kBAAkB,CAAC;SAChC,MAAM;UACL;QAAA;;;;EAKR,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}