{"ast": null, "code": "import { __rest } from \"tslib\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useEffect, useMemo, useState } from 'react';\nimport clsx from 'clsx';\nimport { warnOnce } from '@cloudscape-design/component-toolkit/internal';\nimport { getBaseProps } from '../internal/base-component';\nimport { fireNonCancelableEvent } from '../internal/events';\nimport useBaseComponent from '../internal/hooks/use-base-component';\nimport { useUniqueId } from '../internal/hooks/use-unique-id';\nimport { applyDisplayName } from '../internal/utils/apply-display-name';\nimport { joinStrings } from '../internal/utils/strings';\nimport { throttle } from '../internal/utils/throttle';\nimport InternalLiveRegion from '../live-region/internal';\nimport { Progress, ResultState, SmallText } from './internal';\nimport styles from './styles.css.js';\nconst ASSERTION_FREQUENCY = 5000; // interval in ms between progress announcements\nexport default function ProgressBar(_a) {\n  var {\n      value = 0,\n      status = 'in-progress',\n      variant = 'standalone',\n      resultButtonText,\n      label,\n      ariaLabel,\n      ariaLabelledby,\n      ariaDescribedby,\n      description,\n      additionalInfo,\n      resultText,\n      onResultButtonClick\n    } = _a,\n    rest = __rest(_a, [\"value\", \"status\", \"variant\", \"resultButtonText\", \"label\", \"ariaLabel\", \"ariaLabelledby\", \"ariaDescribedby\", \"description\", \"additionalInfo\", \"resultText\", \"onResultButtonClick\"]);\n  const {\n    __internalRootRef\n  } = useBaseComponent('ProgressBar', {\n    props: {\n      variant\n    }\n  });\n  const baseProps = getBaseProps(rest);\n  const generatedName = useUniqueId('awsui-progress-bar-');\n  const labelId = `${generatedName}-label`;\n  const isInFlash = variant === 'flash';\n  const isInProgressState = status === 'in-progress';\n  const descriptionId = useUniqueId('progressbar-description-');\n  const additionalInfoId = useUniqueId('progressbar-additional-info-');\n  const [announcedValue, setAnnouncedValue] = useState('');\n  const throttledAssertion = useMemo(() => {\n    return throttle(value => {\n      setAnnouncedValue(`${value}%`);\n    }, ASSERTION_FREQUENCY);\n  }, []);\n  useEffect(() => {\n    throttledAssertion(value);\n  }, [throttledAssertion, value]);\n  if (isInFlash && resultButtonText) {\n    warnOnce('ProgressBar', 'The `resultButtonText` is ignored if you set `variant=\"flash\"`, and the result button is not displayed. Use the `buttonText` property and the `onButtonClick` event listener of the flashbar item in which the progress bar component is embedded.');\n  }\n  return React.createElement(\"div\", Object.assign({}, baseProps, {\n    className: clsx(baseProps.className, styles.root, variant && styles[variant]),\n    ref: __internalRootRef\n  }), React.createElement(\"div\", {\n    className: isInFlash ? styles['flash-container'] : undefined\n  }, React.createElement(\"div\", {\n    className: clsx(styles['word-wrap'], styles[`label-${variant}`]),\n    id: labelId\n  }, label), description && React.createElement(SmallText, {\n    color: isInFlash ? 'inherit' : undefined,\n    id: descriptionId\n  }, description), React.createElement(\"div\", null, isInProgressState ? React.createElement(React.Fragment, null, React.createElement(Progress, {\n    value: value,\n    ariaLabel: ariaLabel,\n    ariaLabelledby: joinStrings(labelId, ariaLabelledby),\n    ariaDescribedby: joinStrings(description ? descriptionId : undefined, additionalInfo ? additionalInfoId : undefined, ariaDescribedby),\n    isInFlash: isInFlash\n  }), React.createElement(InternalLiveRegion, {\n    hidden: true,\n    tagName: \"span\",\n    delay: 0\n  }, label, label ? ': ' : null, announcedValue)) : React.createElement(InternalLiveRegion, {\n    hidden: false,\n    tagName: \"span\",\n    delay: 0\n  }, React.createElement(ResultState, {\n    resultText: resultText,\n    isInFlash: isInFlash,\n    resultButtonText: resultButtonText,\n    status: status,\n    onClick: () => {\n      fireNonCancelableEvent(onResultButtonClick);\n    }\n  })))), additionalInfo && React.createElement(SmallText, {\n    className: styles['additional-info'],\n    color: isInFlash ? 'inherit' : undefined,\n    id: additionalInfoId\n  }, additionalInfo));\n}\napplyDisplayName(ProgressBar, 'ProgressBar');", "map": {"version": 3, "names": ["React", "useEffect", "useMemo", "useState", "clsx", "warnOnce", "getBaseProps", "fireNonCancelableEvent", "useBaseComponent", "useUniqueId", "applyDisplayName", "joinStrings", "throttle", "InternalLiveRegion", "Progress", "ResultState", "SmallText", "styles", "ASSERTION_FREQUENCY", "ProgressBar", "_a", "value", "status", "variant", "resultButtonText", "label", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON><PERSON>", "description", "additionalInfo", "resultText", "onResultButtonClick", "rest", "__rest", "__internalRootRef", "props", "baseProps", "generatedName", "labelId", "isInFlash", "isInProgressState", "descriptionId", "additionalInfoId", "announcedValue", "setAnnouncedValue", "throttledAsser<PERSON>", "createElement", "Object", "assign", "className", "root", "ref", "undefined", "id", "color", "Fragment", "hidden", "tagName", "delay", "onClick"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\progress-bar\\index.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useEffect, useMemo, useState } from 'react';\nimport clsx from 'clsx';\n\nimport { warnOnce } from '@cloudscape-design/component-toolkit/internal';\n\nimport { getBaseProps } from '../internal/base-component';\nimport { fireNonCancelableEvent } from '../internal/events';\nimport useBaseComponent from '../internal/hooks/use-base-component';\nimport { useUniqueId } from '../internal/hooks/use-unique-id';\nimport { applyDisplayName } from '../internal/utils/apply-display-name';\nimport { joinStrings } from '../internal/utils/strings';\nimport { throttle } from '../internal/utils/throttle';\nimport InternalLiveRegion from '../live-region/internal';\nimport { ProgressBarProps } from './interfaces';\nimport { Progress, ResultState, SmallText } from './internal';\n\nimport styles from './styles.css.js';\n\nconst ASSERTION_FREQUENCY = 5000; // interval in ms between progress announcements\n\nexport { ProgressBarProps };\n\nexport default function ProgressBar({\n  value = 0,\n  status = 'in-progress',\n  variant = 'standalone',\n  resultButtonText,\n  label,\n  ariaLabel,\n  ariaLabelledby,\n  ariaDescribedby,\n  description,\n  additionalInfo,\n  resultText,\n  onResultButtonClick,\n  ...rest\n}: ProgressBarProps) {\n  const { __internalRootRef } = useBaseComponent('ProgressBar', {\n    props: { variant },\n  });\n  const baseProps = getBaseProps(rest);\n  const generatedName = useUniqueId('awsui-progress-bar-');\n\n  const labelId = `${generatedName}-label`;\n  const isInFlash = variant === 'flash';\n  const isInProgressState = status === 'in-progress';\n\n  const descriptionId = useUniqueId('progressbar-description-');\n  const additionalInfoId = useUniqueId('progressbar-additional-info-');\n\n  const [announcedValue, setAnnouncedValue] = useState('');\n  const throttledAssertion = useMemo(() => {\n    return throttle((value: ProgressBarProps['value']) => {\n      setAnnouncedValue(`${value}%`);\n    }, ASSERTION_FREQUENCY);\n  }, []);\n\n  useEffect(() => {\n    throttledAssertion(value);\n  }, [throttledAssertion, value]);\n\n  if (isInFlash && resultButtonText) {\n    warnOnce(\n      'ProgressBar',\n      'The `resultButtonText` is ignored if you set `variant=\"flash\"`, and the result button is not displayed. Use the `buttonText` property and the `onButtonClick` event listener of the flashbar item in which the progress bar component is embedded.'\n    );\n  }\n\n  return (\n    <div\n      {...baseProps}\n      className={clsx(baseProps.className, styles.root, variant && styles[variant])}\n      ref={__internalRootRef}\n    >\n      <div className={isInFlash ? styles['flash-container'] : undefined}>\n        <div className={clsx(styles['word-wrap'], styles[`label-${variant}`])} id={labelId}>\n          {label}\n        </div>\n        {description && (\n          <SmallText color={isInFlash ? 'inherit' : undefined} id={descriptionId}>\n            {description}\n          </SmallText>\n        )}\n        <div>\n          {isInProgressState ? (\n            <>\n              <Progress\n                value={value}\n                ariaLabel={ariaLabel}\n                ariaLabelledby={joinStrings(labelId, ariaLabelledby)}\n                ariaDescribedby={joinStrings(\n                  description ? descriptionId : undefined,\n                  additionalInfo ? additionalInfoId : undefined,\n                  ariaDescribedby\n                )}\n                isInFlash={isInFlash}\n              />\n              <InternalLiveRegion hidden={true} tagName=\"span\" delay={0}>\n                {label}\n                {label ? ': ' : null}\n                {announcedValue}\n              </InternalLiveRegion>\n            </>\n          ) : (\n            <InternalLiveRegion hidden={false} tagName=\"span\" delay={0}>\n              <ResultState\n                resultText={resultText}\n                isInFlash={isInFlash}\n                resultButtonText={resultButtonText}\n                status={status}\n                onClick={() => {\n                  fireNonCancelableEvent(onResultButtonClick);\n                }}\n              />\n            </InternalLiveRegion>\n          )}\n        </div>\n      </div>\n      {additionalInfo && (\n        <SmallText\n          className={styles['additional-info']}\n          color={isInFlash ? 'inherit' : undefined}\n          id={additionalInfoId}\n        >\n          {additionalInfo}\n        </SmallText>\n      )}\n    </div>\n  );\n}\n\napplyDisplayName(ProgressBar, 'ProgressBar');\n"], "mappings": ";AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AAC3D,OAAOC,IAAI,MAAM,MAAM;AAEvB,SAASC,QAAQ,QAAQ,+CAA+C;AAExE,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,sBAAsB,QAAQ,oBAAoB;AAC3D,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,SAASC,WAAW,QAAQ,iCAAiC;AAC7D,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAOC,kBAAkB,MAAM,yBAAyB;AAExD,SAASC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,YAAY;AAE7D,OAAOC,MAAM,MAAM,iBAAiB;AAEpC,MAAMC,mBAAmB,GAAG,IAAI,CAAC,CAAC;AAIlC,eAAc,SAAUC,WAAWA,CAACC,EAcjB;MAdiB;MAClCC,KAAK,GAAG,CAAC;MACTC,MAAM,GAAG,aAAa;MACtBC,OAAO,GAAG,YAAY;MACtBC,gBAAgB;MAChBC,KAAK;MACLC,SAAS;MACTC,cAAc;MACdC,eAAe;MACfC,WAAW;MACXC,cAAc;MACdC,UAAU;MACVC;IAAmB,IAAAZ,EAEF;IADda,IAAI,GAAAC,MAAA,CAAAd,EAAA,EAb2B,mLAcnC,CADQ;EAEP,MAAM;IAAEe;EAAiB,CAAE,GAAG3B,gBAAgB,CAAC,aAAa,EAAE;IAC5D4B,KAAK,EAAE;MAAEb;IAAO;GACjB,CAAC;EACF,MAAMc,SAAS,GAAG/B,YAAY,CAAC2B,IAAI,CAAC;EACpC,MAAMK,aAAa,GAAG7B,WAAW,CAAC,qBAAqB,CAAC;EAExD,MAAM8B,OAAO,GAAG,GAAGD,aAAa,QAAQ;EACxC,MAAME,SAAS,GAAGjB,OAAO,KAAK,OAAO;EACrC,MAAMkB,iBAAiB,GAAGnB,MAAM,KAAK,aAAa;EAElD,MAAMoB,aAAa,GAAGjC,WAAW,CAAC,0BAA0B,CAAC;EAC7D,MAAMkC,gBAAgB,GAAGlC,WAAW,CAAC,8BAA8B,CAAC;EAEpE,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM2C,kBAAkB,GAAG5C,OAAO,CAAC,MAAK;IACtC,OAAOU,QAAQ,CAAES,KAAgC,IAAI;MACnDwB,iBAAiB,CAAC,GAAGxB,KAAK,GAAG,CAAC;IAChC,CAAC,EAAEH,mBAAmB,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAENjB,SAAS,CAAC,MAAK;IACb6C,kBAAkB,CAACzB,KAAK,CAAC;EAC3B,CAAC,EAAE,CAACyB,kBAAkB,EAAEzB,KAAK,CAAC,CAAC;EAE/B,IAAImB,SAAS,IAAIhB,gBAAgB,EAAE;IACjCnB,QAAQ,CACN,aAAa,EACb,oPAAoP,CACrP;;EAGH,OACEL,KAAA,CAAA+C,aAAA,QAAAC,MAAA,CAAAC,MAAA,KACMZ,SAAS;IACba,SAAS,EAAE9C,IAAI,CAACiC,SAAS,CAACa,SAAS,EAAEjC,MAAM,CAACkC,IAAI,EAAE5B,OAAO,IAAIN,MAAM,CAACM,OAAO,CAAC,CAAC;IAC7E6B,GAAG,EAAEjB;EAAiB,IAEtBnC,KAAA,CAAA+C,aAAA;IAAKG,SAAS,EAAEV,SAAS,GAAGvB,MAAM,CAAC,iBAAiB,CAAC,GAAGoC;EAAS,GAC/DrD,KAAA,CAAA+C,aAAA;IAAKG,SAAS,EAAE9C,IAAI,CAACa,MAAM,CAAC,WAAW,CAAC,EAAEA,MAAM,CAAC,SAASM,OAAO,EAAE,CAAC,CAAC;IAAE+B,EAAE,EAAEf;EAAO,GAC/Ed,KAAK,CACF,EACLI,WAAW,IACV7B,KAAA,CAAA+C,aAAA,CAAC/B,SAAS;IAACuC,KAAK,EAAEf,SAAS,GAAG,SAAS,GAAGa,SAAS;IAAEC,EAAE,EAAEZ;EAAa,GACnEb,WAAW,CAEf,EACD7B,KAAA,CAAA+C,aAAA,cACGN,iBAAiB,GAChBzC,KAAA,CAAA+C,aAAA,CAAA/C,KAAA,CAAAwD,QAAA,QACExD,KAAA,CAAA+C,aAAA,CAACjC,QAAQ;IACPO,KAAK,EAAEA,KAAK;IACZK,SAAS,EAAEA,SAAS;IACpBC,cAAc,EAAEhB,WAAW,CAAC4B,OAAO,EAAEZ,cAAc,CAAC;IACpDC,eAAe,EAAEjB,WAAW,CAC1BkB,WAAW,GAAGa,aAAa,GAAGW,SAAS,EACvCvB,cAAc,GAAGa,gBAAgB,GAAGU,SAAS,EAC7CzB,eAAe,CAChB;IACDY,SAAS,EAAEA;EAAS,EACpB,EACFxC,KAAA,CAAA+C,aAAA,CAAClC,kBAAkB;IAAC4C,MAAM,EAAE,IAAI;IAAEC,OAAO,EAAC,MAAM;IAACC,KAAK,EAAE;EAAC,GACtDlC,KAAK,EACLA,KAAK,GAAG,IAAI,GAAG,IAAI,EACnBmB,cAAc,CACI,CACpB,GAEH5C,KAAA,CAAA+C,aAAA,CAAClC,kBAAkB;IAAC4C,MAAM,EAAE,KAAK;IAAEC,OAAO,EAAC,MAAM;IAACC,KAAK,EAAE;EAAC,GACxD3D,KAAA,CAAA+C,aAAA,CAAChC,WAAW;IACVgB,UAAU,EAAEA,UAAU;IACtBS,SAAS,EAAEA,SAAS;IACpBhB,gBAAgB,EAAEA,gBAAgB;IAClCF,MAAM,EAAEA,MAAM;IACdsC,OAAO,EAAEA,CAAA,KAAK;MACZrD,sBAAsB,CAACyB,mBAAmB,CAAC;IAC7C;EAAC,EACD,CAEL,CACG,CACF,EACLF,cAAc,IACb9B,KAAA,CAAA+C,aAAA,CAAC/B,SAAS;IACRkC,SAAS,EAAEjC,MAAM,CAAC,iBAAiB,CAAC;IACpCsC,KAAK,EAAEf,SAAS,GAAG,SAAS,GAAGa,SAAS;IACxCC,EAAE,EAAEX;EAAgB,GAEnBb,cAAc,CAElB,CACG;AAEV;AAEApB,gBAAgB,CAACS,WAAW,EAAE,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}