{"ast": null, "code": "// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst AWSUI_EVENT = 'awsui';\nfunction validateLength(value, maxLength) {\n  return !value || value.length <= maxLength;\n}\n/**\n * Console Platform's client logging JS API client.\n */\nexport class CLogClient {\n  /**\n   * Sends metric but only if Console Platform client logging JS API is present in the page.\n   */\n  sendMetric(metricName, value, detail) {\n    if (!metricName || !/^[a-zA-Z0-9_-]+$/.test(metricName)) {\n      console.error(`Invalid metric name: ${metricName}`);\n      return;\n    }\n    if (!validateLength(metricName, 1000)) {\n      console.error(`Metric name ${metricName} is too long`);\n      return;\n    }\n    if (!validateLength(detail, 4000)) {\n      console.error(`Detail for metric ${metricName} is too long: ${detail}`);\n      return;\n    }\n    const wasSent = new PanoramaClient().sendMetric({\n      eventContext: metricName,\n      eventDetail: detail,\n      eventValue: `${value}`,\n      timestamp: Date.now()\n    });\n    if (wasSent) {\n      return;\n    }\n    const AWSC = this.findAWSC(window);\n    if (typeof AWSC === 'object' && typeof AWSC.Clog === 'object' && typeof AWSC.Clog.log === 'function') {\n      AWSC.Clog.log(metricName, value, detail);\n    }\n  }\n  findAWSC(currentWindow) {\n    try {\n      if (typeof (currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.AWSC) === 'object') {\n        return currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.AWSC;\n      }\n      if (!currentWindow || currentWindow.parent === currentWindow) {\n        // When the window has no more parents, it references itself\n        return undefined;\n      }\n      return this.findAWSC(currentWindow.parent);\n    } catch (ex) {\n      // Most likely a cross-origin access error\n      return undefined;\n    }\n  }\n}\n/**\n * Console Platform's client v2 logging JS API client.\n */\nexport class PanoramaClient {\n  /**\n   * Sends metric but only if Console Platform client v2 logging JS API is present in the page.\n   */\n  sendMetric(metric) {\n    const panorama = this.findPanorama(window);\n    if (!panorama) {\n      return false;\n    }\n    const payload = Object.assign(Object.assign({\n      eventType: AWSUI_EVENT,\n      timestamp: Date.now()\n    }, metric), {\n      eventDetail: typeof metric.eventDetail === 'object' ? JSON.stringify(metric.eventDetail) : metric.eventDetail,\n      eventValue: typeof metric.eventValue === 'object' ? JSON.stringify(metric.eventValue) : metric.eventValue\n    });\n    if (!validateLength(payload.eventDetail, 4000)) {\n      this.onMetricError(`Event detail for metric is too long: ${payload.eventDetail}`);\n      return true;\n    }\n    if (!validateLength(payload.eventValue, 4000)) {\n      this.onMetricError(`Event value for metric is too long: ${payload.eventValue}`);\n      return true;\n    }\n    if (!validateLength(payload.eventContext, 4000)) {\n      this.onMetricError(`Event context for metric is too long: ${payload.eventContext}`);\n      return true;\n    }\n    panorama('trackCustomEvent', payload);\n    return true;\n  }\n  onMetricError(message) {\n    console.error(message);\n    const panorama = this.findPanorama(window);\n    if (panorama) {\n      panorama('trackCustomEvent', {\n        eventType: AWSUI_EVENT,\n        eventContext: 'awsui-metric-error',\n        eventDetail: message.slice(0, 4000),\n        timestamp: Date.now()\n      });\n    }\n  }\n  findPanorama(currentWindow) {\n    try {\n      if (typeof (currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.panorama) === 'function') {\n        return currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.panorama;\n      }\n      if (!currentWindow || currentWindow.parent === currentWindow) {\n        // When the window has no more parents, it references itself\n        return undefined;\n      }\n      return this.findPanorama(currentWindow.parent);\n    } catch (ex) {\n      // Most likely a cross-origin access error\n      return undefined;\n    }\n  }\n}", "map": {"version": 3, "names": ["AWSUI_EVENT", "validate<PERSON><PERSON>th", "value", "max<PERSON><PERSON><PERSON>", "length", "CLogClient", "sendMetric", "metricName", "detail", "test", "console", "error", "wasSent", "PanoramaClient", "eventContext", "eventDetail", "eventValue", "timestamp", "Date", "now", "AWSC", "findAWSC", "window", "Clog", "log", "currentWindow", "parent", "undefined", "ex", "metric", "panorama", "findPanorama", "payload", "Object", "assign", "eventType", "JSON", "stringify", "onMetricError", "message", "slice"], "sources": ["C:/Repos2025/Amazonians_App/App/node_modules/@cloudscape-design/component-toolkit/internal/base-component/metrics/log-clients.js"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nconst AWSUI_EVENT = 'awsui';\nfunction validateLength(value, maxLength) {\n    return !value || value.length <= maxLength;\n}\n/**\n * Console Platform's client logging JS API client.\n */\nexport class CLogClient {\n    /**\n     * Sends metric but only if Console Platform client logging JS API is present in the page.\n     */\n    sendMetric(metricName, value, detail) {\n        if (!metricName || !/^[a-zA-Z0-9_-]+$/.test(metricName)) {\n            console.error(`Invalid metric name: ${metricName}`);\n            return;\n        }\n        if (!validateLength(metricName, 1000)) {\n            console.error(`Metric name ${metricName} is too long`);\n            return;\n        }\n        if (!validateLength(detail, 4000)) {\n            console.error(`Detail for metric ${metricName} is too long: ${detail}`);\n            return;\n        }\n        const wasSent = new PanoramaClient().sendMetric({\n            eventContext: metricName,\n            eventDetail: detail,\n            eventValue: `${value}`,\n            timestamp: Date.now(),\n        });\n        if (wasSent) {\n            return;\n        }\n        const AWSC = this.findAWSC(window);\n        if (typeof AWSC === 'object' && typeof AWSC.Clog === 'object' && typeof AWSC.Clog.log === 'function') {\n            AWSC.Clog.log(metricName, value, detail);\n        }\n    }\n    findAWSC(currentWindow) {\n        try {\n            if (typeof (currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.AWSC) === 'object') {\n                return currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.AWSC;\n            }\n            if (!currentWindow || currentWindow.parent === currentWindow) {\n                // When the window has no more parents, it references itself\n                return undefined;\n            }\n            return this.findAWSC(currentWindow.parent);\n        }\n        catch (ex) {\n            // Most likely a cross-origin access error\n            return undefined;\n        }\n    }\n}\n/**\n * Console Platform's client v2 logging JS API client.\n */\nexport class PanoramaClient {\n    /**\n     * Sends metric but only if Console Platform client v2 logging JS API is present in the page.\n     */\n    sendMetric(metric) {\n        const panorama = this.findPanorama(window);\n        if (!panorama) {\n            return false;\n        }\n        const payload = Object.assign(Object.assign({ eventType: AWSUI_EVENT, timestamp: Date.now() }, metric), { eventDetail: typeof metric.eventDetail === 'object' ? JSON.stringify(metric.eventDetail) : metric.eventDetail, eventValue: typeof metric.eventValue === 'object' ? JSON.stringify(metric.eventValue) : metric.eventValue });\n        if (!validateLength(payload.eventDetail, 4000)) {\n            this.onMetricError(`Event detail for metric is too long: ${payload.eventDetail}`);\n            return true;\n        }\n        if (!validateLength(payload.eventValue, 4000)) {\n            this.onMetricError(`Event value for metric is too long: ${payload.eventValue}`);\n            return true;\n        }\n        if (!validateLength(payload.eventContext, 4000)) {\n            this.onMetricError(`Event context for metric is too long: ${payload.eventContext}`);\n            return true;\n        }\n        panorama('trackCustomEvent', payload);\n        return true;\n    }\n    onMetricError(message) {\n        console.error(message);\n        const panorama = this.findPanorama(window);\n        if (panorama) {\n            panorama('trackCustomEvent', {\n                eventType: AWSUI_EVENT,\n                eventContext: 'awsui-metric-error',\n                eventDetail: message.slice(0, 4000),\n                timestamp: Date.now(),\n            });\n        }\n    }\n    findPanorama(currentWindow) {\n        try {\n            if (typeof (currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.panorama) === 'function') {\n                return currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.panorama;\n            }\n            if (!currentWindow || currentWindow.parent === currentWindow) {\n                // When the window has no more parents, it references itself\n                return undefined;\n            }\n            return this.findPanorama(currentWindow.parent);\n        }\n        catch (ex) {\n            // Most likely a cross-origin access error\n            return undefined;\n        }\n    }\n}\n"], "mappings": "AAAA;AACA;AACA,MAAMA,WAAW,GAAG,OAAO;AAC3B,SAASC,cAAcA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACtC,OAAO,CAACD,KAAK,IAAIA,KAAK,CAACE,MAAM,IAAID,SAAS;AAC9C;AACA;AACA;AACA;AACA,OAAO,MAAME,UAAU,CAAC;EACpB;AACJ;AACA;EACIC,UAAUA,CAACC,UAAU,EAAEL,KAAK,EAAEM,MAAM,EAAE;IAClC,IAAI,CAACD,UAAU,IAAI,CAAC,kBAAkB,CAACE,IAAI,CAACF,UAAU,CAAC,EAAE;MACrDG,OAAO,CAACC,KAAK,CAAC,wBAAwBJ,UAAU,EAAE,CAAC;MACnD;IACJ;IACA,IAAI,CAACN,cAAc,CAACM,UAAU,EAAE,IAAI,CAAC,EAAE;MACnCG,OAAO,CAACC,KAAK,CAAC,eAAeJ,UAAU,cAAc,CAAC;MACtD;IACJ;IACA,IAAI,CAACN,cAAc,CAACO,MAAM,EAAE,IAAI,CAAC,EAAE;MAC/BE,OAAO,CAACC,KAAK,CAAC,qBAAqBJ,UAAU,iBAAiBC,MAAM,EAAE,CAAC;MACvE;IACJ;IACA,MAAMI,OAAO,GAAG,IAAIC,cAAc,CAAC,CAAC,CAACP,UAAU,CAAC;MAC5CQ,YAAY,EAAEP,UAAU;MACxBQ,WAAW,EAAEP,MAAM;MACnBQ,UAAU,EAAE,GAAGd,KAAK,EAAE;MACtBe,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACxB,CAAC,CAAC;IACF,IAAIP,OAAO,EAAE;MACT;IACJ;IACA,MAAMQ,IAAI,GAAG,IAAI,CAACC,QAAQ,CAACC,MAAM,CAAC;IAClC,IAAI,OAAOF,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,CAACG,IAAI,KAAK,QAAQ,IAAI,OAAOH,IAAI,CAACG,IAAI,CAACC,GAAG,KAAK,UAAU,EAAE;MAClGJ,IAAI,CAACG,IAAI,CAACC,GAAG,CAACjB,UAAU,EAAEL,KAAK,EAAEM,MAAM,CAAC;IAC5C;EACJ;EACAa,QAAQA,CAACI,aAAa,EAAE;IACpB,IAAI;MACA,IAAI,QAAQA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACL,IAAI,CAAC,KAAK,QAAQ,EAAE;QACxG,OAAOK,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACL,IAAI;MAC3F;MACA,IAAI,CAACK,aAAa,IAAIA,aAAa,CAACC,MAAM,KAAKD,aAAa,EAAE;QAC1D;QACA,OAAOE,SAAS;MACpB;MACA,OAAO,IAAI,CAACN,QAAQ,CAACI,aAAa,CAACC,MAAM,CAAC;IAC9C,CAAC,CACD,OAAOE,EAAE,EAAE;MACP;MACA,OAAOD,SAAS;IACpB;EACJ;AACJ;AACA;AACA;AACA;AACA,OAAO,MAAMd,cAAc,CAAC;EACxB;AACJ;AACA;EACIP,UAAUA,CAACuB,MAAM,EAAE;IACf,MAAMC,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACT,MAAM,CAAC;IAC1C,IAAI,CAACQ,QAAQ,EAAE;MACX,OAAO,KAAK;IAChB;IACA,MAAME,OAAO,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;MAAEC,SAAS,EAAEnC,WAAW;MAAEiB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IAAE,CAAC,EAAEU,MAAM,CAAC,EAAE;MAAEd,WAAW,EAAE,OAAOc,MAAM,CAACd,WAAW,KAAK,QAAQ,GAAGqB,IAAI,CAACC,SAAS,CAACR,MAAM,CAACd,WAAW,CAAC,GAAGc,MAAM,CAACd,WAAW;MAAEC,UAAU,EAAE,OAAOa,MAAM,CAACb,UAAU,KAAK,QAAQ,GAAGoB,IAAI,CAACC,SAAS,CAACR,MAAM,CAACb,UAAU,CAAC,GAAGa,MAAM,CAACb;IAAW,CAAC,CAAC;IACrU,IAAI,CAACf,cAAc,CAAC+B,OAAO,CAACjB,WAAW,EAAE,IAAI,CAAC,EAAE;MAC5C,IAAI,CAACuB,aAAa,CAAC,wCAAwCN,OAAO,CAACjB,WAAW,EAAE,CAAC;MACjF,OAAO,IAAI;IACf;IACA,IAAI,CAACd,cAAc,CAAC+B,OAAO,CAAChB,UAAU,EAAE,IAAI,CAAC,EAAE;MAC3C,IAAI,CAACsB,aAAa,CAAC,uCAAuCN,OAAO,CAAChB,UAAU,EAAE,CAAC;MAC/E,OAAO,IAAI;IACf;IACA,IAAI,CAACf,cAAc,CAAC+B,OAAO,CAAClB,YAAY,EAAE,IAAI,CAAC,EAAE;MAC7C,IAAI,CAACwB,aAAa,CAAC,yCAAyCN,OAAO,CAAClB,YAAY,EAAE,CAAC;MACnF,OAAO,IAAI;IACf;IACAgB,QAAQ,CAAC,kBAAkB,EAAEE,OAAO,CAAC;IACrC,OAAO,IAAI;EACf;EACAM,aAAaA,CAACC,OAAO,EAAE;IACnB7B,OAAO,CAACC,KAAK,CAAC4B,OAAO,CAAC;IACtB,MAAMT,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACT,MAAM,CAAC;IAC1C,IAAIQ,QAAQ,EAAE;MACVA,QAAQ,CAAC,kBAAkB,EAAE;QACzBK,SAAS,EAAEnC,WAAW;QACtBc,YAAY,EAAE,oBAAoB;QAClCC,WAAW,EAAEwB,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;QACnCvB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;MACxB,CAAC,CAAC;IACN;EACJ;EACAY,YAAYA,CAACN,aAAa,EAAE;IACxB,IAAI;MACA,IAAI,QAAQA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACK,QAAQ,CAAC,KAAK,UAAU,EAAE;QAC9G,OAAOL,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACK,QAAQ;MAC/F;MACA,IAAI,CAACL,aAAa,IAAIA,aAAa,CAACC,MAAM,KAAKD,aAAa,EAAE;QAC1D;QACA,OAAOE,SAAS;MACpB;MACA,OAAO,IAAI,CAACI,YAAY,CAACN,aAAa,CAACC,MAAM,CAAC;IAClD,CAAC,CACD,OAAOE,EAAE,EAAE;MACP;MACA,OAAOD,SAAS;IACpB;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}