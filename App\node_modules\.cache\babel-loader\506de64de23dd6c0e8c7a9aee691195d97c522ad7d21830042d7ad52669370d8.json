{"ast": null, "code": "var _jsxFileName = \"C:\\\\Repos2025\\\\Amazonians_App\\\\App\\\\src\\\\components\\\\ResourceFilterBar.jsx\";\nimport React from 'react';\nimport { Input, Select, FormField, ColumnLayout, Toggle, SpaceBetween } from \"@cloudscape-design/components\";\nimport { SORTABLE_FIELDS } from '../config/constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ResourceFilterBar({\n  filters,\n  onFilterChange,\n  sorting,\n  onSortChange,\n  showMetrics,\n  onShowMetricsChange,\n  filterOptions\n}) {\n  var _SORTABLE_FIELDS$find;\n  const {\n    searchQuery,\n    selectedType,\n    selectedRegion,\n    selectedStatus\n  } = filters;\n  const {\n    sortBy,\n    sortAscending\n  } = sorting;\n  return /*#__PURE__*/_jsxDEV(ColumnLayout, {\n    columns: 5,\n    direction: \"horizontal\",\n    children: [\" \", /*#__PURE__*/_jsxDEV(FormField, {\n      label: \"Search\",\n      width: \"20%\",\n      children: /*#__PURE__*/_jsxDEV(Input, {\n        value: searchQuery,\n        onChange: ({\n          detail\n        }) => onFilterChange('searchQuery', detail.value),\n        placeholder: \"Search resources...\",\n        ariaLabel: \"Search resources by name, description, or tags\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(FormField, {\n      label: \"Resource type\",\n      width: \"20%\",\n      children: /*#__PURE__*/_jsxDEV(Select, {\n        selectedOption: selectedType,\n        onChange: ({\n          detail\n        }) => onFilterChange('selectedType', detail.selectedOption),\n        options: filterOptions.types,\n        ariaLabel: \"Filter by resource type\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(FormField, {\n      label: \"Region\",\n      width: \"20%\",\n      children: /*#__PURE__*/_jsxDEV(Select, {\n        selectedOption: selectedRegion,\n        onChange: ({\n          detail\n        }) => onFilterChange('selectedRegion', detail.selectedOption),\n        options: filterOptions.regions,\n        ariaLabel: \"Filter by AWS region\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(FormField, {\n      label: \"Status\",\n      width: \"20%\",\n      children: /*#__PURE__*/_jsxDEV(Select, {\n        selectedOption: selectedStatus,\n        onChange: ({\n          detail\n        }) => onFilterChange('selectedStatus', detail.selectedOption),\n        options: filterOptions.statuses,\n        ariaLabel: \"Filter by resource status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(FormField, {\n      label: \"Sort by\",\n      width: \"20%\",\n      children: /*#__PURE__*/_jsxDEV(Select, {\n        selectedOption: {\n          value: sortBy.field,\n          label: (_SORTABLE_FIELDS$find = SORTABLE_FIELDS.find(f => f.value === sortBy.field)) === null || _SORTABLE_FIELDS$find === void 0 ? void 0 : _SORTABLE_FIELDS$find.label\n        },\n        onChange: ({\n          detail\n        }) => onSortChange({\n          field: detail.selectedOption.value\n        }),\n        options: SORTABLE_FIELDS,\n        ariaLabel: \"Select field to sort resources by\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(SpaceBetween, {\n      size: \"s\",\n      direction: \"vertical\",\n      children: [/*#__PURE__*/_jsxDEV(Toggle, {\n        onChange: ({\n          detail\n        }) => onSortChange({\n          ascending: detail.checked\n        }),\n        checked: sortBy.ascending,\n        ariaLabel: `Sort order: ${sortBy.ascending ? 'Ascending' : 'Descending'}`,\n        children: \"Sort ascending\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Toggle, {\n        onChange: ({\n          detail\n        }) => onShowMetricsChange(detail.checked),\n        checked: showMetrics,\n        ariaLabel: \"Toggle visibility of resource metrics charts\",\n        children: \"Show resource metrics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 9\n  }, this);\n}\n_c = ResourceFilterBar;\nexport default ResourceFilterBar;\nvar _c;\n$RefreshReg$(_c, \"ResourceFilterBar\");", "map": {"version": 3, "names": ["React", "Input", "Select", "FormField", "ColumnLayout", "Toggle", "SpaceBetween", "SORTABLE_FIELDS", "jsxDEV", "_jsxDEV", "ResourceFilterBar", "filters", "onFilterChange", "sorting", "onSortChange", "showMetrics", "onShowMetricsChange", "filterOptions", "_SORTABLE_FIELDS$find", "searchQuery", "selectedType", "selectedRegion", "selectedStatus", "sortBy", "sortAscending", "columns", "direction", "children", "label", "width", "value", "onChange", "detail", "placeholder", "aria<PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selectedOption", "options", "types", "regions", "statuses", "field", "find", "f", "size", "ascending", "checked", "_c", "$RefreshReg$"], "sources": ["C:/Repos2025/Amazonians_App/App/src/components/ResourceFilterBar.jsx"], "sourcesContent": ["import React from 'react';\r\nimport {\r\n    Input,\r\n    Select,\r\n    FormField,\r\n    ColumnLayout,\r\n    Toggle,\r\n    SpaceBetween\r\n} from \"@cloudscape-design/components\";\r\nimport { SORTABLE_FIELDS } from '../config/constants';\r\n\r\nfunction ResourceFilterBar({\r\n    filters,\r\n    onFilterChange,\r\n    sorting,\r\n    onSortChange,\r\n    showMetrics,\r\n    onShowMetricsChange,\r\n    filterOptions\r\n}) {\r\n    const { searchQuery, selectedType, selectedRegion, selectedStatus } = filters;\r\n    const { sortBy, sortAscending } = sorting;\r\n\r\n    return (\r\n        <ColumnLayout columns={5} direction=\"horizontal\"> {/* 5 columns */}\r\n            <FormField label=\"Search\" width=\"20%\">\r\n                <Input\r\n                    value={searchQuery}\r\n                    onChange={({ detail }) => onFilterChange('searchQuery', detail.value)}\r\n                    placeholder=\"Search resources...\"\r\n                    ariaLabel=\"Search resources by name, description, or tags\"\r\n                />\r\n            </FormField>\r\n            <FormField label=\"Resource type\" width=\"20%\">\r\n                <Select\r\n                    selectedOption={selectedType}\r\n                    onChange={({ detail }) => onFilterChange('selectedType', detail.selectedOption)}\r\n                    options={filterOptions.types}\r\n                    ariaLabel=\"Filter by resource type\"\r\n                />\r\n            </FormField>\r\n            <FormField label=\"Region\" width=\"20%\">\r\n                <Select\r\n                    selectedOption={selectedRegion}\r\n                    onChange={({ detail }) => onFilterChange('selectedRegion', detail.selectedOption)}\r\n                    options={filterOptions.regions}\r\n                    ariaLabel=\"Filter by AWS region\"\r\n                />\r\n            </FormField>\r\n            <FormField label=\"Status\" width=\"20%\">\r\n                <Select\r\n                    selectedOption={selectedStatus}\r\n                    onChange={({ detail }) => onFilterChange('selectedStatus', detail.selectedOption)}\r\n                    options={filterOptions.statuses}\r\n                    ariaLabel=\"Filter by resource status\"\r\n                />\r\n            </FormField>\r\n            <FormField label=\"Sort by\" width=\"20%\">\r\n                <Select\r\n                    selectedOption={{ value: sortBy.field, label: SORTABLE_FIELDS.find(f => f.value === sortBy.field)?.label }}\r\n                    onChange={({ detail }) => onSortChange({ field: detail.selectedOption.value })}\r\n                    options={SORTABLE_FIELDS}\r\n                    ariaLabel=\"Select field to sort resources by\"\r\n                />\r\n            </FormField>\r\n            <SpaceBetween size=\"s\" direction=\"vertical\">\r\n                <Toggle\r\n                    onChange={({ detail }) => onSortChange({ ascending: detail.checked })}\r\n                    checked={sortBy.ascending}\r\n                    ariaLabel={`Sort order: ${sortBy.ascending ? 'Ascending' : 'Descending'}`}\r\n                >\r\n                    Sort ascending\r\n                </Toggle>\r\n                <Toggle\r\n                    onChange={({ detail }) => onShowMetricsChange(detail.checked)}\r\n                    checked={showMetrics}\r\n                    ariaLabel=\"Toggle visibility of resource metrics charts\"\r\n                >\r\n                    Show resource metrics\r\n                </Toggle>\r\n            </SpaceBetween>\r\n        </ColumnLayout>\r\n    );\r\n}\r\n\r\nexport default ResourceFilterBar;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,YAAY,EACZC,MAAM,EACNC,YAAY,QACT,+BAA+B;AACtC,SAASC,eAAe,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,SAASC,iBAAiBA,CAAC;EACvBC,OAAO;EACPC,cAAc;EACdC,OAAO;EACPC,YAAY;EACZC,WAAW;EACXC,mBAAmB;EACnBC;AACJ,CAAC,EAAE;EAAA,IAAAC,qBAAA;EACC,MAAM;IAAEC,WAAW;IAAEC,YAAY;IAAEC,cAAc;IAAEC;EAAe,CAAC,GAAGX,OAAO;EAC7E,MAAM;IAAEY,MAAM;IAAEC;EAAc,CAAC,GAAGX,OAAO;EAEzC,oBACIJ,OAAA,CAACL,YAAY;IAACqB,OAAO,EAAE,CAAE;IAACC,SAAS,EAAC,YAAY;IAAAC,QAAA,GAAC,GAAC,eAC9ClB,OAAA,CAACN,SAAS;MAACyB,KAAK,EAAC,QAAQ;MAACC,KAAK,EAAC,KAAK;MAAAF,QAAA,eACjClB,OAAA,CAACR,KAAK;QACF6B,KAAK,EAAEX,WAAY;QACnBY,QAAQ,EAAEA,CAAC;UAAEC;QAAO,CAAC,KAAKpB,cAAc,CAAC,aAAa,EAAEoB,MAAM,CAACF,KAAK,CAAE;QACtEG,WAAW,EAAC,qBAAqB;QACjCC,SAAS,EAAC;MAAgD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACZ7B,OAAA,CAACN,SAAS;MAACyB,KAAK,EAAC,eAAe;MAACC,KAAK,EAAC,KAAK;MAAAF,QAAA,eACxClB,OAAA,CAACP,MAAM;QACHqC,cAAc,EAAEnB,YAAa;QAC7BW,QAAQ,EAAEA,CAAC;UAAEC;QAAO,CAAC,KAAKpB,cAAc,CAAC,cAAc,EAAEoB,MAAM,CAACO,cAAc,CAAE;QAChFC,OAAO,EAAEvB,aAAa,CAACwB,KAAM;QAC7BP,SAAS,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACZ7B,OAAA,CAACN,SAAS;MAACyB,KAAK,EAAC,QAAQ;MAACC,KAAK,EAAC,KAAK;MAAAF,QAAA,eACjClB,OAAA,CAACP,MAAM;QACHqC,cAAc,EAAElB,cAAe;QAC/BU,QAAQ,EAAEA,CAAC;UAAEC;QAAO,CAAC,KAAKpB,cAAc,CAAC,gBAAgB,EAAEoB,MAAM,CAACO,cAAc,CAAE;QAClFC,OAAO,EAAEvB,aAAa,CAACyB,OAAQ;QAC/BR,SAAS,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACZ7B,OAAA,CAACN,SAAS;MAACyB,KAAK,EAAC,QAAQ;MAACC,KAAK,EAAC,KAAK;MAAAF,QAAA,eACjClB,OAAA,CAACP,MAAM;QACHqC,cAAc,EAAEjB,cAAe;QAC/BS,QAAQ,EAAEA,CAAC;UAAEC;QAAO,CAAC,KAAKpB,cAAc,CAAC,gBAAgB,EAAEoB,MAAM,CAACO,cAAc,CAAE;QAClFC,OAAO,EAAEvB,aAAa,CAAC0B,QAAS;QAChCT,SAAS,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACZ7B,OAAA,CAACN,SAAS;MAACyB,KAAK,EAAC,SAAS;MAACC,KAAK,EAAC,KAAK;MAAAF,QAAA,eAClClB,OAAA,CAACP,MAAM;QACHqC,cAAc,EAAE;UAAET,KAAK,EAAEP,MAAM,CAACqB,KAAK;UAAEhB,KAAK,GAAAV,qBAAA,GAAEX,eAAe,CAACsC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChB,KAAK,KAAKP,MAAM,CAACqB,KAAK,CAAC,cAAA1B,qBAAA,uBAAnDA,qBAAA,CAAqDU;QAAM,CAAE;QAC3GG,QAAQ,EAAEA,CAAC;UAAEC;QAAO,CAAC,KAAKlB,YAAY,CAAC;UAAE8B,KAAK,EAAEZ,MAAM,CAACO,cAAc,CAACT;QAAM,CAAC,CAAE;QAC/EU,OAAO,EAAEjC,eAAgB;QACzB2B,SAAS,EAAC;MAAmC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eACZ7B,OAAA,CAACH,YAAY;MAACyC,IAAI,EAAC,GAAG;MAACrB,SAAS,EAAC,UAAU;MAAAC,QAAA,gBACvClB,OAAA,CAACJ,MAAM;QACH0B,QAAQ,EAAEA,CAAC;UAAEC;QAAO,CAAC,KAAKlB,YAAY,CAAC;UAAEkC,SAAS,EAAEhB,MAAM,CAACiB;QAAQ,CAAC,CAAE;QACtEA,OAAO,EAAE1B,MAAM,CAACyB,SAAU;QAC1Bd,SAAS,EAAE,eAAeX,MAAM,CAACyB,SAAS,GAAG,WAAW,GAAG,YAAY,EAAG;QAAArB,QAAA,EAC7E;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7B,OAAA,CAACJ,MAAM;QACH0B,QAAQ,EAAEA,CAAC;UAAEC;QAAO,CAAC,KAAKhB,mBAAmB,CAACgB,MAAM,CAACiB,OAAO,CAAE;QAC9DA,OAAO,EAAElC,WAAY;QACrBmB,SAAS,EAAC,8CAA8C;QAAAP,QAAA,EAC3D;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEvB;AAACY,EAAA,GAxEQxC,iBAAiB;AA0E1B,eAAeA,iBAAiB;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}