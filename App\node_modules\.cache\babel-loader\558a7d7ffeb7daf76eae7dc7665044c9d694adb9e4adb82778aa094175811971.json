{"ast": null, "code": "var _jsxFileName = \"C:\\\\Repos2025\\\\Amazonians_App\\\\App\\\\src\\\\components\\\\ResourceFilterBar.jsx\";\nimport React from 'react';\nimport { Input, Select, FormField, ColumnLayout, Toggle, SpaceBetween } from \"@cloudscape-design/components\";\nimport { SORTABLE_FIELDS } from '../config/constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ResourceFilterBar({\n  filters,\n  onFilterChange,\n  sorting,\n  onSortChange,\n  showMetrics,\n  onShowMetricsChange,\n  filterOptions\n}) {\n  var _SORTABLE_FIELDS$find;\n  const {\n    searchQuery,\n    selectedType,\n    selectedRegion,\n    selectedStatus\n  } = filters; // Removed dateRange\n  const {\n    sortBy,\n    sortAscending\n  } = sorting;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(ColumnLayout, {\n      columns: 4,\n      direction: \"horizontal\",\n      children: [/*#__PURE__*/_jsxDEV(FormField, {\n        label: \"Search\",\n        children: /*#__PURE__*/_jsxDEV(Input, {\n          value: searchQuery,\n          onChange: ({\n            detail\n          }) => onFilterChange('searchQuery', detail.value),\n          placeholder: \"Search resources...\",\n          ariaLabel: \"Search resources by name, description, or tags\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(FormField, {\n        label: \"Resource type\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          selectedOption: selectedType,\n          onChange: ({\n            detail\n          }) => onFilterChange('selectedType', detail.selectedOption),\n          options: filterOptions.types,\n          ariaLabel: \"Filter by resource type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(FormField, {\n        label: \"Region\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          selectedOption: selectedRegion,\n          onChange: ({\n            detail\n          }) => onFilterChange('selectedRegion', detail.selectedOption),\n          options: filterOptions.regions,\n          ariaLabel: \"Filter by AWS region\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(FormField, {\n        label: \"Status\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          selectedOption: selectedStatus,\n          onChange: ({\n            detail\n          }) => onFilterChange('selectedStatus', detail.selectedOption),\n          options: filterOptions.statuses,\n          ariaLabel: \"Filter by resource status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ColumnLayout, {\n      columns: 2,\n      direction: \"horizontal\",\n      children: [\" \", /*#__PURE__*/_jsxDEV(FormField, {\n        label: \"Sort by\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          selectedOption: {\n            value: sortBy.field,\n            label: (_SORTABLE_FIELDS$find = SORTABLE_FIELDS.find(f => f.value === sortBy.field)) === null || _SORTABLE_FIELDS$find === void 0 ? void 0 : _SORTABLE_FIELDS$find.label\n          },\n          onChange: ({\n            detail\n          }) => onSortChange({\n            field: detail.selectedOption.value\n          }),\n          options: SORTABLE_FIELDS,\n          ariaLabel: \"Select field to sort resources by\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(SpaceBetween, {\n        size: \"s\",\n        direction: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Toggle, {\n          onChange: ({\n            detail\n          }) => onSortChange({\n            ascending: detail.checked\n          }),\n          checked: sortBy.ascending,\n          ariaLabel: `Sort order: ${sortBy.ascending ? 'Ascending' : 'Descending'}`,\n          children: \"Sort ascending\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Toggle, {\n          onChange: ({\n            detail\n          }) => onShowMetricsChange(detail.checked),\n          checked: showMetrics,\n          ariaLabel: \"Toggle visibility of resource metrics charts\",\n          children: \"Show resource metrics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 9\n  }, this);\n}\n_c = ResourceFilterBar;\nexport default ResourceFilterBar;\nvar _c;\n$RefreshReg$(_c, \"ResourceFilterBar\");", "map": {"version": 3, "names": ["React", "Input", "Select", "FormField", "ColumnLayout", "Toggle", "SpaceBetween", "SORTABLE_FIELDS", "jsxDEV", "_jsxDEV", "ResourceFilterBar", "filters", "onFilterChange", "sorting", "onSortChange", "showMetrics", "onShowMetricsChange", "filterOptions", "_SORTABLE_FIELDS$find", "searchQuery", "selectedType", "selectedRegion", "selectedStatus", "sortBy", "sortAscending", "style", "display", "flexDirection", "children", "columns", "direction", "label", "value", "onChange", "detail", "placeholder", "aria<PERSON><PERSON><PERSON>", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "selectedOption", "options", "types", "regions", "statuses", "field", "find", "f", "size", "ascending", "checked", "_c", "$RefreshReg$"], "sources": ["C:/Repos2025/Amazonians_App/App/src/components/ResourceFilterBar.jsx"], "sourcesContent": ["import React from 'react';\r\nimport {\r\n    Input,\r\n    Select,\r\n    FormField,\r\n    ColumnLayout,\r\n    Toggle,\r\n    SpaceBetween\r\n} from \"@cloudscape-design/components\";\r\nimport { SORTABLE_FIELDS } from '../config/constants';\r\n\r\nfunction ResourceFilterBar({\r\n    filters,\r\n    onFilterChange,\r\n    sorting,\r\n    onSortChange,\r\n    showMetrics,\r\n    onShowMetricsChange,\r\n    filterOptions\r\n}) {\r\n    const { searchQuery, selectedType, selectedRegion, selectedStatus } = filters; // Removed dateRange\r\n    const { sortBy, sortAscending } = sorting;\r\n\r\n    return (\r\n        <div style={{ display: 'flex', flexDirection: 'column' }}>\r\n            <ColumnLayout columns={4} direction=\"horizontal\">\r\n                <FormField label=\"Search\">\r\n                    <Input\r\n                        value={searchQuery}\r\n                        onChange={({ detail }) => onFilterChange('searchQuery', detail.value)}\r\n                        placeholder=\"Search resources...\"\r\n                        ariaLabel=\"Search resources by name, description, or tags\"\r\n                    />\r\n                </FormField>\r\n                <FormField label=\"Resource type\">\r\n                    <Select\r\n                        selectedOption={selectedType}\r\n                        onChange={({ detail }) => onFilterChange('selectedType', detail.selectedOption)}\r\n                        options={filterOptions.types}\r\n                        ariaLabel=\"Filter by resource type\"\r\n                    />\r\n                </FormField>\r\n                <FormField label=\"Region\">\r\n                    <Select\r\n                        selectedOption={selectedRegion}\r\n                        onChange={({ detail }) => onFilterChange('selectedRegion', detail.selectedOption)}\r\n                        options={filterOptions.regions}\r\n                        ariaLabel=\"Filter by AWS region\"\r\n                    />\r\n                </FormField>\r\n                <FormField label=\"Status\">\r\n                    <Select\r\n                        selectedOption={selectedStatus}\r\n                        onChange={({ detail }) => onFilterChange('selectedStatus', detail.selectedOption)}\r\n                        options={filterOptions.statuses}\r\n                        ariaLabel=\"Filter by resource status\"\r\n                    />\r\n                </FormField>\r\n            </ColumnLayout>\r\n            <ColumnLayout columns={2} direction=\"horizontal\"> {/* Changed to 2 columns */}\r\n                <FormField label=\"Sort by\">\r\n                    <Select\r\n                        selectedOption={{ value: sortBy.field, label: SORTABLE_FIELDS.find(f => f.value === sortBy.field)?.label }}\r\n                        onChange={({ detail }) => onSortChange({ field: detail.selectedOption.value })}\r\n                        options={SORTABLE_FIELDS}\r\n                        ariaLabel=\"Select field to sort resources by\"\r\n                    />\r\n                </FormField>\r\n                <SpaceBetween size=\"s\" direction=\"vertical\">\r\n                    <Toggle\r\n                        onChange={({ detail }) => onSortChange({ ascending: detail.checked })}\r\n                        checked={sortBy.ascending}\r\n                        ariaLabel={`Sort order: ${sortBy.ascending ? 'Ascending' : 'Descending'}`}\r\n                    >\r\n                        Sort ascending\r\n                    </Toggle>\r\n                    <Toggle\r\n                        onChange={({ detail }) => onShowMetricsChange(detail.checked)}\r\n                        checked={showMetrics}\r\n                        ariaLabel=\"Toggle visibility of resource metrics charts\"\r\n                    >\r\n                        Show resource metrics\r\n                    </Toggle>\r\n                </SpaceBetween>\r\n            </ColumnLayout>\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default ResourceFilterBar;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACIC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,YAAY,EACZC,MAAM,EACNC,YAAY,QACT,+BAA+B;AACtC,SAASC,eAAe,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,SAASC,iBAAiBA,CAAC;EACvBC,OAAO;EACPC,cAAc;EACdC,OAAO;EACPC,YAAY;EACZC,WAAW;EACXC,mBAAmB;EACnBC;AACJ,CAAC,EAAE;EAAA,IAAAC,qBAAA;EACC,MAAM;IAAEC,WAAW;IAAEC,YAAY;IAAEC,cAAc;IAAEC;EAAe,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC/E,MAAM;IAAEY,MAAM;IAAEC;EAAc,CAAC,GAAGX,OAAO;EAEzC,oBACIJ,OAAA;IAAKgB,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACrDnB,OAAA,CAACL,YAAY;MAACyB,OAAO,EAAE,CAAE;MAACC,SAAS,EAAC,YAAY;MAAAF,QAAA,gBAC5CnB,OAAA,CAACN,SAAS;QAAC4B,KAAK,EAAC,QAAQ;QAAAH,QAAA,eACrBnB,OAAA,CAACR,KAAK;UACF+B,KAAK,EAAEb,WAAY;UACnBc,QAAQ,EAAEA,CAAC;YAAEC;UAAO,CAAC,KAAKtB,cAAc,CAAC,aAAa,EAAEsB,MAAM,CAACF,KAAK,CAAE;UACtEG,WAAW,EAAC,qBAAqB;UACjCC,SAAS,EAAC;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACZ/B,OAAA,CAACN,SAAS;QAAC4B,KAAK,EAAC,eAAe;QAAAH,QAAA,eAC5BnB,OAAA,CAACP,MAAM;UACHuC,cAAc,EAAErB,YAAa;UAC7Ba,QAAQ,EAAEA,CAAC;YAAEC;UAAO,CAAC,KAAKtB,cAAc,CAAC,cAAc,EAAEsB,MAAM,CAACO,cAAc,CAAE;UAChFC,OAAO,EAAEzB,aAAa,CAAC0B,KAAM;UAC7BP,SAAS,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACZ/B,OAAA,CAACN,SAAS;QAAC4B,KAAK,EAAC,QAAQ;QAAAH,QAAA,eACrBnB,OAAA,CAACP,MAAM;UACHuC,cAAc,EAAEpB,cAAe;UAC/BY,QAAQ,EAAEA,CAAC;YAAEC;UAAO,CAAC,KAAKtB,cAAc,CAAC,gBAAgB,EAAEsB,MAAM,CAACO,cAAc,CAAE;UAClFC,OAAO,EAAEzB,aAAa,CAAC2B,OAAQ;UAC/BR,SAAS,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACZ/B,OAAA,CAACN,SAAS;QAAC4B,KAAK,EAAC,QAAQ;QAAAH,QAAA,eACrBnB,OAAA,CAACP,MAAM;UACHuC,cAAc,EAAEnB,cAAe;UAC/BW,QAAQ,EAAEA,CAAC;YAAEC;UAAO,CAAC,KAAKtB,cAAc,CAAC,gBAAgB,EAAEsB,MAAM,CAACO,cAAc,CAAE;UAClFC,OAAO,EAAEzB,aAAa,CAAC4B,QAAS;UAChCT,SAAS,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACf/B,OAAA,CAACL,YAAY;MAACyB,OAAO,EAAE,CAAE;MAACC,SAAS,EAAC,YAAY;MAAAF,QAAA,GAAC,GAAC,eAC9CnB,OAAA,CAACN,SAAS;QAAC4B,KAAK,EAAC,SAAS;QAAAH,QAAA,eACtBnB,OAAA,CAACP,MAAM;UACHuC,cAAc,EAAE;YAAET,KAAK,EAAET,MAAM,CAACuB,KAAK;YAAEf,KAAK,GAAAb,qBAAA,GAAEX,eAAe,CAACwC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChB,KAAK,KAAKT,MAAM,CAACuB,KAAK,CAAC,cAAA5B,qBAAA,uBAAnDA,qBAAA,CAAqDa;UAAM,CAAE;UAC3GE,QAAQ,EAAEA,CAAC;YAAEC;UAAO,CAAC,KAAKpB,YAAY,CAAC;YAAEgC,KAAK,EAAEZ,MAAM,CAACO,cAAc,CAACT;UAAM,CAAC,CAAE;UAC/EU,OAAO,EAAEnC,eAAgB;UACzB6B,SAAS,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACZ/B,OAAA,CAACH,YAAY;QAAC2C,IAAI,EAAC,GAAG;QAACnB,SAAS,EAAC,UAAU;QAAAF,QAAA,gBACvCnB,OAAA,CAACJ,MAAM;UACH4B,QAAQ,EAAEA,CAAC;YAAEC;UAAO,CAAC,KAAKpB,YAAY,CAAC;YAAEoC,SAAS,EAAEhB,MAAM,CAACiB;UAAQ,CAAC,CAAE;UACtEA,OAAO,EAAE5B,MAAM,CAAC2B,SAAU;UAC1Bd,SAAS,EAAE,eAAeb,MAAM,CAAC2B,SAAS,GAAG,WAAW,GAAG,YAAY,EAAG;UAAAtB,QAAA,EAC7E;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT/B,OAAA,CAACJ,MAAM;UACH4B,QAAQ,EAAEA,CAAC;YAAEC;UAAO,CAAC,KAAKlB,mBAAmB,CAACkB,MAAM,CAACiB,OAAO,CAAE;UAC9DA,OAAO,EAAEpC,WAAY;UACrBqB,SAAS,EAAC,8CAA8C;UAAAR,QAAA,EAC3D;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEd;AAACY,EAAA,GA5EQ1C,iBAAiB;AA8E1B,eAAeA,iBAAiB;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}