{"ast": null, "code": "import { __rest } from \"tslib\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useEffect } from 'react';\nimport { FunnelMetrics } from '../internal/analytics';\nimport { AnalyticsFunnel, AnalyticsFunnelStep } from '../internal/analytics/components/analytics-funnel';\nimport { useFunnel, useFunnelNameSelector, useFunnelStep } from '../internal/analytics/hooks/use-funnel';\nimport { getAnalyticsMetadataProps } from '../internal/base-component';\nimport { ButtonContext } from '../internal/context/button-context';\nimport useBaseComponent from '../internal/hooks/use-base-component';\nimport { applyDisplayName } from '../internal/utils/apply-display-name';\nimport InternalForm from './internal';\nimport headerStyles from '../header/styles.css.js';\nimport analyticsSelectors from './analytics-metadata/styles.css.js';\nconst FormWithAnalytics = _a => {\n  var {\n      variant = 'full-page',\n      actions,\n      errorText\n    } = _a,\n    props = __rest(_a, [\"variant\", \"actions\", \"errorText\"]);\n  const {\n    funnelIdentifier,\n    funnelInteractionId,\n    funnelProps,\n    funnelSubmit,\n    funnelNextOrSubmitAttempt,\n    errorCount,\n    submissionAttempt,\n    funnelErrorContext\n  } = useFunnel();\n  const {\n    funnelStepProps\n  } = useFunnelStep();\n  const handleActionButtonClick = ({\n    variant\n  }) => {\n    if (variant === 'primary') {\n      funnelNextOrSubmitAttempt();\n      funnelSubmit();\n    }\n  };\n  useEffect(() => {\n    if (funnelInteractionId && errorText) {\n      errorCount.current++;\n      FunnelMetrics.funnelError({\n        funnelInteractionId,\n        funnelIdentifier,\n        funnelErrorContext\n      });\n      return () => {\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        errorCount.current--;\n      };\n    }\n  }, [funnelInteractionId, funnelIdentifier, errorText, submissionAttempt, errorCount, funnelErrorContext]);\n  return React.createElement(ButtonContext.Provider, {\n    value: {\n      onClick: handleActionButtonClick\n    }\n  }, React.createElement(InternalForm, Object.assign({\n    variant: variant,\n    actions: actions,\n    errorText: errorText\n  }, props, funnelProps, funnelStepProps, {\n    __injectAnalyticsComponentMetadata: true\n  })));\n};\nexport default function Form(_a) {\n  var {\n      variant = 'full-page'\n    } = _a,\n    props = __rest(_a, [\"variant\"]);\n  const analyticsMetadata = getAnalyticsMetadataProps(props);\n  const baseComponentProps = useBaseComponent('Form', {\n    props: {\n      variant,\n      flowType: analyticsMetadata === null || analyticsMetadata === void 0 ? void 0 : analyticsMetadata.flowType\n    },\n    metadata: {\n      hasResourceType: Boolean(analyticsMetadata === null || analyticsMetadata === void 0 ? void 0 : analyticsMetadata.resourceType),\n      hasInstanceIdentifier: Boolean(analyticsMetadata === null || analyticsMetadata === void 0 ? void 0 : analyticsMetadata.instanceIdentifier)\n    }\n  }, analyticsMetadata);\n  const inheritedFunnelNameSelector = useFunnelNameSelector();\n  const funnelNameSelector = inheritedFunnelNameSelector || `.${analyticsSelectors.header} .${headerStyles['heading-text']}`;\n  return React.createElement(AnalyticsFunnel, {\n    funnelIdentifier: analyticsMetadata === null || analyticsMetadata === void 0 ? void 0 : analyticsMetadata.instanceIdentifier,\n    funnelFlowType: analyticsMetadata === null || analyticsMetadata === void 0 ? void 0 : analyticsMetadata.flowType,\n    funnelErrorContext: analyticsMetadata === null || analyticsMetadata === void 0 ? void 0 : analyticsMetadata.errorContext,\n    funnelResourceType: analyticsMetadata === null || analyticsMetadata === void 0 ? void 0 : analyticsMetadata.resourceType,\n    funnelType: \"single-page\",\n    optionalStepNumbers: [],\n    totalFunnelSteps: 1,\n    funnelNameSelectors: () => [funnelNameSelector, `.${analyticsSelectors.header}`]\n  }, React.createElement(AnalyticsFunnelStep, {\n    stepIdentifier: analyticsMetadata === null || analyticsMetadata === void 0 ? void 0 : analyticsMetadata.instanceIdentifier,\n    stepErrorContext: analyticsMetadata === null || analyticsMetadata === void 0 ? void 0 : analyticsMetadata.errorContext,\n    stepNumber: 1\n  }, React.createElement(FormWithAnalytics, Object.assign({\n    variant: variant\n  }, props, baseComponentProps))));\n}\napplyDisplayName(Form, 'Form');", "map": {"version": 3, "names": ["React", "useEffect", "FunnelMetrics", "AnalyticsFunnel", "AnalyticsFunnelStep", "useFunnel", "useFunnelNameSelector", "useFunnelStep", "getAnalyticsMetadataProps", "ButtonContext", "useBaseComponent", "applyDisplayName", "InternalForm", "headerStyles", "analyticsSelectors", "FormWithAnalytics", "_a", "variant", "actions", "errorText", "props", "__rest", "funnelIdentifier", "funnelInteractionId", "funnelProps", "funnelSubmit", "funnelNextOrSubmitAttempt", "errorCount", "submissionAttempt", "funnelErrorContext", "funnelStepProps", "handleActionButtonClick", "current", "funnelError", "createElement", "Provider", "value", "onClick", "Object", "assign", "__injectAnalyticsComponentMetadata", "Form", "analyticsMetadata", "baseComponentProps", "flowType", "metadata", "hasResourceType", "Boolean", "resourceType", "hasInstanceIdentifier", "instanceIdentifier", "inheritedFunnelNameSelector", "funnelNameSelector", "header", "funnelFlowType", "errorContext", "funnelResourceType", "funnelType", "optionalStepNumbers", "totalFunnelSteps", "funnelNameSelectors", "stepIdentifier", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Repos2025\\Amazonians_App\\App\\node_modules\\src\\form\\index.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useEffect } from 'react';\n\nimport { FunnelMetrics } from '../internal/analytics';\nimport { AnalyticsFunnel, AnalyticsFunnelStep } from '../internal/analytics/components/analytics-funnel';\nimport { useFunnel, useFunnelNameSelector, useFunnelStep } from '../internal/analytics/hooks/use-funnel';\nimport { BasePropsWithAnalyticsMetadata, getAnalyticsMetadataProps } from '../internal/base-component';\nimport { ButtonContext, ButtonContextProps } from '../internal/context/button-context';\nimport useBaseComponent from '../internal/hooks/use-base-component';\nimport { applyDisplayName } from '../internal/utils/apply-display-name';\nimport { FormProps } from './interfaces';\nimport InternalForm from './internal';\n\nimport headerStyles from '../header/styles.css.js';\nimport analyticsSelectors from './analytics-metadata/styles.css.js';\n\nexport { FormProps };\n\nconst FormWithAnalytics = ({ variant = 'full-page', actions, errorText, ...props }: FormProps) => {\n  const {\n    funnelIdentifier,\n    funnelInteractionId,\n    funnelProps,\n    funnelSubmit,\n    funnelNextOrSubmitAttempt,\n    errorCount,\n    submissionAttempt,\n    funnelErrorContext,\n  } = useFunnel();\n  const { funnelStepProps } = useFunnelStep();\n\n  const handleActionButtonClick: ButtonContextProps['onClick'] = ({ variant }) => {\n    if (variant === 'primary') {\n      funnelNextOrSubmitAttempt();\n      funnelSubmit();\n    }\n  };\n\n  useEffect(() => {\n    if (funnelInteractionId && errorText) {\n      errorCount.current++;\n      FunnelMetrics.funnelError({ funnelInteractionId, funnelIdentifier, funnelErrorContext });\n      return () => {\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        errorCount.current--;\n      };\n    }\n  }, [funnelInteractionId, funnelIdentifier, errorText, submissionAttempt, errorCount, funnelErrorContext]);\n\n  return (\n    <ButtonContext.Provider value={{ onClick: handleActionButtonClick }}>\n      <InternalForm\n        variant={variant}\n        actions={actions}\n        errorText={errorText}\n        {...props}\n        {...funnelProps}\n        {...funnelStepProps}\n        __injectAnalyticsComponentMetadata={true}\n      />\n    </ButtonContext.Provider>\n  );\n};\n\nexport default function Form({ variant = 'full-page', ...props }: FormProps) {\n  const analyticsMetadata = getAnalyticsMetadataProps(props as BasePropsWithAnalyticsMetadata);\n  const baseComponentProps = useBaseComponent(\n    'Form',\n    {\n      props: {\n        variant,\n        flowType: analyticsMetadata?.flowType,\n      },\n      metadata: {\n        hasResourceType: Boolean(analyticsMetadata?.resourceType),\n        hasInstanceIdentifier: Boolean(analyticsMetadata?.instanceIdentifier),\n      },\n    },\n    analyticsMetadata\n  );\n  const inheritedFunnelNameSelector = useFunnelNameSelector();\n  const funnelNameSelector =\n    inheritedFunnelNameSelector || `.${analyticsSelectors.header} .${headerStyles['heading-text']}`;\n\n  return (\n    <AnalyticsFunnel\n      funnelIdentifier={analyticsMetadata?.instanceIdentifier}\n      funnelFlowType={analyticsMetadata?.flowType}\n      funnelErrorContext={analyticsMetadata?.errorContext}\n      funnelResourceType={analyticsMetadata?.resourceType}\n      funnelType=\"single-page\"\n      optionalStepNumbers={[]}\n      totalFunnelSteps={1}\n      funnelNameSelectors={() => [funnelNameSelector, `.${analyticsSelectors.header}`]}\n    >\n      <AnalyticsFunnelStep\n        stepIdentifier={analyticsMetadata?.instanceIdentifier}\n        stepErrorContext={analyticsMetadata?.errorContext}\n        stepNumber={1}\n      >\n        <FormWithAnalytics variant={variant} {...props} {...baseComponentProps} />\n      </AnalyticsFunnelStep>\n    </AnalyticsFunnel>\n  );\n}\n\napplyDisplayName(Form, 'Form');\n"], "mappings": ";AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AAExC,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,EAAEC,mBAAmB,QAAQ,mDAAmD;AACxG,SAASC,SAAS,EAAEC,qBAAqB,EAAEC,aAAa,QAAQ,wCAAwC;AACxG,SAAyCC,yBAAyB,QAAQ,4BAA4B;AACtG,SAASC,aAAa,QAA4B,oCAAoC;AACtF,OAAOC,gBAAgB,MAAM,sCAAsC;AACnE,SAASC,gBAAgB,QAAQ,sCAAsC;AAEvE,OAAOC,YAAY,MAAM,YAAY;AAErC,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,kBAAkB,MAAM,oCAAoC;AAInE,MAAMC,iBAAiB,GAAIC,EAAkE,IAAI;MAAtE;MAAEC,OAAO,GAAG,WAAW;MAAEC,OAAO;MAAEC;IAAS,IAAAH,EAAuB;IAAlBI,KAAK,GAAAC,MAAA,CAAAL,EAAA,EAArD,mCAAuD,CAAF;EAC9E,MAAM;IACJM,gBAAgB;IAChBC,mBAAmB;IACnBC,WAAW;IACXC,YAAY;IACZC,yBAAyB;IACzBC,UAAU;IACVC,iBAAiB;IACjBC;EAAkB,CACnB,GAAGxB,SAAS,EAAE;EACf,MAAM;IAAEyB;EAAe,CAAE,GAAGvB,aAAa,EAAE;EAE3C,MAAMwB,uBAAuB,GAAkCA,CAAC;IAAEd;EAAO,CAAE,KAAI;IAC7E,IAAIA,OAAO,KAAK,SAAS,EAAE;MACzBS,yBAAyB,EAAE;MAC3BD,YAAY,EAAE;;EAElB,CAAC;EAEDxB,SAAS,CAAC,MAAK;IACb,IAAIsB,mBAAmB,IAAIJ,SAAS,EAAE;MACpCQ,UAAU,CAACK,OAAO,EAAE;MACpB9B,aAAa,CAAC+B,WAAW,CAAC;QAAEV,mBAAmB;QAAED,gBAAgB;QAAEO;MAAkB,CAAE,CAAC;MACxF,OAAO,MAAK;QACV;QACAF,UAAU,CAACK,OAAO,EAAE;MACtB,CAAC;;EAEL,CAAC,EAAE,CAACT,mBAAmB,EAAED,gBAAgB,EAAEH,SAAS,EAAES,iBAAiB,EAAED,UAAU,EAAEE,kBAAkB,CAAC,CAAC;EAEzG,OACE7B,KAAA,CAAAkC,aAAA,CAACzB,aAAa,CAAC0B,QAAQ;IAACC,KAAK,EAAE;MAAEC,OAAO,EAAEN;IAAuB;EAAE,GACjE/B,KAAA,CAAAkC,aAAA,CAACtB,YAAY,EAAA0B,MAAA,CAAAC,MAAA;IACXtB,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA;EAAS,GAChBC,KAAK,EACLI,WAAW,EACXM,eAAe;IACnBU,kCAAkC,EAAE;EAAI,GACxC,CACqB;AAE7B,CAAC;AAED,eAAc,SAAUC,IAAIA,CAACzB,EAA8C;MAA9C;MAAEC,OAAO,GAAG;IAAW,IAAAD,EAAuB;IAAlBI,KAAK,GAAAC,MAAA,CAAAL,EAAA,EAAjC,WAAmC,CAAF;EAC5D,MAAM0B,iBAAiB,GAAGlC,yBAAyB,CAACY,KAAuC,CAAC;EAC5F,MAAMuB,kBAAkB,GAAGjC,gBAAgB,CACzC,MAAM,EACN;IACEU,KAAK,EAAE;MACLH,OAAO;MACP2B,QAAQ,EAAEF,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEE;KAC9B;IACDC,QAAQ,EAAE;MACRC,eAAe,EAAEC,OAAO,CAACL,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEM,YAAY,CAAC;MACzDC,qBAAqB,EAAEF,OAAO,CAACL,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEQ,kBAAkB;;GAEvE,EACDR,iBAAiB,CAClB;EACD,MAAMS,2BAA2B,GAAG7C,qBAAqB,EAAE;EAC3D,MAAM8C,kBAAkB,GACtBD,2BAA2B,IAAI,IAAIrC,kBAAkB,CAACuC,MAAM,KAAKxC,YAAY,CAAC,cAAc,CAAC,EAAE;EAEjG,OACEb,KAAA,CAAAkC,aAAA,CAAC/B,eAAe;IACdmB,gBAAgB,EAAEoB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEQ,kBAAkB;IACvDI,cAAc,EAAEZ,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEE,QAAQ;IAC3Cf,kBAAkB,EAAEa,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEa,YAAY;IACnDC,kBAAkB,EAAEd,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEM,YAAY;IACnDS,UAAU,EAAC,aAAa;IACxBC,mBAAmB,EAAE,EAAE;IACvBC,gBAAgB,EAAE,CAAC;IACnBC,mBAAmB,EAAEA,CAAA,KAAM,CAACR,kBAAkB,EAAE,IAAItC,kBAAkB,CAACuC,MAAM,EAAE;EAAC,GAEhFrD,KAAA,CAAAkC,aAAA,CAAC9B,mBAAmB;IAClByD,cAAc,EAAEnB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEQ,kBAAkB;IACrDY,gBAAgB,EAAEpB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEa,YAAY;IACjDQ,UAAU,EAAE;EAAC,GAEb/D,KAAA,CAAAkC,aAAA,CAACnB,iBAAiB,EAAAuB,MAAA,CAAAC,MAAA;IAACtB,OAAO,EAAEA;EAAO,GAAMG,KAAK,EAAMuB,kBAAkB,EAAI,CACtD,CACN;AAEtB;AAEAhC,gBAAgB,CAAC8B,IAAI,EAAE,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}