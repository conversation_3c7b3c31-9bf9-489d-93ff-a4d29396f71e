{"ast": null, "code": "import { __rest } from \"tslib\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useLayoutEffect, useRef } from 'react';\nimport clsx from 'clsx';\nimport { getAnalyticsMetadataAttribute } from '@cloudscape-design/component-toolkit/internal/analytics-metadata';\nimport { getBaseProps } from '../../base-component';\nimport { getAnalyticsSelectActionMetadata } from './analytics-metadata/utils';\nimport analyticsSelectors from './analytics-metadata/styles.css.js';\nimport styles from './styles.css.js';\nconst SelectableItem = (_a, ref) => {\n  var {\n      children: content,\n      ariaSelected,\n      ariaChecked,\n      selected,\n      highlighted,\n      disabled,\n      hasBackground,\n      isParent,\n      isChild,\n      virtualPosition,\n      padBottom,\n      isNextSelected,\n      useInteractiveGroups,\n      screenReaderContent,\n      ariaPosinset,\n      ariaSetsize,\n      highlightType,\n      value\n    } = _a,\n    restProps = __rest(_a, [\"children\", \"ariaSelected\", \"ariaChecked\", \"selected\", \"highlighted\", \"disabled\", \"hasBackground\", \"isParent\", \"isChild\", \"virtualPosition\", \"padBottom\", \"isNextSelected\", \"useInteractiveGroups\", \"screenReaderContent\", \"ariaPosinset\", \"ariaSetsize\", \"highlightType\", \"value\"]);\n  const _b = getBaseProps(restProps),\n    {\n      className\n    } = _b,\n    rest = __rest(_b, [\"className\"]);\n  const classNames = clsx(className, styles['selectable-item'], {\n    [styles.selected]: selected,\n    [styles.highlighted]: highlighted,\n    [styles['has-background']]: hasBackground,\n    [styles.parent]: isParent,\n    [analyticsSelectors.parent]: isParent,\n    [styles.child]: isChild,\n    [styles['is-keyboard']]: highlightType === 'keyboard',\n    [styles.disabled]: disabled,\n    [styles.virtual]: virtualPosition !== undefined,\n    [styles['pad-bottom']]: padBottom,\n    [styles['next-item-selected']]: isNextSelected,\n    [styles.interactiveGroups]: useInteractiveGroups\n  });\n  const contentRef = useRef(null);\n  const screenReaderContentRef = useRef(null);\n  useLayoutEffect(() => {\n    // the state of aria-hidden and announcement is not set back because NVDA+Firefox would announce\n    // the item which lost highlight\n    // set aria-hidden true when there is announcement content, so that screen reader still announce\n    // meaningful content when navigate with screen reader cursor\n    // imperatively update to avoid announcement made multiple times when content updates\n    if (highlighted && screenReaderContent) {\n      if (contentRef.current) {\n        contentRef.current.setAttribute('aria-hidden', 'true');\n      }\n      if (screenReaderContentRef.current) {\n        screenReaderContentRef.current.textContent = screenReaderContent;\n      }\n    }\n  }, [highlighted, screenReaderContent, contentRef, screenReaderContentRef]);\n  const style = virtualPosition !== undefined ? {\n    transform: `translateY(${virtualPosition}px)`\n  } : undefined;\n  const a11yProperties = {\n    'aria-disabled': disabled\n  };\n  if (isParent && !useInteractiveGroups) {\n    a11yProperties['aria-hidden'] = true;\n  }\n  if (ariaSelected !== undefined) {\n    a11yProperties['aria-selected'] = ariaSelected;\n  }\n  // Safari+VO needs aria-checked for multi-selection. Otherwise it only announces selected option even though another option is highlighted.\n  if (ariaChecked !== undefined) {\n    a11yProperties['aria-checked'] = ariaChecked;\n  }\n  if (ariaPosinset && ariaSetsize) {\n    a11yProperties['aria-posinset'] = ariaPosinset;\n    a11yProperties['aria-setsize'] = ariaSetsize;\n  }\n  if (restProps.ariaDescribedby) {\n    a11yProperties['aria-describedby'] = restProps.ariaDescribedby;\n  }\n  return React.createElement(\"li\", Object.assign({\n    role: \"option\",\n    className: classNames,\n    style: style\n  }, a11yProperties, rest, isParent || disabled ? {} : getAnalyticsMetadataAttribute(getAnalyticsSelectActionMetadata(Object.assign({\n    isChild,\n    value\n  }, restProps)))), React.createElement(\"div\", {\n    className: clsx(styles['option-content'], analyticsSelectors['option-content']),\n    ref: contentRef\n  }, content), React.createElement(\"div\", {\n    className: styles['measure-strut'],\n    ref: ref\n  }), React.createElement(\"div\", {\n    className: styles['screenreader-content'],\n    ref: screenReaderContentRef\n  }));\n};\nexport default React.forwardRef(SelectableItem);", "map": {"version": 3, "names": ["React", "useLayoutEffect", "useRef", "clsx", "getAnalyticsMetadataAttribute", "getBaseProps", "getAnalyticsSelectActionMetadata", "analyticsSelectors", "styles", "SelectableItem", "_a", "ref", "children", "content", "ariaSelected", "ariaChe<PERSON>", "selected", "highlighted", "disabled", "hasBackground", "isParent", "<PERSON><PERSON><PERSON><PERSON>", "virtualPosition", "padBottom", "isNextSelected", "useInteractiveGroups", "screenReader<PERSON><PERSON>nt", "ariaPosinset", "ariaSetsize", "highlightType", "value", "restProps", "__rest", "_b", "className", "rest", "classNames", "parent", "child", "virtual", "undefined", "interactiveGroups", "contentRef", "screenReaderContentRef", "current", "setAttribute", "textContent", "style", "transform", "a11yProperties", "aria<PERSON><PERSON><PERSON><PERSON>", "createElement", "Object", "assign", "role", "forwardRef"], "sources": ["C:\\Repos2025\\App-main\\App\\node_modules\\src\\internal\\components\\selectable-item\\index.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useLayoutEffect, useRef } from 'react';\nimport clsx from 'clsx';\n\nimport { getAnalyticsMetadataAttribute } from '@cloudscape-design/component-toolkit/internal/analytics-metadata';\n\nimport { getBaseProps } from '../../base-component';\nimport { getAnalyticsSelectActionMetadata } from './analytics-metadata/utils';\nimport { SelectableItemProps } from './interfaces';\n\nimport analyticsSelectors from './analytics-metadata/styles.css.js';\nimport styles from './styles.css.js';\n\nexport { SelectableItemProps };\n\nconst SelectableItem = (\n  {\n    children: content,\n    ariaSelected,\n    ariaChecked,\n    selected,\n    highlighted,\n    disabled,\n    hasBackground,\n    isParent,\n    isChild,\n    virtualPosition,\n    padBottom,\n    isNextSelected,\n    useInteractiveGroups,\n    screenReaderContent,\n    ariaPosinset,\n    ariaSetsize,\n    highlightType,\n    value,\n    ...restProps\n  }: SelectableItemProps,\n  ref: React.Ref<HTMLDivElement>\n) => {\n  const { className, ...rest } = getBaseProps(restProps);\n  const classNames = clsx(className, styles['selectable-item'], {\n    [styles.selected]: selected,\n    [styles.highlighted]: highlighted,\n    [styles['has-background']]: hasBackground,\n    [styles.parent]: isParent,\n    [analyticsSelectors.parent]: isParent,\n    [styles.child]: isChild,\n    [styles['is-keyboard']]: highlightType === 'keyboard',\n    [styles.disabled]: disabled,\n    [styles.virtual]: virtualPosition !== undefined,\n    [styles['pad-bottom']]: padBottom,\n    [styles['next-item-selected']]: isNextSelected,\n    [styles.interactiveGroups]: useInteractiveGroups,\n  });\n\n  const contentRef = useRef<HTMLDivElement>(null);\n  const screenReaderContentRef = useRef<HTMLDivElement>(null);\n\n  useLayoutEffect(() => {\n    // the state of aria-hidden and announcement is not set back because NVDA+Firefox would announce\n    // the item which lost highlight\n    // set aria-hidden true when there is announcement content, so that screen reader still announce\n    // meaningful content when navigate with screen reader cursor\n    // imperatively update to avoid announcement made multiple times when content updates\n    if (highlighted && screenReaderContent) {\n      if (contentRef.current) {\n        contentRef.current.setAttribute('aria-hidden', 'true');\n      }\n      if (screenReaderContentRef.current) {\n        screenReaderContentRef.current.textContent = screenReaderContent;\n      }\n    }\n  }, [highlighted, screenReaderContent, contentRef, screenReaderContentRef]);\n\n  const style =\n    virtualPosition !== undefined\n      ? {\n          transform: `translateY(${virtualPosition}px)`,\n        }\n      : undefined;\n\n  const a11yProperties: Record<string, string | number | boolean | undefined> = {\n    'aria-disabled': disabled,\n  };\n\n  if (isParent && !useInteractiveGroups) {\n    a11yProperties['aria-hidden'] = true;\n  }\n\n  if (ariaSelected !== undefined) {\n    a11yProperties['aria-selected'] = ariaSelected;\n  }\n\n  // Safari+VO needs aria-checked for multi-selection. Otherwise it only announces selected option even though another option is highlighted.\n  if (ariaChecked !== undefined) {\n    a11yProperties['aria-checked'] = ariaChecked;\n  }\n\n  if (ariaPosinset && ariaSetsize) {\n    a11yProperties['aria-posinset'] = ariaPosinset;\n    a11yProperties['aria-setsize'] = ariaSetsize;\n  }\n\n  if (restProps.ariaDescribedby) {\n    a11yProperties['aria-describedby'] = restProps.ariaDescribedby;\n  }\n\n  return (\n    <li\n      role=\"option\"\n      className={classNames}\n      style={style}\n      {...a11yProperties}\n      {...rest}\n      {...(isParent || disabled\n        ? {}\n        : getAnalyticsMetadataAttribute(getAnalyticsSelectActionMetadata({ isChild, value, ...restProps })))}\n    >\n      <div className={clsx(styles['option-content'], analyticsSelectors['option-content'])} ref={contentRef}>\n        {content}\n      </div>\n      <div className={styles['measure-strut']} ref={ref} />\n      <div className={styles['screenreader-content']} ref={screenReaderContentRef}></div>\n    </li>\n  );\n};\n\nexport default React.forwardRef(SelectableItem);\n"], "mappings": ";AAAA;AACA;AACA,OAAOA,KAAK,IAAIC,eAAe,EAAEC,MAAM,QAAQ,OAAO;AACtD,OAAOC,IAAI,MAAM,MAAM;AAEvB,SAASC,6BAA6B,QAAQ,kEAAkE;AAEhH,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,gCAAgC,QAAQ,4BAA4B;AAG7E,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,MAAM,MAAM,iBAAiB;AAIpC,MAAMC,cAAc,GAAGA,CACrBC,EAoBsB,EACtBC,GAA8B,KAC5B;MAtBF;MACEC,QAAQ,EAAEC,OAAO;MACjBC,YAAY;MACZC,WAAW;MACXC,QAAQ;MACRC,WAAW;MACXC,QAAQ;MACRC,aAAa;MACbC,QAAQ;MACRC,OAAO;MACPC,eAAe;MACfC,SAAS;MACTC,cAAc;MACdC,oBAAoB;MACpBC,mBAAmB;MACnBC,YAAY;MACZC,WAAW;MACXC,aAAa;MACbC;IAAK,IAAApB,EAEe;IADjBqB,SAAS,GAAAC,MAAA,CAAAtB,EAAA,EAnBd,oRAoBC,CADa;EAId,MAAMuB,EAAA,GAAyB5B,YAAY,CAAC0B,SAAS,CAAC;IAAhD;MAAEG;IAAS,IAAAD,EAAqC;IAAhCE,IAAI,GAAAH,MAAA,CAAAC,EAAA,EAApB,aAAsB,CAA0B;EACtD,MAAMG,UAAU,GAAGjC,IAAI,CAAC+B,SAAS,EAAE1B,MAAM,CAAC,iBAAiB,CAAC,EAAE;IAC5D,CAACA,MAAM,CAACQ,QAAQ,GAAGA,QAAQ;IAC3B,CAACR,MAAM,CAACS,WAAW,GAAGA,WAAW;IACjC,CAACT,MAAM,CAAC,gBAAgB,CAAC,GAAGW,aAAa;IACzC,CAACX,MAAM,CAAC6B,MAAM,GAAGjB,QAAQ;IACzB,CAACb,kBAAkB,CAAC8B,MAAM,GAAGjB,QAAQ;IACrC,CAACZ,MAAM,CAAC8B,KAAK,GAAGjB,OAAO;IACvB,CAACb,MAAM,CAAC,aAAa,CAAC,GAAGqB,aAAa,KAAK,UAAU;IACrD,CAACrB,MAAM,CAACU,QAAQ,GAAGA,QAAQ;IAC3B,CAACV,MAAM,CAAC+B,OAAO,GAAGjB,eAAe,KAAKkB,SAAS;IAC/C,CAAChC,MAAM,CAAC,YAAY,CAAC,GAAGe,SAAS;IACjC,CAACf,MAAM,CAAC,oBAAoB,CAAC,GAAGgB,cAAc;IAC9C,CAAChB,MAAM,CAACiC,iBAAiB,GAAGhB;GAC7B,CAAC;EAEF,MAAMiB,UAAU,GAAGxC,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAMyC,sBAAsB,GAAGzC,MAAM,CAAiB,IAAI,CAAC;EAE3DD,eAAe,CAAC,MAAK;IACnB;IACA;IACA;IACA;IACA;IACA,IAAIgB,WAAW,IAAIS,mBAAmB,EAAE;MACtC,IAAIgB,UAAU,CAACE,OAAO,EAAE;QACtBF,UAAU,CAACE,OAAO,CAACC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;;MAExD,IAAIF,sBAAsB,CAACC,OAAO,EAAE;QAClCD,sBAAsB,CAACC,OAAO,CAACE,WAAW,GAAGpB,mBAAmB;;;EAGtE,CAAC,EAAE,CAACT,WAAW,EAAES,mBAAmB,EAAEgB,UAAU,EAAEC,sBAAsB,CAAC,CAAC;EAE1E,MAAMI,KAAK,GACTzB,eAAe,KAAKkB,SAAS,GACzB;IACEQ,SAAS,EAAE,cAAc1B,eAAe;GACzC,GACDkB,SAAS;EAEf,MAAMS,cAAc,GAA0D;IAC5E,eAAe,EAAE/B;GAClB;EAED,IAAIE,QAAQ,IAAI,CAACK,oBAAoB,EAAE;IACrCwB,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI;;EAGtC,IAAInC,YAAY,KAAK0B,SAAS,EAAE;IAC9BS,cAAc,CAAC,eAAe,CAAC,GAAGnC,YAAY;;EAGhD;EACA,IAAIC,WAAW,KAAKyB,SAAS,EAAE;IAC7BS,cAAc,CAAC,cAAc,CAAC,GAAGlC,WAAW;;EAG9C,IAAIY,YAAY,IAAIC,WAAW,EAAE;IAC/BqB,cAAc,CAAC,eAAe,CAAC,GAAGtB,YAAY;IAC9CsB,cAAc,CAAC,cAAc,CAAC,GAAGrB,WAAW;;EAG9C,IAAIG,SAAS,CAACmB,eAAe,EAAE;IAC7BD,cAAc,CAAC,kBAAkB,CAAC,GAAGlB,SAAS,CAACmB,eAAe;;EAGhE,OACElD,KAAA,CAAAmD,aAAA,OAAAC,MAAA,CAAAC,MAAA;IACEC,IAAI,EAAC,QAAQ;IACbpB,SAAS,EAAEE,UAAU;IACrBW,KAAK,EAAEA;EAAK,GACRE,cAAc,EACdd,IAAI,EACHf,QAAQ,IAAIF,QAAQ,GACrB,EAAE,GACFd,6BAA6B,CAACE,gCAAgC,CAAA8C,MAAA,CAAAC,MAAA;IAAGhC,OAAO;IAAES;EAAK,GAAKC,SAAS,EAAG,CAAE,GAEtG/B,KAAA,CAAAmD,aAAA;IAAKjB,SAAS,EAAE/B,IAAI,CAACK,MAAM,CAAC,gBAAgB,CAAC,EAAED,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;IAAEI,GAAG,EAAE+B;EAAU,GAClG7B,OAAO,CACJ,EACNb,KAAA,CAAAmD,aAAA;IAAKjB,SAAS,EAAE1B,MAAM,CAAC,eAAe,CAAC;IAAEG,GAAG,EAAEA;EAAG,EAAI,EACrDX,KAAA,CAAAmD,aAAA;IAAKjB,SAAS,EAAE1B,MAAM,CAAC,sBAAsB,CAAC;IAAEG,GAAG,EAAEgC;EAAsB,EAAQ,CAChF;AAET,CAAC;AAED,eAAe3C,KAAK,CAACuD,UAAU,CAAC9C,cAAc,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}