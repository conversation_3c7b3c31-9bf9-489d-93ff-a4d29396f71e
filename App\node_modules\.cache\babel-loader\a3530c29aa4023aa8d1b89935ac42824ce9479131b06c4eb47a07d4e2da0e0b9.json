{"ast": null, "code": "// src/App.jsx\nimport{useState,useEffect}from'react';import{AppLayout,TopNavigation}from\"@cloudscape-design/components\";import{BrowserRouter,Routes,Route,Navigate}from'react-router-dom';import{applyMode,Mode,applyDensity,Density}from'@cloudscape-design/global-styles';import Navigation from\"./components/Navigation\";import Content from\"./components/Content\";import Tools from\"./components/Tools\";import Footer from\"./components/Footer\";import{routes}from'./routes/routes';import Introduction from'./pages/Introduction';import'@cloudscape-design/global-styles/index.css';import logo from\"./assets/images/logo.png\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function AppContent(){const[navigationOpen,setNavigationOpen]=useState(true);const[isDarkMode,setIsDarkMode]=useState(()=>{return localStorage.getItem('theme')==='dark';});const[isCompact,setIsCompact]=useState(()=>{return localStorage.getItem('density')==='compact';});useEffect(()=>{applyMode(isDarkMode?Mode.Dark:Mode.Light);localStorage.setItem('theme',isDarkMode?'dark':'light');},[isDarkMode]);useEffect(()=>{applyDensity(isCompact?Density.Compact:Density.Comfortable);localStorage.setItem('density',isCompact?'compact':'comfortable');},[isCompact]);const handleThemeChange=()=>{setIsDarkMode(prevMode=>!prevMode);};const handleDensityChange=()=>{setIsCompact(prevDensity=>!prevDensity);};return/*#__PURE__*/_jsx(BrowserRouter,{children:/*#__PURE__*/_jsxs(\"div\",{style:{minHeight:\"100vh\",display:\"flex\",flexDirection:\"column\"},children:[/*#__PURE__*/_jsx(TopNavigation,{identity:{href:\"/\",logo:{src:logo,alt:\"Amazonians Workshop Portal Logo\",title:\"Amazonians Workshop Portal\"}},utilities:[{type:\"button\",variant:\"link\",iconName:isCompact?\"view-full\":\"view-vertical\",text:isCompact?\"Comfortable\":\"Compact\",onClick:handleDensityChange,title:isCompact?\"Switch to comfortable density with relaxed spacing\":\"Switch to compact density with tighter spacing\"},{type:\"button\",variant:\"link\",iconName:isDarkMode?\"sun\":\"moon\",text:isDarkMode?\"Light Mode\":\"Dark Mode\",onClick:handleThemeChange,title:isDarkMode?\"Switch to light color theme\":\"Switch to dark color theme\"},{type:\"menu-dropdown\",text:\"Account\",description:\"<EMAIL>\",iconName:\"user-profile\",items:[{id:\"profile\",text:\"Profile\"},{id:\"preferences\",text:\"Preferences\"},{id:\"security\",text:\"Security\"},{id:\"signout\",text:\"Sign out\"}]}],i18nStrings:{searchIconAriaLabel:\"Search\",searchDismissIconAriaLabel:\"Close search\",overflowMenuTriggerText:\"More\"}}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1},children:/*#__PURE__*/_jsx(AppLayout,{navigation:/*#__PURE__*/_jsx(Navigation,{}),navigationOpen:navigationOpen,onNavigationChange:_ref=>{let{detail}=_ref;return setNavigationOpen(detail.open);},content:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Introduction,{})}),routes.map(route=>/*#__PURE__*/_jsx(Route,{path:route.path,element:/*#__PURE__*/_jsx(route.component,{})},route.id))]}),tools:/*#__PURE__*/_jsx(Tools,{}),toolsHide:false,contentType:\"default\",headerSelector:\"#header\",stickyNotifications:true,notifications:/*#__PURE__*/_jsx(\"div\",{id:\"notifications\"})})}),/*#__PURE__*/_jsx(Footer,{})]})});}function App(){return/*#__PURE__*/_jsx(AppContent,{});}export default App;", "map": {"version": 3, "names": ["useState", "useEffect", "AppLayout", "TopNavigation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Navigate", "applyMode", "Mode", "applyDensity", "Density", "Navigation", "Content", "Tools", "Footer", "routes", "Introduction", "logo", "jsx", "_jsx", "jsxs", "_jsxs", "A<PERSON><PERSON><PERSON>nt", "navigationOpen", "setNavigationOpen", "isDarkMode", "setIsDarkMode", "localStorage", "getItem", "isCompact", "setIsCompact", "Dark", "Light", "setItem", "Compact", "Comfortable", "handleThemeChange", "prevMode", "handleDensityChange", "prevDensity", "children", "style", "minHeight", "display", "flexDirection", "identity", "href", "src", "alt", "title", "utilities", "type", "variant", "iconName", "text", "onClick", "description", "items", "id", "i18nStrings", "searchIconAriaLabel", "searchDismissIconAriaLabel", "overflowMenuTriggerText", "flex", "navigation", "onNavigationChange", "_ref", "detail", "open", "content", "path", "element", "map", "route", "component", "tools", "toolsHide", "contentType", "headerSelector", "stickyNotifications", "notifications", "App"], "sources": ["C:/Repos2025/App-main/App/src/App.jsx"], "sourcesContent": ["// src/App.jsx\r\nimport { useState, useEffect } from 'react';\r\nimport { AppLayout, TopNavigation } from \"@cloudscape-design/components\";\r\nimport { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';\r\nimport { applyMode, Mode, applyDensity, Density } from '@cloudscape-design/global-styles';\r\nimport Navigation from \"./components/Navigation\";\r\nimport Content from \"./components/Content\";\r\nimport Tools from \"./components/Tools\";\r\nimport Footer from \"./components/Footer\";\r\nimport { routes } from './routes/routes';\r\nimport Introduction from './pages/Introduction';\r\nimport '@cloudscape-design/global-styles/index.css';\r\nimport logo from \"./assets/images/logo.png\";\r\n\r\n\r\n\r\nfunction AppContent() {\r\n  const [navigationOpen, setNavigationOpen] = useState(true);\r\n  const [isDarkMode, setIsDarkMode] = useState(() => {\r\n    return localStorage.getItem('theme') === 'dark';\r\n  });\r\n  const [isCompact, setIsCompact] = useState(() => {\r\n    return localStorage.getItem('density') === 'compact';\r\n  });\r\n\r\n  useEffect(() => {\r\n    applyMode(isDarkMode ? Mode.Dark : Mode.Light);\r\n    localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');\r\n  }, [isDarkMode]);\r\n\r\n  useEffect(() => {\r\n    applyDensity(isCompact ? Density.Compact : Density.Comfortable);\r\n    localStorage.setItem('density', isCompact ? 'compact' : 'comfortable');\r\n  }, [isCompact]);\r\n\r\n  const handleThemeChange = () => {\r\n    setIsDarkMode(prevMode => !prevMode);\r\n  };\r\n\r\n  const handleDensityChange = () => {\r\n    setIsCompact(prevDensity => !prevDensity);\r\n  };\r\n\r\n  return (\r\n    <BrowserRouter>\r\n      <div style={{ \r\n        minHeight: \"100vh\",\r\n        display: \"flex\",\r\n        flexDirection: \"column\"\r\n      }}>\r\n        <TopNavigation\r\n          identity={{\r\n            href: \"/\",\r\n            logo: {\r\n              src: logo,\r\n              alt: \"Amazonians Workshop Portal Logo\",\r\n              title: \"Amazonians Workshop Portal\"\r\n            }\r\n          }}\r\n          utilities={[\r\n            {\r\n              type: \"button\",\r\n              variant: \"link\",\r\n              iconName: isCompact ? \"view-full\" : \"view-vertical\",\r\n              text: isCompact ? \"Comfortable\" : \"Compact\",\r\n              onClick: handleDensityChange,\r\n              title: isCompact \r\n                ? \"Switch to comfortable density with relaxed spacing\" \r\n                : \"Switch to compact density with tighter spacing\"\r\n            },\r\n            {\r\n              type: \"button\",\r\n              variant: \"link\",\r\n              iconName: isDarkMode ? \"sun\" : \"moon\",\r\n              text: isDarkMode ? \"Light Mode\" : \"Dark Mode\",\r\n              onClick: handleThemeChange,\r\n              title: isDarkMode \r\n                ? \"Switch to light color theme\" \r\n                : \"Switch to dark color theme\"\r\n            },\r\n          \r\n            {\r\n              type: \"menu-dropdown\",\r\n              text: \"Account\",\r\n              description: \"<EMAIL>\",\r\n              iconName: \"user-profile\",\r\n              items: [\r\n                { id: \"profile\", text: \"Profile\" },\r\n                { id: \"preferences\", text: \"Preferences\" },\r\n                { id: \"security\", text: \"Security\" },\r\n                { id: \"signout\", text: \"Sign out\" }\r\n              ]\r\n            }\r\n          ]}\r\n          i18nStrings={{\r\n            searchIconAriaLabel: \"Search\",\r\n            searchDismissIconAriaLabel: \"Close search\",\r\n            overflowMenuTriggerText: \"More\"\r\n          }}\r\n        />\r\n        <div style={{ flex: 1 }}>\r\n          <AppLayout\r\n            navigation={<Navigation />}\r\n            navigationOpen={navigationOpen}\r\n            onNavigationChange={({ detail }) => setNavigationOpen(detail.open)}\r\n            content={\r\n              <Routes>\r\n                <Route \r\n                  path=\"/\" \r\n                  element={<Introduction />}\r\n                />\r\n                {routes.map((route) => (\r\n                  <Route\r\n                    key={route.id}\r\n                    path={route.path}\r\n                    element={<route.component />}\r\n                  />\r\n                ))}\r\n              </Routes>\r\n            }\r\n            tools={<Tools />}\r\n            toolsHide={false}\r\n            contentType=\"default\"\r\n            headerSelector=\"#header\"\r\n            stickyNotifications\r\n            notifications={<div id=\"notifications\" />}\r\n          />\r\n        </div>\r\n        <Footer />\r\n      </div>\r\n    </BrowserRouter>\r\n  );\r\n}\r\n\r\nfunction App() {\r\n  return <AppContent />;\r\n}\r\n\r\nexport default App;\r\n"], "mappings": "AAAA;AACA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,OAASC,SAAS,CAAEC,aAAa,KAAQ,+BAA+B,CACxE,OAASC,aAAa,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACzE,OAASC,SAAS,CAAEC,IAAI,CAAEC,YAAY,CAAEC,OAAO,KAAQ,kCAAkC,CACzF,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,OAAO,KAAM,sBAAsB,CAC1C,MAAO,CAAAC,KAAK,KAAM,oBAAoB,CACtC,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,OAASC,MAAM,KAAQ,iBAAiB,CACxC,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,4CAA4C,CACnD,MAAO,CAAAC,IAAI,KAAM,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAI5C,QAAS,CAAAC,UAAUA,CAAA,CAAG,CACpB,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGzB,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC0B,UAAU,CAAEC,aAAa,CAAC,CAAG3B,QAAQ,CAAC,IAAM,CACjD,MAAO,CAAA4B,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAK,MAAM,CACjD,CAAC,CAAC,CACF,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAG/B,QAAQ,CAAC,IAAM,CAC/C,MAAO,CAAA4B,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,GAAK,SAAS,CACtD,CAAC,CAAC,CAEF5B,SAAS,CAAC,IAAM,CACdO,SAAS,CAACkB,UAAU,CAAGjB,IAAI,CAACuB,IAAI,CAAGvB,IAAI,CAACwB,KAAK,CAAC,CAC9CL,YAAY,CAACM,OAAO,CAAC,OAAO,CAAER,UAAU,CAAG,MAAM,CAAG,OAAO,CAAC,CAC9D,CAAC,CAAE,CAACA,UAAU,CAAC,CAAC,CAEhBzB,SAAS,CAAC,IAAM,CACdS,YAAY,CAACoB,SAAS,CAAGnB,OAAO,CAACwB,OAAO,CAAGxB,OAAO,CAACyB,WAAW,CAAC,CAC/DR,YAAY,CAACM,OAAO,CAAC,SAAS,CAAEJ,SAAS,CAAG,SAAS,CAAG,aAAa,CAAC,CACxE,CAAC,CAAE,CAACA,SAAS,CAAC,CAAC,CAEf,KAAM,CAAAO,iBAAiB,CAAGA,CAAA,GAAM,CAC9BV,aAAa,CAACW,QAAQ,EAAI,CAACA,QAAQ,CAAC,CACtC,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChCR,YAAY,CAACS,WAAW,EAAI,CAACA,WAAW,CAAC,CAC3C,CAAC,CAED,mBACEpB,IAAA,CAAChB,aAAa,EAAAqC,QAAA,cACZnB,KAAA,QAAKoB,KAAK,CAAE,CACVC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CAAE,CAAAJ,QAAA,eACArB,IAAA,CAACjB,aAAa,EACZ2C,QAAQ,CAAE,CACRC,IAAI,CAAE,GAAG,CACT7B,IAAI,CAAE,CACJ8B,GAAG,CAAE9B,IAAI,CACT+B,GAAG,CAAE,iCAAiC,CACtCC,KAAK,CAAE,4BACT,CACF,CAAE,CACFC,SAAS,CAAE,CACT,CACEC,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAE,MAAM,CACfC,QAAQ,CAAExB,SAAS,CAAG,WAAW,CAAG,eAAe,CACnDyB,IAAI,CAAEzB,SAAS,CAAG,aAAa,CAAG,SAAS,CAC3C0B,OAAO,CAAEjB,mBAAmB,CAC5BW,KAAK,CAAEpB,SAAS,CACZ,oDAAoD,CACpD,gDACN,CAAC,CACD,CACEsB,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAE,MAAM,CACfC,QAAQ,CAAE5B,UAAU,CAAG,KAAK,CAAG,MAAM,CACrC6B,IAAI,CAAE7B,UAAU,CAAG,YAAY,CAAG,WAAW,CAC7C8B,OAAO,CAAEnB,iBAAiB,CAC1Ba,KAAK,CAAExB,UAAU,CACb,6BAA6B,CAC7B,4BACN,CAAC,CAED,CACE0B,IAAI,CAAE,eAAe,CACrBG,IAAI,CAAE,SAAS,CACfE,WAAW,CAAE,kBAAkB,CAC/BH,QAAQ,CAAE,cAAc,CACxBI,KAAK,CAAE,CACL,CAAEC,EAAE,CAAE,SAAS,CAAEJ,IAAI,CAAE,SAAU,CAAC,CAClC,CAAEI,EAAE,CAAE,aAAa,CAAEJ,IAAI,CAAE,aAAc,CAAC,CAC1C,CAAEI,EAAE,CAAE,UAAU,CAAEJ,IAAI,CAAE,UAAW,CAAC,CACpC,CAAEI,EAAE,CAAE,SAAS,CAAEJ,IAAI,CAAE,UAAW,CAAC,CAEvC,CAAC,CACD,CACFK,WAAW,CAAE,CACXC,mBAAmB,CAAE,QAAQ,CAC7BC,0BAA0B,CAAE,cAAc,CAC1CC,uBAAuB,CAAE,MAC3B,CAAE,CACH,CAAC,cACF3C,IAAA,QAAKsB,KAAK,CAAE,CAAEsB,IAAI,CAAE,CAAE,CAAE,CAAAvB,QAAA,cACtBrB,IAAA,CAAClB,SAAS,EACR+D,UAAU,cAAE7C,IAAA,CAACR,UAAU,GAAE,CAAE,CAC3BY,cAAc,CAAEA,cAAe,CAC/B0C,kBAAkB,CAAEC,IAAA,MAAC,CAAEC,MAAO,CAAC,CAAAD,IAAA,OAAK,CAAA1C,iBAAiB,CAAC2C,MAAM,CAACC,IAAI,CAAC,EAAC,CACnEC,OAAO,cACLhD,KAAA,CAACjB,MAAM,EAAAoC,QAAA,eACLrB,IAAA,CAACd,KAAK,EACJiE,IAAI,CAAC,GAAG,CACRC,OAAO,cAAEpD,IAAA,CAACH,YAAY,GAAE,CAAE,CAC3B,CAAC,CACDD,MAAM,CAACyD,GAAG,CAAEC,KAAK,eAChBtD,IAAA,CAACd,KAAK,EAEJiE,IAAI,CAAEG,KAAK,CAACH,IAAK,CACjBC,OAAO,cAAEpD,IAAA,CAACsD,KAAK,CAACC,SAAS,GAAE,CAAE,EAFxBD,KAAK,CAACf,EAGZ,CACF,CAAC,EACI,CACT,CACDiB,KAAK,cAAExD,IAAA,CAACN,KAAK,GAAE,CAAE,CACjB+D,SAAS,CAAE,KAAM,CACjBC,WAAW,CAAC,SAAS,CACrBC,cAAc,CAAC,SAAS,CACxBC,mBAAmB,MACnBC,aAAa,cAAE7D,IAAA,QAAKuC,EAAE,CAAC,eAAe,CAAE,CAAE,CAC3C,CAAC,CACC,CAAC,cACNvC,IAAA,CAACL,MAAM,GAAE,CAAC,EACP,CAAC,CACO,CAAC,CAEpB,CAEA,QAAS,CAAAmE,GAAGA,CAAA,CAAG,CACb,mBAAO9D,IAAA,CAACG,UAAU,GAAE,CAAC,CACvB,CAEA,cAAe,CAAA2D,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}