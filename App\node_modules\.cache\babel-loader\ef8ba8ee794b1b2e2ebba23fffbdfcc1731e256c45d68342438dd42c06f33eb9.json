{"ast": null, "code": "import { __rest } from \"tslib\";\n// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';\nimport { TransitionGroup } from 'react-transition-group';\nimport clsx from 'clsx';\nimport { findUpUntil } from '@cloudscape-design/component-toolkit/dom';\nimport { getAnalyticsMetadataAttribute } from '@cloudscape-design/component-toolkit/internal/analytics-metadata';\nimport { useInternalI18n } from '../i18n/context';\nimport InternalIcon from '../icon/internal';\nimport { animate, getDOMRects } from '../internal/animate';\nimport { Transition } from '../internal/components/transition';\nimport { getVisualContextClassname } from '../internal/components/visual-context';\nimport customCssProps from '../internal/generated/custom-css-properties';\nimport { useEffectOnUpdate } from '../internal/hooks/use-effect-on-update';\nimport { useUniqueId } from '../internal/hooks/use-unique-id';\nimport { scrollElementIntoView } from '../internal/utils/scrollable-containers';\nimport { throttle } from '../internal/utils/throttle';\nimport { getComponentsAnalyticsMetadata, getItemAnalyticsMetadata } from './analytics-metadata/utils';\nimport { useFlashbar } from './common';\nimport { Flash, focusFlashById } from './flash';\nimport { counterTypes, getFlashTypeCount, getItemColor, getVisibleCollapsedItems } from './utils';\nimport styles from './styles.css.js';\n// If the number of items is equal or less than this value,\n// the toggle element will not be displayed and the Flashbar will look like a regular single-item Flashbar.\nconst maxNonCollapsibleItems = 1;\nconst resizeListenerThrottleDelay = 100;\nexport default function CollapsibleFlashbar(_a) {\n  var {\n      items\n    } = _a,\n    restProps = __rest(_a, [\"items\"]);\n  const [enteringItems, setEnteringItems] = useState([]);\n  const [exitingItems, setExitingItems] = useState([]);\n  const [isFlashbarStackExpanded, setIsFlashbarStackExpanded] = useState(false);\n  const getElementsToAnimate = useCallback(() => {\n    const flashElements = isFlashbarStackExpanded ? expandedItemRefs.current : collapsedItemRefs.current;\n    return Object.assign(Object.assign({}, flashElements), {\n      notificationBar: notificationBarRef.current\n    });\n  }, [isFlashbarStackExpanded]);\n  const prepareAnimations = useCallback(() => {\n    const rects = getDOMRects(getElementsToAnimate());\n    setInitialAnimationState(rects);\n  }, [getElementsToAnimate]);\n  const {\n    baseProps,\n    breakpoint,\n    isReducedMotion,\n    isVisualRefresh,\n    mergedRef,\n    ref\n  } = useFlashbar(Object.assign(Object.assign({\n    items\n  }, restProps), {\n    onItemsAdded: newItems => {\n      setEnteringItems([...enteringItems, ...newItems]);\n    },\n    onItemsChanged: options => {\n      // If not all items have ID, we can still animate collapse/expand transitions\n      // because we can rely on each item's index in the original array,\n      // but we can't do that when elements are added or removed, since the index changes.\n      if ((options === null || options === void 0 ? void 0 : options.allItemsHaveId) && !(options === null || options === void 0 ? void 0 : options.isReducedMotion)) {\n        prepareAnimations();\n      }\n    },\n    onItemsRemoved: removedItems => {\n      setExitingItems([...exitingItems, ...removedItems]);\n    }\n  }));\n  const collapsedItemRefs = useRef({});\n  const expandedItemRefs = useRef({});\n  const [initialAnimationState, setInitialAnimationState] = useState(null);\n  const listElementRef = useRef(null);\n  const notificationBarRef = useRef(null);\n  const [transitioning, setTransitioning] = useState(false);\n  const flashbarElementId = useUniqueId('flashbar');\n  const itemCountElementId = useUniqueId('item-count');\n  if (items.length <= maxNonCollapsibleItems && isFlashbarStackExpanded) {\n    setIsFlashbarStackExpanded(false);\n  }\n  const animateFlash = !isReducedMotion;\n  function toggleCollapseExpand() {\n    if (!isReducedMotion) {\n      prepareAnimations();\n    }\n    setIsFlashbarStackExpanded(prev => !prev);\n  }\n  useLayoutEffect(() => {\n    if (isFlashbarStackExpanded && (items === null || items === void 0 ? void 0 : items.length)) {\n      const mostRecentItem = items[0];\n      if (mostRecentItem.id !== undefined) {\n        focusFlashById(ref.current, mostRecentItem.id);\n      }\n    }\n    // Run this after expanding, but not every time the items change.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isFlashbarStackExpanded]);\n  // When collapsing, scroll up if necessary to avoid losing track of the focused button\n  useEffectOnUpdate(() => {\n    if (!isFlashbarStackExpanded && notificationBarRef.current) {\n      scrollElementIntoView(notificationBarRef.current);\n    }\n  }, [isFlashbarStackExpanded]);\n  const updateBottomSpacing = useMemo(() => throttle(() => {\n    // Allow vertical space between Flashbar and page bottom only when the Flashbar is reaching the end of the page,\n    // otherwise avoid spacing with eventual sticky elements below.\n    const listElement = listElementRef === null || listElementRef === void 0 ? void 0 : listElementRef.current;\n    const flashbar = listElement === null || listElement === void 0 ? void 0 : listElement.parentElement;\n    if (listElement && flashbar) {\n      // Make sure the bottom padding is present when we make the calculations,\n      // then we might decide to remove it or not.\n      flashbar.classList.remove(styles.floating);\n      const windowHeight = window.innerHeight;\n      // Take the parent region into account if using the App Layout, because it might have additional margins.\n      // Otherwise we use the Flashbar component for this calculation.\n      const outerElement = findUpUntil(flashbar, element => element.getAttribute('role') === 'region') || flashbar;\n      const applySpacing = isFlashbarStackExpanded && Math.ceil(outerElement.getBoundingClientRect().bottom) >= windowHeight;\n      if (!applySpacing) {\n        flashbar.classList.add(styles.floating);\n      }\n    }\n  }, resizeListenerThrottleDelay), [isFlashbarStackExpanded]);\n  useLayoutEffect(() => {\n    window.addEventListener('resize', updateBottomSpacing);\n    return () => {\n      window.removeEventListener('resize', updateBottomSpacing);\n      updateBottomSpacing.cancel();\n    };\n  }, [updateBottomSpacing]);\n  const {\n    i18nStrings\n  } = restProps;\n  const i18n = useInternalI18n('flashbar');\n  const ariaLabel = i18n('i18nStrings.ariaLabel', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.ariaLabel);\n  const notificationBarText = i18n('i18nStrings.notificationBarText', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.notificationBarText);\n  const notificationBarAriaLabel = i18n('i18nStrings.notificationBarAriaLabel', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.notificationBarAriaLabel);\n  const iconAriaLabels = {\n    errorIconAriaLabel: i18n('i18nStrings.errorIconAriaLabel', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.errorIconAriaLabel),\n    inProgressIconAriaLabel: i18n('i18nStrings.inProgressIconAriaLabel', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.inProgressIconAriaLabel),\n    infoIconAriaLabel: i18n('i18nStrings.infoIconAriaLabel', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.infoIconAriaLabel),\n    successIconAriaLabel: i18n('i18nStrings.successIconAriaLabel', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.successIconAriaLabel),\n    warningIconAriaLabel: i18n('i18nStrings.warningIconAriaLabel', i18nStrings === null || i18nStrings === void 0 ? void 0 : i18nStrings.warningIconAriaLabel)\n  };\n  useLayoutEffect(() => {\n    // When `useLayoutEffect` is called, the DOM is updated but has not been painted yet,\n    // so it's a good moment to trigger animations that will make calculations based on old and new DOM state.\n    // The old state is kept in `initialAnimationState`\n    // and the new state can be retrieved from the current DOM elements.\n    if (initialAnimationState) {\n      updateBottomSpacing();\n      animate({\n        elements: getElementsToAnimate(),\n        oldState: initialAnimationState,\n        newElementInitialState: ({\n          top\n        }) => ({\n          scale: 0.9,\n          y: -0.2 * top\n        }),\n        onTransitionsEnd: () => setTransitioning(false)\n      });\n      setTransitioning(true);\n      setInitialAnimationState(null);\n    }\n  }, [updateBottomSpacing, getElementsToAnimate, initialAnimationState, isFlashbarStackExpanded]);\n  const isCollapsible = items.length > maxNonCollapsibleItems;\n  const countByType = getFlashTypeCount(items);\n  const numberOfColorsInStack = new Set(items.map(getItemColor)).size;\n  const maxSlots = Math.max(numberOfColorsInStack, 3);\n  const stackDepth = Math.min(maxSlots, items.length);\n  const itemsToShow = isFlashbarStackExpanded ? items.map((item, index) => Object.assign(Object.assign({}, item), {\n    expandedIndex: index\n  })) : getVisibleCollapsedItems(items, stackDepth).map((item, index) => Object.assign(Object.assign({}, item), {\n    collapsedIndex: index\n  }));\n  const getItemId = item => {\n    var _a, _b;\n    return (_b = (_a = item.id) !== null && _a !== void 0 ? _a : item.expandedIndex) !== null && _b !== void 0 ? _b : 0;\n  };\n  // This check allows us to use the standard \"enter\" Transition only when the notification was not existing before.\n  // If instead it was moved to the top of the stack but was already present in the array\n  // (e.g, after dismissing another notification),\n  // we need to use different, more custom and more controlled animations.\n  const hasEntered = item => enteringItems.some(_item => _item.id && _item.id === item.id);\n  const hasLeft = item => !('expandedIndex' in item);\n  const hasEnteredOrLeft = item => hasEntered(item) || hasLeft(item);\n  const showInnerContent = item => isFlashbarStackExpanded || hasLeft(item) || 'expandedIndex' in item && item.expandedIndex === 0;\n  const shouldUseStandardAnimation = (item, index) => index === 0 && hasEnteredOrLeft(item);\n  const getAnimationElementId = item => `flash-${getItemId(item)}`;\n  const renderList = () => React.createElement(\"ul\", {\n    ref: listElementRef,\n    className: clsx(styles['flash-list'], isFlashbarStackExpanded ? styles.expanded : styles.collapsed, transitioning && styles['animation-running'], initialAnimationState && styles['animation-ready'], isVisualRefresh && styles['visual-refresh']),\n    id: flashbarElementId,\n    \"aria-label\": ariaLabel,\n    \"aria-describedby\": isCollapsible ? itemCountElementId : undefined,\n    style: !isFlashbarStackExpanded || transitioning ? {\n      [customCssProps.flashbarStackDepth]: stackDepth\n    } : undefined\n  }, React.createElement(ListWrapper, {\n    withMotion: !isReducedMotion\n  }, itemsToShow.map((item, index) => React.createElement(Transition, {\n    key: getItemId(item),\n    in: !hasLeft(item),\n    onStatusChange: status => {\n      if (status === 'entered') {\n        setEnteringItems([]);\n      } else if (status === 'exited') {\n        setExitingItems([]);\n      }\n    }\n  }, (state, transitionRootElement) => {\n    var _a, _b, _c;\n    return React.createElement(\"li\", Object.assign({\n      \"aria-hidden\": !showInnerContent(item),\n      className: showInnerContent(item) ? clsx(styles['flash-list-item'], !isFlashbarStackExpanded && styles.item, !collapsedItemRefs.current[getAnimationElementId(item)] && styles['expanded-only']) : clsx(styles.flash, styles[`flash-type-${(_a = item.type) !== null && _a !== void 0 ? _a : 'info'}`], styles.item),\n      ref: element => {\n        if (isFlashbarStackExpanded) {\n          expandedItemRefs.current[getAnimationElementId(item)] = element;\n        } else {\n          collapsedItemRefs.current[getAnimationElementId(item)] = element;\n        }\n      },\n      style: !isFlashbarStackExpanded || transitioning ? {\n        [customCssProps.flashbarStackIndex]: (_c = (_b = item.collapsedIndex) !== null && _b !== void 0 ? _b : item.expandedIndex) !== null && _c !== void 0 ? _c : index\n      } : undefined,\n      key: getItemId(item)\n    }, getAnalyticsMetadataAttribute(getItemAnalyticsMetadata(index + 1, item.type || 'info', item.id))), showInnerContent(item) && React.createElement(Flash\n    // eslint-disable-next-line react/forbid-component-props\n    , Object.assign({\n      // eslint-disable-next-line react/forbid-component-props\n      className: clsx(animateFlash && styles['flash-with-motion'], isVisualRefresh && styles['flash-refresh']),\n      key: getItemId(item),\n      ref: shouldUseStandardAnimation(item, index) ? transitionRootElement : undefined,\n      transitionState: shouldUseStandardAnimation(item, index) ? state : undefined,\n      i18nStrings: iconAriaLabels\n    }, item)));\n  }))));\n  return React.createElement(\"div\", Object.assign({}, baseProps, {\n    className: clsx(baseProps.className, styles.flashbar, styles[`breakpoint-${breakpoint}`], styles.stack, isCollapsible && styles.collapsible, items.length === 2 && styles['short-list'], isFlashbarStackExpanded && styles.expanded, isVisualRefresh && styles['visual-refresh']),\n    ref: mergedRef\n  }, getAnalyticsMetadataAttribute(getComponentsAnalyticsMetadata(items.length, true, isFlashbarStackExpanded))), isFlashbarStackExpanded && renderList(), isCollapsible && React.createElement(\"div\", Object.assign({\n    className: clsx(styles['notification-bar'], isVisualRefresh && styles['visual-refresh'], isFlashbarStackExpanded ? styles.expanded : styles.collapsed, transitioning && styles['animation-running'], items.length === 2 && styles['short-list'], getVisualContextClassname('flashbar') // Visual context is needed for focus ring to be white\n    ),\n    onClick: toggleCollapseExpand,\n    ref: notificationBarRef\n  }, getAnalyticsMetadataAttribute({\n    action: 'expand',\n    detail: {\n      label: 'h2',\n      expanded: `${!isFlashbarStackExpanded}`\n    }\n  })), React.createElement(\"span\", {\n    \"aria-live\": \"polite\",\n    className: styles.status,\n    role: \"status\",\n    id: itemCountElementId\n  }, notificationBarText && React.createElement(\"h2\", {\n    className: styles.header\n  }, notificationBarText), React.createElement(\"span\", {\n    className: styles['item-count']\n  }, counterTypes.map(({\n    type,\n    labelName,\n    iconName\n  }) => React.createElement(NotificationTypeCount, {\n    key: type,\n    iconName: iconName,\n    label: iconAriaLabels[labelName],\n    count: countByType[type]\n  })))), React.createElement(\"button\", {\n    \"aria-controls\": flashbarElementId,\n    \"aria-describedby\": itemCountElementId,\n    \"aria-expanded\": isFlashbarStackExpanded,\n    \"aria-label\": notificationBarAriaLabel,\n    className: clsx(styles.button, isFlashbarStackExpanded && styles.expanded)\n  }, React.createElement(InternalIcon, {\n    className: styles.icon,\n    size: \"normal\",\n    name: \"angle-down\"\n  }))), !isFlashbarStackExpanded && renderList());\n}\nconst NotificationTypeCount = ({\n  iconName,\n  label,\n  count\n}) => {\n  return React.createElement(\"span\", {\n    className: styles['type-count']\n  }, React.createElement(\"span\", {\n    title: label\n  }, React.createElement(InternalIcon, {\n    name: iconName,\n    ariaLabel: label\n  })), React.createElement(\"span\", {\n    className: styles['count-number']\n  }, count));\n};\nconst ListWrapper = ({\n  children,\n  withMotion\n}) => withMotion ? React.createElement(TransitionGroup, {\n  component: null\n}, children) : React.createElement(React.Fragment, null, children);", "map": {"version": 3, "names": ["React", "useCallback", "useLayoutEffect", "useMemo", "useRef", "useState", "TransitionGroup", "clsx", "findUpUntil", "getAnalyticsMetadataAttribute", "useInternalI18n", "InternalIcon", "animate", "getDOMRects", "Transition", "getVisualContextClassname", "customCssProps", "useEffectOnUpdate", "useUniqueId", "scrollElementIntoView", "throttle", "getComponentsAnalyticsMetadata", "getItemAnalyticsMetadata", "useFlashbar", "Flash", "focusFlashById", "counterTypes", "getFlashTypeCount", "getItemColor", "getVisibleCollapsedItems", "styles", "maxNonCollapsibleItems", "resizeListenerThrottleDelay", "CollapsibleFlashbar", "_a", "items", "restProps", "__rest", "enteringItems", "setEnteringItems", "exitingItems", "setExitingItems", "isFlashbarStackExpanded", "setIsFlashbarStackExpanded", "getElementsToAnimate", "flashElements", "expandedItemRefs", "current", "collapsedItemRefs", "Object", "assign", "notificationBar", "notificationBarRef", "prepareAnimations", "rects", "setInitialAnimationState", "baseProps", "breakpoint", "isReducedMotion", "isVisualRefresh", "mergedRef", "ref", "onItemsAdded", "newItems", "onItemsChanged", "options", "allItemsHaveId", "onItemsRemoved", "removedItems", "initialAnimationState", "listElementRef", "transitioning", "setTransitioning", "flashbarElementId", "itemCountElementId", "length", "animateFlash", "toggleCollapseExpand", "prev", "mostRecentItem", "id", "undefined", "updateBottomSpacing", "listElement", "flashbar", "parentElement", "classList", "remove", "floating", "windowHeight", "window", "innerHeight", "outerElement", "element", "getAttribute", "applySpacing", "Math", "ceil", "getBoundingClientRect", "bottom", "add", "addEventListener", "removeEventListener", "cancel", "i18nStrings", "i18n", "aria<PERSON><PERSON><PERSON>", "notificationBarText", "notificationBarAriaLabel", "iconAriaLabels", "errorIconAriaLabel", "inProgressIconAriaLabel", "infoIconAriaLabel", "successIconAriaLabel", "warningIconAriaLabel", "elements", "oldState", "newElementInitialState", "top", "scale", "y", "onTransitionsEnd", "isCollapsible", "countByType", "numberOfColorsInStack", "Set", "map", "size", "maxSlots", "max", "stack<PERSON>epth", "min", "itemsToShow", "item", "index", "expandedIndex", "collapsedIndex", "getItemId", "_b", "hasEntered", "some", "_item", "hasLeft", "hasEnteredOrLeft", "showInnerContent", "shouldUseStandardAnimation", "getAnimationElementId", "renderList", "createElement", "className", "expanded", "collapsed", "style", "flashbarStackDepth", "ListWrapper", "withMotion", "key", "in", "onStatusChange", "status", "state", "transitionRootElement", "flash", "type", "flashbarStackIndex", "_c", "transitionState", "stack", "collapsible", "onClick", "action", "detail", "label", "role", "header", "labelName", "iconName", "NotificationTypeCount", "count", "button", "icon", "name", "title", "children", "component", "Fragment"], "sources": ["C:\\Repos2025\\App-main\\App\\node_modules\\src\\flashbar\\collapsible-flashbar.tsx"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { ReactNode, useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';\nimport { TransitionGroup } from 'react-transition-group';\nimport clsx from 'clsx';\n\nimport { findUpUntil } from '@cloudscape-design/component-toolkit/dom';\nimport { getAnalyticsMetadataAttribute } from '@cloudscape-design/component-toolkit/internal/analytics-metadata';\n\nimport { useInternalI18n } from '../i18n/context';\nimport { IconProps } from '../icon/interfaces';\nimport InternalIcon from '../icon/internal';\nimport { animate, getDOMRects } from '../internal/animate';\nimport { Transition } from '../internal/components/transition';\nimport { getVisualContextClassname } from '../internal/components/visual-context';\nimport customCssProps from '../internal/generated/custom-css-properties';\nimport { useEffectOnUpdate } from '../internal/hooks/use-effect-on-update';\nimport { useUniqueId } from '../internal/hooks/use-unique-id';\nimport { scrollElementIntoView } from '../internal/utils/scrollable-containers';\nimport { throttle } from '../internal/utils/throttle';\nimport { GeneratedAnalyticsMetadataFlashbarExpand } from './analytics-metadata/interfaces';\nimport { getComponentsAnalyticsMetadata, getItemAnalyticsMetadata } from './analytics-metadata/utils';\nimport { useFlashbar } from './common';\nimport { Flash, focusFlashById } from './flash';\nimport { FlashbarProps } from './interfaces';\nimport { counterTypes, getFlashTypeCount, getItemColor, getVisibleCollapsedItems, StackableItem } from './utils';\n\nimport styles from './styles.css.js';\n\n// If the number of items is equal or less than this value,\n// the toggle element will not be displayed and the Flashbar will look like a regular single-item Flashbar.\nconst maxNonCollapsibleItems = 1;\n\nconst resizeListenerThrottleDelay = 100;\n\nexport default function CollapsibleFlashbar({ items, ...restProps }: FlashbarProps) {\n  const [enteringItems, setEnteringItems] = useState<ReadonlyArray<FlashbarProps.MessageDefinition>>([]);\n  const [exitingItems, setExitingItems] = useState<ReadonlyArray<FlashbarProps.MessageDefinition>>([]);\n  const [isFlashbarStackExpanded, setIsFlashbarStackExpanded] = useState(false);\n\n  const getElementsToAnimate = useCallback(() => {\n    const flashElements = isFlashbarStackExpanded ? expandedItemRefs.current : collapsedItemRefs.current;\n    return { ...flashElements, notificationBar: notificationBarRef.current };\n  }, [isFlashbarStackExpanded]);\n\n  const prepareAnimations = useCallback(() => {\n    const rects = getDOMRects(getElementsToAnimate());\n    setInitialAnimationState(rects);\n  }, [getElementsToAnimate]);\n\n  const { baseProps, breakpoint, isReducedMotion, isVisualRefresh, mergedRef, ref } = useFlashbar({\n    items,\n    ...restProps,\n    onItemsAdded: newItems => {\n      setEnteringItems([...enteringItems, ...newItems]);\n    },\n    onItemsChanged: options => {\n      // If not all items have ID, we can still animate collapse/expand transitions\n      // because we can rely on each item's index in the original array,\n      // but we can't do that when elements are added or removed, since the index changes.\n      if (options?.allItemsHaveId && !options?.isReducedMotion) {\n        prepareAnimations();\n      }\n    },\n    onItemsRemoved: removedItems => {\n      setExitingItems([...exitingItems, ...removedItems]);\n    },\n  });\n\n  const collapsedItemRefs = useRef<Record<string, HTMLElement | null>>({});\n  const expandedItemRefs = useRef<Record<string, HTMLElement | null>>({});\n  const [initialAnimationState, setInitialAnimationState] = useState<Record<string, DOMRect> | null>(null);\n  const listElementRef = useRef<HTMLUListElement | null>(null);\n  const notificationBarRef = useRef<HTMLDivElement | null>(null);\n  const [transitioning, setTransitioning] = useState(false);\n  const flashbarElementId = useUniqueId('flashbar');\n  const itemCountElementId = useUniqueId('item-count');\n\n  if (items.length <= maxNonCollapsibleItems && isFlashbarStackExpanded) {\n    setIsFlashbarStackExpanded(false);\n  }\n\n  const animateFlash = !isReducedMotion;\n\n  function toggleCollapseExpand() {\n    if (!isReducedMotion) {\n      prepareAnimations();\n    }\n    setIsFlashbarStackExpanded(prev => !prev);\n  }\n\n  useLayoutEffect(() => {\n    if (isFlashbarStackExpanded && items?.length) {\n      const mostRecentItem = items[0];\n      if (mostRecentItem.id !== undefined) {\n        focusFlashById(ref.current, mostRecentItem.id);\n      }\n    }\n    // Run this after expanding, but not every time the items change.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isFlashbarStackExpanded]);\n\n  // When collapsing, scroll up if necessary to avoid losing track of the focused button\n  useEffectOnUpdate(() => {\n    if (!isFlashbarStackExpanded && notificationBarRef.current) {\n      scrollElementIntoView(notificationBarRef.current);\n    }\n  }, [isFlashbarStackExpanded]);\n\n  const updateBottomSpacing = useMemo(\n    () =>\n      throttle(() => {\n        // Allow vertical space between Flashbar and page bottom only when the Flashbar is reaching the end of the page,\n        // otherwise avoid spacing with eventual sticky elements below.\n        const listElement = listElementRef?.current;\n        const flashbar = listElement?.parentElement;\n        if (listElement && flashbar) {\n          // Make sure the bottom padding is present when we make the calculations,\n          // then we might decide to remove it or not.\n          flashbar.classList.remove(styles.floating);\n          const windowHeight = window.innerHeight;\n          // Take the parent region into account if using the App Layout, because it might have additional margins.\n          // Otherwise we use the Flashbar component for this calculation.\n          const outerElement = findUpUntil(flashbar, element => element.getAttribute('role') === 'region') || flashbar;\n          const applySpacing =\n            isFlashbarStackExpanded && Math.ceil(outerElement.getBoundingClientRect().bottom) >= windowHeight;\n          if (!applySpacing) {\n            flashbar.classList.add(styles.floating);\n          }\n        }\n      }, resizeListenerThrottleDelay),\n    [isFlashbarStackExpanded]\n  );\n\n  useLayoutEffect(() => {\n    window.addEventListener('resize', updateBottomSpacing);\n    return () => {\n      window.removeEventListener('resize', updateBottomSpacing);\n      updateBottomSpacing.cancel();\n    };\n  }, [updateBottomSpacing]);\n\n  const { i18nStrings } = restProps;\n\n  const i18n = useInternalI18n('flashbar');\n  const ariaLabel = i18n('i18nStrings.ariaLabel', i18nStrings?.ariaLabel);\n  const notificationBarText = i18n('i18nStrings.notificationBarText', i18nStrings?.notificationBarText);\n  const notificationBarAriaLabel = i18n('i18nStrings.notificationBarAriaLabel', i18nStrings?.notificationBarAriaLabel);\n  const iconAriaLabels = {\n    errorIconAriaLabel: i18n('i18nStrings.errorIconAriaLabel', i18nStrings?.errorIconAriaLabel),\n    inProgressIconAriaLabel: i18n('i18nStrings.inProgressIconAriaLabel', i18nStrings?.inProgressIconAriaLabel),\n    infoIconAriaLabel: i18n('i18nStrings.infoIconAriaLabel', i18nStrings?.infoIconAriaLabel),\n    successIconAriaLabel: i18n('i18nStrings.successIconAriaLabel', i18nStrings?.successIconAriaLabel),\n    warningIconAriaLabel: i18n('i18nStrings.warningIconAriaLabel', i18nStrings?.warningIconAriaLabel),\n  };\n\n  useLayoutEffect(() => {\n    // When `useLayoutEffect` is called, the DOM is updated but has not been painted yet,\n    // so it's a good moment to trigger animations that will make calculations based on old and new DOM state.\n    // The old state is kept in `initialAnimationState`\n    // and the new state can be retrieved from the current DOM elements.\n\n    if (initialAnimationState) {\n      updateBottomSpacing();\n\n      animate({\n        elements: getElementsToAnimate(),\n        oldState: initialAnimationState,\n        newElementInitialState: ({ top }) => ({ scale: 0.9, y: -0.2 * top }),\n        onTransitionsEnd: () => setTransitioning(false),\n      });\n\n      setTransitioning(true);\n      setInitialAnimationState(null);\n    }\n  }, [updateBottomSpacing, getElementsToAnimate, initialAnimationState, isFlashbarStackExpanded]);\n\n  const isCollapsible = items.length > maxNonCollapsibleItems;\n\n  const countByType = getFlashTypeCount(items);\n\n  const numberOfColorsInStack = new Set(items.map(getItemColor)).size;\n  const maxSlots = Math.max(numberOfColorsInStack, 3);\n  const stackDepth = Math.min(maxSlots, items.length);\n\n  const itemsToShow = isFlashbarStackExpanded\n    ? items.map((item, index) => ({ ...item, expandedIndex: index }))\n    : getVisibleCollapsedItems(items, stackDepth).map((item: StackableItem, index: number) => ({\n        ...item,\n        collapsedIndex: index,\n      }));\n\n  const getItemId = (item: StackableItem | FlashbarProps.MessageDefinition) =>\n    item.id ?? (item as StackableItem).expandedIndex ?? 0;\n\n  // This check allows us to use the standard \"enter\" Transition only when the notification was not existing before.\n  // If instead it was moved to the top of the stack but was already present in the array\n  // (e.g, after dismissing another notification),\n  // we need to use different, more custom and more controlled animations.\n  const hasEntered = (item: StackableItem | FlashbarProps.MessageDefinition) =>\n    enteringItems.some(_item => _item.id && _item.id === item.id);\n  const hasLeft = (item: StackableItem | FlashbarProps.MessageDefinition) => !('expandedIndex' in item);\n  const hasEnteredOrLeft = (item: StackableItem | FlashbarProps.MessageDefinition) => hasEntered(item) || hasLeft(item);\n\n  const showInnerContent = (item: StackableItem | FlashbarProps.MessageDefinition) =>\n    isFlashbarStackExpanded || hasLeft(item) || ('expandedIndex' in item && item.expandedIndex === 0);\n\n  const shouldUseStandardAnimation = (item: StackableItem, index: number) => index === 0 && hasEnteredOrLeft(item);\n\n  const getAnimationElementId = (item: StackableItem) => `flash-${getItemId(item)}`;\n\n  const renderList = () => (\n    <ul\n      ref={listElementRef}\n      className={clsx(\n        styles['flash-list'],\n        isFlashbarStackExpanded ? styles.expanded : styles.collapsed,\n        transitioning && styles['animation-running'],\n        initialAnimationState && styles['animation-ready'],\n        isVisualRefresh && styles['visual-refresh']\n      )}\n      id={flashbarElementId}\n      aria-label={ariaLabel}\n      aria-describedby={isCollapsible ? itemCountElementId : undefined}\n      style={\n        !isFlashbarStackExpanded || transitioning\n          ? {\n              [customCssProps.flashbarStackDepth]: stackDepth,\n            }\n          : undefined\n      }\n    >\n      <ListWrapper withMotion={!isReducedMotion}>\n        {itemsToShow.map((item: StackableItem, index: number) => (\n          <Transition\n            key={getItemId(item)}\n            in={!hasLeft(item)}\n            onStatusChange={status => {\n              if (status === 'entered') {\n                setEnteringItems([]);\n              } else if (status === 'exited') {\n                setExitingItems([]);\n              }\n            }}\n          >\n            {(state: string, transitionRootElement: React.Ref<HTMLDivElement> | undefined) => (\n              <li\n                aria-hidden={!showInnerContent(item)}\n                className={\n                  showInnerContent(item)\n                    ? clsx(\n                        styles['flash-list-item'],\n                        !isFlashbarStackExpanded && styles.item,\n                        !collapsedItemRefs.current[getAnimationElementId(item)] && styles['expanded-only']\n                      )\n                    : clsx(styles.flash, styles[`flash-type-${item.type ?? 'info'}`], styles.item)\n                }\n                ref={element => {\n                  if (isFlashbarStackExpanded) {\n                    expandedItemRefs.current[getAnimationElementId(item)] = element;\n                  } else {\n                    collapsedItemRefs.current[getAnimationElementId(item)] = element;\n                  }\n                }}\n                style={\n                  !isFlashbarStackExpanded || transitioning\n                    ? {\n                        [customCssProps.flashbarStackIndex]:\n                          (item as StackableItem).collapsedIndex ?? (item as StackableItem).expandedIndex ?? index,\n                      }\n                    : undefined\n                }\n                key={getItemId(item)}\n                {...getAnalyticsMetadataAttribute(getItemAnalyticsMetadata(index + 1, item.type || 'info', item.id))}\n              >\n                {showInnerContent(item) && (\n                  <Flash\n                    // eslint-disable-next-line react/forbid-component-props\n                    className={clsx(\n                      animateFlash && styles['flash-with-motion'],\n                      isVisualRefresh && styles['flash-refresh']\n                    )}\n                    key={getItemId(item)}\n                    ref={shouldUseStandardAnimation(item, index) ? transitionRootElement : undefined}\n                    transitionState={shouldUseStandardAnimation(item, index) ? state : undefined}\n                    i18nStrings={iconAriaLabels}\n                    {...item}\n                  />\n                )}\n              </li>\n            )}\n          </Transition>\n        ))}\n      </ListWrapper>\n    </ul>\n  );\n\n  return (\n    <div\n      {...baseProps}\n      className={clsx(\n        baseProps.className,\n        styles.flashbar,\n        styles[`breakpoint-${breakpoint}`],\n        styles.stack,\n        isCollapsible && styles.collapsible,\n        items.length === 2 && styles['short-list'],\n        isFlashbarStackExpanded && styles.expanded,\n        isVisualRefresh && styles['visual-refresh']\n      )}\n      ref={mergedRef}\n      {...getAnalyticsMetadataAttribute(getComponentsAnalyticsMetadata(items.length, true, isFlashbarStackExpanded))}\n    >\n      {isFlashbarStackExpanded && renderList()}\n      {isCollapsible && (\n        <div\n          className={clsx(\n            styles['notification-bar'],\n            isVisualRefresh && styles['visual-refresh'],\n            isFlashbarStackExpanded ? styles.expanded : styles.collapsed,\n            transitioning && styles['animation-running'],\n            items.length === 2 && styles['short-list'],\n            getVisualContextClassname('flashbar') // Visual context is needed for focus ring to be white\n          )}\n          onClick={toggleCollapseExpand}\n          ref={notificationBarRef}\n          {...getAnalyticsMetadataAttribute({\n            action: 'expand',\n            detail: {\n              label: 'h2',\n              expanded: `${!isFlashbarStackExpanded}`,\n            },\n          } as GeneratedAnalyticsMetadataFlashbarExpand)}\n        >\n          <span aria-live=\"polite\" className={styles.status} role=\"status\" id={itemCountElementId}>\n            {notificationBarText && <h2 className={styles.header}>{notificationBarText}</h2>}\n            <span className={styles['item-count']}>\n              {counterTypes.map(({ type, labelName, iconName }) => (\n                <NotificationTypeCount\n                  key={type}\n                  iconName={iconName}\n                  label={iconAriaLabels[labelName]}\n                  count={countByType[type]}\n                />\n              ))}\n            </span>\n          </span>\n          <button\n            aria-controls={flashbarElementId}\n            aria-describedby={itemCountElementId}\n            aria-expanded={isFlashbarStackExpanded}\n            aria-label={notificationBarAriaLabel}\n            className={clsx(styles.button, isFlashbarStackExpanded && styles.expanded)}\n          >\n            <InternalIcon className={styles.icon} size=\"normal\" name=\"angle-down\" />\n          </button>\n        </div>\n      )}\n      {!isFlashbarStackExpanded && renderList()}\n    </div>\n  );\n}\n\nconst NotificationTypeCount = ({\n  iconName,\n  label,\n  count,\n}: {\n  iconName: IconProps.Name;\n  label?: string;\n  count: number;\n}) => {\n  return (\n    <span className={styles['type-count']}>\n      <span title={label}>\n        <InternalIcon name={iconName} ariaLabel={label} />\n      </span>\n      <span className={styles['count-number']}>{count}</span>\n    </span>\n  );\n};\n\nconst ListWrapper = ({ children, withMotion }: { children: ReactNode; withMotion: boolean }) =>\n  withMotion ? <TransitionGroup component={null}>{children}</TransitionGroup> : <>{children}</>;\n"], "mappings": ";AAAA;AACA;AACA,OAAOA,KAAK,IAAeC,WAAW,EAAEC,eAAe,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACjG,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAOC,IAAI,MAAM,MAAM;AAEvB,SAASC,WAAW,QAAQ,0CAA0C;AACtE,SAASC,6BAA6B,QAAQ,kEAAkE;AAEhH,SAASC,eAAe,QAAQ,iBAAiB;AAEjD,OAAOC,YAAY,MAAM,kBAAkB;AAC3C,SAASC,OAAO,EAAEC,WAAW,QAAQ,qBAAqB;AAC1D,SAASC,UAAU,QAAQ,mCAAmC;AAC9D,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,OAAOC,cAAc,MAAM,6CAA6C;AACxE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,WAAW,QAAQ,iCAAiC;AAC7D,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,QAAQ,QAAQ,4BAA4B;AAErD,SAASC,8BAA8B,EAAEC,wBAAwB,QAAQ,4BAA4B;AACrG,SAASC,WAAW,QAAQ,UAAU;AACtC,SAASC,KAAK,EAAEC,cAAc,QAAQ,SAAS;AAE/C,SAASC,YAAY,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,wBAAwB,QAAuB,SAAS;AAEhH,OAAOC,MAAM,MAAM,iBAAiB;AAEpC;AACA;AACA,MAAMC,sBAAsB,GAAG,CAAC;AAEhC,MAAMC,2BAA2B,GAAG,GAAG;AAEvC,eAAc,SAAUC,mBAAmBA,CAACC,EAAsC;MAAtC;MAAEC;IAAK,IAAAD,EAA+B;IAA1BE,SAAS,GAAAC,MAAA,CAAAH,EAAA,EAArB,SAAuB,CAAF;EAC/D,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAiD,EAAE,CAAC;EACtG,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAiD,EAAE,CAAC;EACpG,MAAM,CAACqC,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAE7E,MAAMuC,oBAAoB,GAAG3C,WAAW,CAAC,MAAK;IAC5C,MAAM4C,aAAa,GAAGH,uBAAuB,GAAGI,gBAAgB,CAACC,OAAO,GAAGC,iBAAiB,CAACD,OAAO;IACpG,OAAAE,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAYL,aAAa;MAAEM,eAAe,EAAEC,kBAAkB,CAACL;IAAO;EACxE,CAAC,EAAE,CAACL,uBAAuB,CAAC,CAAC;EAE7B,MAAMW,iBAAiB,GAAGpD,WAAW,CAAC,MAAK;IACzC,MAAMqD,KAAK,GAAGzC,WAAW,CAAC+B,oBAAoB,EAAE,CAAC;IACjDW,wBAAwB,CAACD,KAAK,CAAC;EACjC,CAAC,EAAE,CAACV,oBAAoB,CAAC,CAAC;EAE1B,MAAM;IAAEY,SAAS;IAAEC,UAAU;IAAEC,eAAe;IAAEC,eAAe;IAAEC,SAAS;IAAEC;EAAG,CAAE,GAAGtC,WAAW,CAAA0B,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA;IAC7Ff;EAAK,GACFC,SAAS;IACZ0B,YAAY,EAAEC,QAAQ,IAAG;MACvBxB,gBAAgB,CAAC,CAAC,GAAGD,aAAa,EAAE,GAAGyB,QAAQ,CAAC,CAAC;IACnD,CAAC;IACDC,cAAc,EAAEC,OAAO,IAAG;MACxB;MACA;MACA;MACA,IAAI,CAAAA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,cAAc,KAAI,EAACD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEP,eAAe,GAAE;QACxDL,iBAAiB,EAAE;;IAEvB,CAAC;IACDc,cAAc,EAAEC,YAAY,IAAG;MAC7B3B,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAE,GAAG4B,YAAY,CAAC,CAAC;IACrD;EAAC,GACD;EAEF,MAAMpB,iBAAiB,GAAG5C,MAAM,CAAqC,EAAE,CAAC;EACxE,MAAM0C,gBAAgB,GAAG1C,MAAM,CAAqC,EAAE,CAAC;EACvE,MAAM,CAACiE,qBAAqB,EAAEd,wBAAwB,CAAC,GAAGlD,QAAQ,CAAiC,IAAI,CAAC;EACxG,MAAMiE,cAAc,GAAGlE,MAAM,CAA0B,IAAI,CAAC;EAC5D,MAAMgD,kBAAkB,GAAGhD,MAAM,CAAwB,IAAI,CAAC;EAC9D,MAAM,CAACmE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAMoE,iBAAiB,GAAGvD,WAAW,CAAC,UAAU,CAAC;EACjD,MAAMwD,kBAAkB,GAAGxD,WAAW,CAAC,YAAY,CAAC;EAEpD,IAAIiB,KAAK,CAACwC,MAAM,IAAI5C,sBAAsB,IAAIW,uBAAuB,EAAE;IACrEC,0BAA0B,CAAC,KAAK,CAAC;;EAGnC,MAAMiC,YAAY,GAAG,CAAClB,eAAe;EAErC,SAASmB,oBAAoBA,CAAA;IAC3B,IAAI,CAACnB,eAAe,EAAE;MACpBL,iBAAiB,EAAE;;IAErBV,0BAA0B,CAACmC,IAAI,IAAI,CAACA,IAAI,CAAC;EAC3C;EAEA5E,eAAe,CAAC,MAAK;IACnB,IAAIwC,uBAAuB,KAAIP,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwC,MAAM,GAAE;MAC5C,MAAMI,cAAc,GAAG5C,KAAK,CAAC,CAAC,CAAC;MAC/B,IAAI4C,cAAc,CAACC,EAAE,KAAKC,SAAS,EAAE;QACnCxD,cAAc,CAACoC,GAAG,CAACd,OAAO,EAAEgC,cAAc,CAACC,EAAE,CAAC;;;IAGlD;IACA;EACF,CAAC,EAAE,CAACtC,uBAAuB,CAAC,CAAC;EAE7B;EACAzB,iBAAiB,CAAC,MAAK;IACrB,IAAI,CAACyB,uBAAuB,IAAIU,kBAAkB,CAACL,OAAO,EAAE;MAC1D5B,qBAAqB,CAACiC,kBAAkB,CAACL,OAAO,CAAC;;EAErD,CAAC,EAAE,CAACL,uBAAuB,CAAC,CAAC;EAE7B,MAAMwC,mBAAmB,GAAG/E,OAAO,CACjC,MACEiB,QAAQ,CAAC,MAAK;IACZ;IACA;IACA,MAAM+D,WAAW,GAAGb,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEvB,OAAO;IAC3C,MAAMqC,QAAQ,GAAGD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEE,aAAa;IAC3C,IAAIF,WAAW,IAAIC,QAAQ,EAAE;MAC3B;MACA;MACAA,QAAQ,CAACE,SAAS,CAACC,MAAM,CAACzD,MAAM,CAAC0D,QAAQ,CAAC;MAC1C,MAAMC,YAAY,GAAGC,MAAM,CAACC,WAAW;MACvC;MACA;MACA,MAAMC,YAAY,GAAGpF,WAAW,CAAC4E,QAAQ,EAAES,OAAO,IAAIA,OAAO,CAACC,YAAY,CAAC,MAAM,CAAC,KAAK,QAAQ,CAAC,IAAIV,QAAQ;MAC5G,MAAMW,YAAY,GAChBrD,uBAAuB,IAAIsD,IAAI,CAACC,IAAI,CAACL,YAAY,CAACM,qBAAqB,EAAE,CAACC,MAAM,CAAC,IAAIV,YAAY;MACnG,IAAI,CAACM,YAAY,EAAE;QACjBX,QAAQ,CAACE,SAAS,CAACc,GAAG,CAACtE,MAAM,CAAC0D,QAAQ,CAAC;;;EAG7C,CAAC,EAAExD,2BAA2B,CAAC,EACjC,CAACU,uBAAuB,CAAC,CAC1B;EAEDxC,eAAe,CAAC,MAAK;IACnBwF,MAAM,CAACW,gBAAgB,CAAC,QAAQ,EAAEnB,mBAAmB,CAAC;IACtD,OAAO,MAAK;MACVQ,MAAM,CAACY,mBAAmB,CAAC,QAAQ,EAAEpB,mBAAmB,CAAC;MACzDA,mBAAmB,CAACqB,MAAM,EAAE;IAC9B,CAAC;EACH,CAAC,EAAE,CAACrB,mBAAmB,CAAC,CAAC;EAEzB,MAAM;IAAEsB;EAAW,CAAE,GAAGpE,SAAS;EAEjC,MAAMqE,IAAI,GAAG/F,eAAe,CAAC,UAAU,CAAC;EACxC,MAAMgG,SAAS,GAAGD,IAAI,CAAC,uBAAuB,EAAED,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEE,SAAS,CAAC;EACvE,MAAMC,mBAAmB,GAAGF,IAAI,CAAC,iCAAiC,EAAED,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEG,mBAAmB,CAAC;EACrG,MAAMC,wBAAwB,GAAGH,IAAI,CAAC,sCAAsC,EAAED,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,wBAAwB,CAAC;EACpH,MAAMC,cAAc,GAAG;IACrBC,kBAAkB,EAAEL,IAAI,CAAC,gCAAgC,EAAED,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEM,kBAAkB,CAAC;IAC3FC,uBAAuB,EAAEN,IAAI,CAAC,qCAAqC,EAAED,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEO,uBAAuB,CAAC;IAC1GC,iBAAiB,EAAEP,IAAI,CAAC,+BAA+B,EAAED,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEQ,iBAAiB,CAAC;IACxFC,oBAAoB,EAAER,IAAI,CAAC,kCAAkC,EAAED,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAES,oBAAoB,CAAC;IACjGC,oBAAoB,EAAET,IAAI,CAAC,kCAAkC,EAAED,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEU,oBAAoB;GACjG;EAEDhH,eAAe,CAAC,MAAK;IACnB;IACA;IACA;IACA;IAEA,IAAImE,qBAAqB,EAAE;MACzBa,mBAAmB,EAAE;MAErBtE,OAAO,CAAC;QACNuG,QAAQ,EAAEvE,oBAAoB,EAAE;QAChCwE,QAAQ,EAAE/C,qBAAqB;QAC/BgD,sBAAsB,EAAEA,CAAC;UAAEC;QAAG,CAAE,MAAM;UAAEC,KAAK,EAAE,GAAG;UAAEC,CAAC,EAAE,CAAC,GAAG,GAAGF;QAAG,CAAE,CAAC;QACpEG,gBAAgB,EAAEA,CAAA,KAAMjD,gBAAgB,CAAC,KAAK;OAC/C,CAAC;MAEFA,gBAAgB,CAAC,IAAI,CAAC;MACtBjB,wBAAwB,CAAC,IAAI,CAAC;;EAElC,CAAC,EAAE,CAAC2B,mBAAmB,EAAEtC,oBAAoB,EAAEyB,qBAAqB,EAAE3B,uBAAuB,CAAC,CAAC;EAE/F,MAAMgF,aAAa,GAAGvF,KAAK,CAACwC,MAAM,GAAG5C,sBAAsB;EAE3D,MAAM4F,WAAW,GAAGhG,iBAAiB,CAACQ,KAAK,CAAC;EAE5C,MAAMyF,qBAAqB,GAAG,IAAIC,GAAG,CAAC1F,KAAK,CAAC2F,GAAG,CAAClG,YAAY,CAAC,CAAC,CAACmG,IAAI;EACnE,MAAMC,QAAQ,GAAGhC,IAAI,CAACiC,GAAG,CAACL,qBAAqB,EAAE,CAAC,CAAC;EACnD,MAAMM,UAAU,GAAGlC,IAAI,CAACmC,GAAG,CAACH,QAAQ,EAAE7F,KAAK,CAACwC,MAAM,CAAC;EAEnD,MAAMyD,WAAW,GAAG1F,uBAAuB,GACvCP,KAAK,CAAC2F,GAAG,CAAC,CAACO,IAAI,EAAEC,KAAK,KAAKrF,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAMmF,IAAI;IAAEE,aAAa,EAAED;EAAK,EAAG,CAAC,GAC/DzG,wBAAwB,CAACM,KAAK,EAAE+F,UAAU,CAAC,CAACJ,GAAG,CAAC,CAACO,IAAmB,EAAEC,KAAa,KAAKrF,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACnFmF,IAAI;IACPG,cAAc,EAAEF;EAAK,EACrB,CAAC;EAEP,MAAMG,SAAS,GAAIJ,IAAqD,IAAI;IAAA,IAAAnG,EAAA,EAAAwG,EAAA;IAC1E,QAAAA,EAAA,IAAAxG,EAAA,GAAAmG,IAAI,CAACrD,EAAE,cAAA9C,EAAA,cAAAA,EAAA,GAAKmG,IAAsB,CAACE,aAAa,cAAAG,EAAA,cAAAA,EAAA,GAAI,CAAC;EAAA;EAEvD;EACA;EACA;EACA;EACA,MAAMC,UAAU,GAAIN,IAAqD,IACvE/F,aAAa,CAACsG,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAC7D,EAAE,IAAI6D,KAAK,CAAC7D,EAAE,KAAKqD,IAAI,CAACrD,EAAE,CAAC;EAC/D,MAAM8D,OAAO,GAAIT,IAAqD,IAAK,EAAE,eAAe,IAAIA,IAAI,CAAC;EACrG,MAAMU,gBAAgB,GAAIV,IAAqD,IAAKM,UAAU,CAACN,IAAI,CAAC,IAAIS,OAAO,CAACT,IAAI,CAAC;EAErH,MAAMW,gBAAgB,GAAIX,IAAqD,IAC7E3F,uBAAuB,IAAIoG,OAAO,CAACT,IAAI,CAAC,IAAK,eAAe,IAAIA,IAAI,IAAIA,IAAI,CAACE,aAAa,KAAK,CAAE;EAEnG,MAAMU,0BAA0B,GAAGA,CAACZ,IAAmB,EAAEC,KAAa,KAAKA,KAAK,KAAK,CAAC,IAAIS,gBAAgB,CAACV,IAAI,CAAC;EAEhH,MAAMa,qBAAqB,GAAIb,IAAmB,IAAK,SAASI,SAAS,CAACJ,IAAI,CAAC,EAAE;EAEjF,MAAMc,UAAU,GAAGA,CAAA,KACjBnJ,KAAA,CAAAoJ,aAAA;IACEvF,GAAG,EAAES,cAAc;IACnB+E,SAAS,EAAE9I,IAAI,CACbuB,MAAM,CAAC,YAAY,CAAC,EACpBY,uBAAuB,GAAGZ,MAAM,CAACwH,QAAQ,GAAGxH,MAAM,CAACyH,SAAS,EAC5DhF,aAAa,IAAIzC,MAAM,CAAC,mBAAmB,CAAC,EAC5CuC,qBAAqB,IAAIvC,MAAM,CAAC,iBAAiB,CAAC,EAClD6B,eAAe,IAAI7B,MAAM,CAAC,gBAAgB,CAAC,CAC5C;IACDkD,EAAE,EAAEP,iBAAiB;IAAA,cACTiC,SAAS;IAAA,oBACHgB,aAAa,GAAGhD,kBAAkB,GAAGO,SAAS;IAChEuE,KAAK,EACH,CAAC9G,uBAAuB,IAAI6B,aAAa,GACrC;MACE,CAACvD,cAAc,CAACyI,kBAAkB,GAAGvB;KACtC,GACDjD;EAAS,GAGfjF,KAAA,CAAAoJ,aAAA,CAACM,WAAW;IAACC,UAAU,EAAE,CAACjG;EAAe,GACtC0E,WAAW,CAACN,GAAG,CAAC,CAACO,IAAmB,EAAEC,KAAa,KAClDtI,KAAA,CAAAoJ,aAAA,CAACtI,UAAU;IACT8I,GAAG,EAAEnB,SAAS,CAACJ,IAAI,CAAC;IACpBwB,EAAE,EAAE,CAACf,OAAO,CAACT,IAAI,CAAC;IAClByB,cAAc,EAAEC,MAAM,IAAG;MACvB,IAAIA,MAAM,KAAK,SAAS,EAAE;QACxBxH,gBAAgB,CAAC,EAAE,CAAC;OACrB,MAAM,IAAIwH,MAAM,KAAK,QAAQ,EAAE;QAC9BtH,eAAe,CAAC,EAAE,CAAC;;IAEvB;EAAC,GAEA,CAACuH,KAAa,EAAEC,qBAA4D,KAAI;;IAAC,OAChFjK,KAAA,CAAAoJ,aAAA,OAAAnG,MAAA,CAAAC,MAAA;MAAA,eACe,CAAC8F,gBAAgB,CAACX,IAAI,CAAC;MACpCgB,SAAS,EACPL,gBAAgB,CAACX,IAAI,CAAC,GAClB9H,IAAI,CACFuB,MAAM,CAAC,iBAAiB,CAAC,EACzB,CAACY,uBAAuB,IAAIZ,MAAM,CAACuG,IAAI,EACvC,CAACrF,iBAAiB,CAACD,OAAO,CAACmG,qBAAqB,CAACb,IAAI,CAAC,CAAC,IAAIvG,MAAM,CAAC,eAAe,CAAC,CACnF,GACDvB,IAAI,CAACuB,MAAM,CAACoI,KAAK,EAAEpI,MAAM,CAAC,cAAc,CAAAI,EAAA,GAAAmG,IAAI,CAAC8B,IAAI,cAAAjI,EAAA,cAAAA,EAAA,GAAI,MAAM,EAAE,CAAC,EAAEJ,MAAM,CAACuG,IAAI,CAAC;MAElFxE,GAAG,EAAEgC,OAAO,IAAG;QACb,IAAInD,uBAAuB,EAAE;UAC3BI,gBAAgB,CAACC,OAAO,CAACmG,qBAAqB,CAACb,IAAI,CAAC,CAAC,GAAGxC,OAAO;SAChE,MAAM;UACL7C,iBAAiB,CAACD,OAAO,CAACmG,qBAAqB,CAACb,IAAI,CAAC,CAAC,GAAGxC,OAAO;;MAEpE,CAAC;MACD2D,KAAK,EACH,CAAC9G,uBAAuB,IAAI6B,aAAa,GACrC;QACE,CAACvD,cAAc,CAACoJ,kBAAkB,GAChC,CAAAC,EAAA,IAAA3B,EAAA,GAACL,IAAsB,CAACG,cAAc,cAAAE,EAAA,cAAAA,EAAA,GAAKL,IAAsB,CAACE,aAAa,cAAA8B,EAAA,cAAAA,EAAA,GAAI/B;OACtF,GACDrD,SAAS;MAEf2E,GAAG,EAAEnB,SAAS,CAACJ,IAAI;IAAC,GAChB5H,6BAA6B,CAACa,wBAAwB,CAACgH,KAAK,GAAG,CAAC,EAAED,IAAI,CAAC8B,IAAI,IAAI,MAAM,EAAE9B,IAAI,CAACrD,EAAE,CAAC,CAAC,GAEnGgE,gBAAgB,CAACX,IAAI,CAAC,IACrBrI,KAAA,CAAAoJ,aAAA,CAAC5H;IACC;IAAA,E;MAAA;MACA6H,SAAS,EAAE9I,IAAI,CACbqE,YAAY,IAAI9C,MAAM,CAAC,mBAAmB,CAAC,EAC3C6B,eAAe,IAAI7B,MAAM,CAAC,eAAe,CAAC,CAC3C;MACD8H,GAAG,EAAEnB,SAAS,CAACJ,IAAI,CAAC;MACpBxE,GAAG,EAAEoF,0BAA0B,CAACZ,IAAI,EAAEC,KAAK,CAAC,GAAG2B,qBAAqB,GAAGhF,SAAS;MAChFqF,eAAe,EAAErB,0BAA0B,CAACZ,IAAI,EAAEC,KAAK,CAAC,GAAG0B,KAAK,GAAG/E,SAAS;MAC5EuB,WAAW,EAAEK;IAAc,GACvBwB,IAAI,EAEX,CACE;GACN,CAEJ,CAAC,CACU,CAEjB;EAED,OACErI,KAAA,CAAAoJ,aAAA,QAAAnG,MAAA,CAAAC,MAAA,KACMM,SAAS;IACb6F,SAAS,EAAE9I,IAAI,CACbiD,SAAS,CAAC6F,SAAS,EACnBvH,MAAM,CAACsD,QAAQ,EACftD,MAAM,CAAC,cAAc2B,UAAU,EAAE,CAAC,EAClC3B,MAAM,CAACyI,KAAK,EACZ7C,aAAa,IAAI5F,MAAM,CAAC0I,WAAW,EACnCrI,KAAK,CAACwC,MAAM,KAAK,CAAC,IAAI7C,MAAM,CAAC,YAAY,CAAC,EAC1CY,uBAAuB,IAAIZ,MAAM,CAACwH,QAAQ,EAC1C3F,eAAe,IAAI7B,MAAM,CAAC,gBAAgB,CAAC,CAC5C;IACD+B,GAAG,EAAED;EAAS,GACVnD,6BAA6B,CAACY,8BAA8B,CAACc,KAAK,CAACwC,MAAM,EAAE,IAAI,EAAEjC,uBAAuB,CAAC,CAAC,GAE7GA,uBAAuB,IAAIyG,UAAU,EAAE,EACvCzB,aAAa,IACZ1H,KAAA,CAAAoJ,aAAA,QAAAnG,MAAA,CAAAC,MAAA;IACEmG,SAAS,EAAE9I,IAAI,CACbuB,MAAM,CAAC,kBAAkB,CAAC,EAC1B6B,eAAe,IAAI7B,MAAM,CAAC,gBAAgB,CAAC,EAC3CY,uBAAuB,GAAGZ,MAAM,CAACwH,QAAQ,GAAGxH,MAAM,CAACyH,SAAS,EAC5DhF,aAAa,IAAIzC,MAAM,CAAC,mBAAmB,CAAC,EAC5CK,KAAK,CAACwC,MAAM,KAAK,CAAC,IAAI7C,MAAM,CAAC,YAAY,CAAC,EAC1Cf,yBAAyB,CAAC,UAAU,CAAC,CAAC;KACvC;IACD0J,OAAO,EAAE5F,oBAAoB;IAC7BhB,GAAG,EAAET;EAAkB,GACnB3C,6BAA6B,CAAC;IAChCiK,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE;MACNC,KAAK,EAAE,IAAI;MACXtB,QAAQ,EAAE,GAAG,CAAC5G,uBAAuB;;GAEI,CAAC,GAE9C1C,KAAA,CAAAoJ,aAAA;IAAA,aAAgB,QAAQ;IAACC,SAAS,EAAEvH,MAAM,CAACiI,MAAM;IAAEc,IAAI,EAAC,QAAQ;IAAC7F,EAAE,EAAEN;EAAkB,GACpFiC,mBAAmB,IAAI3G,KAAA,CAAAoJ,aAAA;IAAIC,SAAS,EAAEvH,MAAM,CAACgJ;EAAM,GAAGnE,mBAAmB,CAAM,EAChF3G,KAAA,CAAAoJ,aAAA;IAAMC,SAAS,EAAEvH,MAAM,CAAC,YAAY;EAAC,GAClCJ,YAAY,CAACoG,GAAG,CAAC,CAAC;IAAEqC,IAAI;IAAEY,SAAS;IAAEC;EAAQ,CAAE,KAC9ChL,KAAA,CAAAoJ,aAAA,CAAC6B,qBAAqB;IACpBrB,GAAG,EAAEO,IAAI;IACTa,QAAQ,EAAEA,QAAQ;IAClBJ,KAAK,EAAE/D,cAAc,CAACkE,SAAS,CAAC;IAChCG,KAAK,EAAEvD,WAAW,CAACwC,IAAI;EAAC,EAE3B,CAAC,CACG,CACF,EACPnK,KAAA,CAAAoJ,aAAA;IAAA,iBACiB3E,iBAAiB;IAAA,oBACdC,kBAAkB;IAAA,iBACrBhC,uBAAuB;IAAA,cAC1BkE,wBAAwB;IACpCyC,SAAS,EAAE9I,IAAI,CAACuB,MAAM,CAACqJ,MAAM,EAAEzI,uBAAuB,IAAIZ,MAAM,CAACwH,QAAQ;EAAC,GAE1EtJ,KAAA,CAAAoJ,aAAA,CAACzI,YAAY;IAAC0I,SAAS,EAAEvH,MAAM,CAACsJ,IAAI;IAAErD,IAAI,EAAC,QAAQ;IAACsD,IAAI,EAAC;EAAY,EAAG,CACjE,CAEZ,EACA,CAAC3I,uBAAuB,IAAIyG,UAAU,EAAE,CACrC;AAEV;AAEA,MAAM8B,qBAAqB,GAAGA,CAAC;EAC7BD,QAAQ;EACRJ,KAAK;EACLM;AAAK,CAKN,KAAI;EACH,OACElL,KAAA,CAAAoJ,aAAA;IAAMC,SAAS,EAAEvH,MAAM,CAAC,YAAY;EAAC,GACnC9B,KAAA,CAAAoJ,aAAA;IAAMkC,KAAK,EAAEV;EAAK,GAChB5K,KAAA,CAAAoJ,aAAA,CAACzI,YAAY;IAAC0K,IAAI,EAAEL,QAAQ;IAAEtE,SAAS,EAAEkE;EAAK,EAAI,CAC7C,EACP5K,KAAA,CAAAoJ,aAAA;IAAMC,SAAS,EAAEvH,MAAM,CAAC,cAAc;EAAC,GAAGoJ,KAAK,CAAQ,CAClD;AAEX,CAAC;AAED,MAAMxB,WAAW,GAAGA,CAAC;EAAE6B,QAAQ;EAAE5B;AAAU,CAAgD,KACzFA,UAAU,GAAG3J,KAAA,CAAAoJ,aAAA,CAAC9I,eAAe;EAACkL,SAAS,EAAE;AAAI,GAAGD,QAAQ,CAAmB,GAAGvL,KAAA,CAAAoJ,aAAA,CAAApJ,KAAA,CAAAyL,QAAA,QAAGF,QAAQ,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}