{"version": 3, "file": "chart-container.js", "sourceRoot": "", "sources": ["../../../src/area-chart/chart-container.tsx"], "names": [], "mappings": "AAAA,qEAAqE;AACrE,sCAAsC;AACtC,OAAO,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAE1E,OAAO,SAAS,MAAM,mDAAmD,CAAC;AAC1E,OAAO,cAAc,EAAE,EAAE,iBAAiB,EAAE,MAAM,yDAAyD,CAAC;AAC5G,OAAO,EAAE,uBAAuB,EAAE,MAAM,wDAAwD,CAAC;AACjG,OAAO,kBAAkB,MAAM,4DAA4D,CAAC;AAC5F,OAAO,iBAAiB,MAAM,4DAA4D,CAAC;AAC3F,OAAO,aAAa,MAAM,uDAAuD,CAAC;AAClF,OAAO,SAAS,MAAM,mCAAmC,CAAC;AAC1D,OAAO,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAChE,OAAO,iBAAiB,MAAM,uCAAuC,CAAC;AACtE,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,gBAAgB,MAAM,0BAA0B,CAAC;AACxD,OAAO,cAAc,MAAM,wBAAwB,CAAC;AACpD,OAAO,oBAAoB,MAAM,8BAA8B,CAAC;AAChE,OAAO,mBAAmB,MAAM,kCAAkC,CAAC;AACnE,OAAO,kBAAkB,MAAM,4BAA4B,CAAC;AAI5D,MAAM,mBAAmB,GAAG,GAAG,CAAC;AAChC,MAAM,0BAA0B,GAAG,EAAE,CAAC;AACtC,MAAM,uBAAuB,GAAG,EAAE,CAAC;AA2BnC,eAAe,IAAI,CAAC,cAAc,CAA0B,CAAC;AAE7D,SAAS,cAAc,CAAqC,EAC1D,KAAK,EACL,SAAS,EACT,MAAM,EACN,MAAM,EACN,iBAAiB,EACjB,mBAAmB,EACnB,SAAS,EACT,cAAc,EACd,eAAe,EACf,WAAW,EAAE,EACX,cAAc,EAAE,wBAAwB,EACxC,cAAc,EAAE,wBAAwB,EACxC,oBAAoB,EAAE,8BAA8B,EACpD,gBAAgB,EAChB,wBAAwB,EACxB,wBAAwB,EACxB,wBAAwB,EACxB,6BAA6B,GAC9B,GAAG,EAAE,EACN,SAAS,EACT,UAAU,EACV,SAAS,EACT,cAAc,GAAG,wBAAwB,EACzC,cAAc,GAAG,wBAAwB,EACzC,oBAAoB,GAAG,8BAA8B,EACrD,KAAK,GACkB;IACvB,MAAM,CAAC,sBAAsB,EAAE,yBAAyB,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxE,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;IACnF,MAAM,yBAAyB,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;IAEjE,MAAM,mBAAmB,GAAG,iBAAiB,CAAC;QAC5C,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM;QAC5B,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM;QAC5B,aAAa,EAAE,cAA+B;KAC/C,CAAC,CAAC;IAEH,kEAAkE;IAClE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,cAAc,GAAG,sBAAsB,GAAG,0BAA0B,CAAC,CAAC;IACpG,SAAS,CAAC,GAAG,EAAE;QACb,SAAS,CAAC,SAAS,CAAC,CAAC;IACvB,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;IAE3B,MAAM,gBAAgB,GAAG,mBAAmB,CAAC;QAC3C,KAAK;QACL,cAAc;QACd,cAAc;QACd,oBAAoB;QACpB,gBAAgB;KACjB,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,MAAM,CAAc,IAAI,CAAC,CAAC;IAEtD,MAAM,SAAS,GAAG,YAAY,CAAC,iBAAiB,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAExE,MAAM,kBAAkB,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,gBAAgB,KAAK,IAAI,CAAC;IAE9E,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;IAElF,MAAM,0BAA0B,GAAG,OAAO,CACxC,GAAG,EAAE,CAAC,CAAC,mBAAmB,IAAI,YAAY,CAAC,CAAC,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAC3F,CAAC,mBAAmB,EAAE,YAAY,CAAC,CACpC,CAAC;IAEF,OAAO,CACL,oBAAC,uBAAuB,IACtB,GAAG,EAAE,SAAS,EACd,SAAS,EAAE,SAAS,GAAG,mBAAmB,CAAC,MAAM,EACjD,SAAS,EAAE,CAAC,CAAC,SAAS,EACtB,UAAU,EAAE,UAAU,EACtB,aAAa,EAAE,oBAAC,SAAS,IAAC,IAAI,EAAC,GAAG,EAAC,QAAQ,EAAC,MAAM,EAAC,KAAK,EAAE,MAAM,GAAI,EACpE,oBAAoB,EAClB,oBAAC,aAAa,IACZ,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,EAC5B,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,EAC5B,aAAa,EAAE,cAA+B,EAC9C,SAAS,EAAE,yBAAyB,EACpC,cAAc,EAAE,yBAAyB,GACzC,EAEJ,eAAe,EAAE,oBAAC,SAAS,IAAC,IAAI,EAAC,GAAG,EAAC,QAAQ,EAAC,QAAQ,EAAC,KAAK,EAAE,MAAM,GAAI,EACxE,SAAS,EACP,oBAAC,SAAS,IACR,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,EACpB,KAAK,EAAC,MAAM,EACZ,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC,eAAe,mBAAmB,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EACjF,YAAY,EAAE,mBAAmB,CAAC,MAAM,EACxC,SAAS,EAAE,SAAS,EACpB,cAAc,EAAE,cAAc,EAC9B,eAAe,EAAE,eAAe,EAChC,mBAAmB,EAAE,wBAAwB,EAC7C,gBAAgB,EAAE,CAAC,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,eAAe,CAAA,KAAI,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,WAAW,CAAA,EACrF,gBAAgB,EAAE,kBAAkB,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,EACtF,wBAAwB,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EACjE,WAAW,EAAE,CAAC,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,eAAe,CAAA,EAC/C,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC,cAAc,EAC1C,UAAU,EAAE,KAAK,CAAC,QAAQ,CAAC,aAAa,EACxC,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,cAAc,EACtC,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,YAAY,EACtC,kBAAkB,EAAE,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EACrD,iBAAiB,EAAE,KAAK,CAAC,QAAQ,CAAC,iBAAiB;YAEnD,8BACE,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,EAC3B,EAAE,EAAC,GAAG,EACN,EAAE,EAAC,GAAG,EACN,EAAE,EAAC,GAAG,EACN,EAAE,EAAC,MAAM,EACT,MAAM,EAAC,aAAa,EACpB,WAAW,EAAE,CAAC,EACd,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE,GAChC;YAEF,oBAAC,iBAAiB,IAChB,SAAS,EAAE,KAAK,CAAC,KAAK,EACtB,UAAU,EAAE,KAAK,CAAC,MAAM,EACxB,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,EAC5B,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,EAC5B,aAAa,EAAE,cAAc,EAC7B,KAAK,EAAE,MAAM,EACb,mBAAmB,EAAE,wBAAwB,EAC7C,cAAc,EAAE,yBAAyB,GACzC;YAEF,oBAAC,cAAc,IAAC,KAAK,EAAE,KAAK,GAAI;YAEhC,oBAAC,cAAc,oBACT,mBAAmB,IACvB,KAAK,EAAE,KAAK,CAAC,KAAK,EAClB,MAAM,EAAE,KAAK,CAAC,MAAM,EACpB,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,EAC5B,KAAK,EAAE,MAAM,EACb,mBAAmB,EAAE,wBAAwB,EAC7C,UAAU,EAAE,sBAAsB,GAAG,uBAAuB,EAC5D,WAAW,EAAE,uBAAuB,EACpC,KAAK,EAAE,KAAK,IACZ;YAEF,oBAAC,kBAAkB,IAAC,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAI;YAE9F,oBAAC,kBAAkB,IAAC,KAAK,EAAE,KAAK,GAAI;YAEpC,oBAAC,oBAAoB,IAAC,GAAG,EAAE,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,WAAW,GAAI,CAChG,EAEd,OAAO,EACL,oBAAC,gBAAgB,IACf,KAAK,EAAE,KAAK,EACZ,gBAAgB,EAAE,gBAAgB,EAClC,gBAAgB,EAAE,6BAA6B,EAC/C,IAAI,EAAE,iBAAiB,EACvB,MAAM,EAAE,0BAA0B,EAClC,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,iBAAiB,GACxC,GAEJ,CACH,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport React, { memo, useEffect, useMemo, useRef, useState } from 'react';\n\nimport AxisLabel from '../internal/components/cartesian-chart/axis-label';\nimport BlockEndLabels, { useBLockEndLabels } from '../internal/components/cartesian-chart/block-end-labels';\nimport { CartesianChartContainer } from '../internal/components/cartesian-chart/chart-container';\nimport EmphasizedBaseline from '../internal/components/cartesian-chart/emphasized-baseline';\nimport InlineStartLabels from '../internal/components/cartesian-chart/inline-start-labels';\nimport LabelsMeasure from '../internal/components/cartesian-chart/labels-measure';\nimport ChartPlot from '../internal/components/chart-plot';\nimport { useMergeRefs } from '../internal/hooks/use-merge-refs';\nimport useContainerWidth from '../internal/utils/use-container-width';\nimport { useSelector } from './async-store';\nimport AreaChartPopover from './elements/chart-popover';\nimport AreaDataSeries from './elements/data-series';\nimport AreaHighlightedPoint from './elements/highlighted-point';\nimport useHighlightDetails from './elements/use-highlight-details';\nimport AreaVerticalMarker from './elements/vertical-marker';\nimport { AreaChartProps } from './interfaces';\nimport { ChartModel } from './model';\n\nconst DEFAULT_CHART_WIDTH = 500;\nconst INLINE_START_LABELS_MARGIN = 16;\nconst BLOCK_END_LABELS_OFFSET = 12;\n\ntype TickFormatter = undefined | ((value: AreaChartProps.DataTypes) => string);\n\ninterface ChartContainerProps<T extends AreaChartProps.DataTypes>\n  extends Pick<\n    AreaChartProps<T>,\n    | 'xTitle'\n    | 'yTitle'\n    | 'xTickFormatter'\n    | 'yTickFormatter'\n    | 'detailTotalFormatter'\n    | 'detailPopoverSize'\n    | 'detailPopoverFooter'\n    | 'ariaLabel'\n    | 'ariaLabelledby'\n    | 'ariaDescription'\n    | 'i18nStrings'\n  > {\n  model: ChartModel<T>;\n  autoWidth: (value: number) => void;\n  fitHeight?: boolean;\n  hasFilters: boolean;\n  minHeight: number;\n  isRTL?: boolean;\n}\n\nexport default memo(ChartContainer) as typeof ChartContainer;\n\nfunction ChartContainer<T extends AreaChartProps.DataTypes>({\n  model,\n  autoWidth,\n  xTitle,\n  yTitle,\n  detailPopoverSize,\n  detailPopoverFooter,\n  ariaLabel,\n  ariaLabelledby,\n  ariaDescription,\n  i18nStrings: {\n    xTickFormatter: deprecatedXTickFormatter,\n    yTickFormatter: deprecatedYTickFormatter,\n    detailTotalFormatter: deprecatedDetailTotalFormatter,\n    detailTotalLabel,\n    chartAriaRoleDescription,\n    xAxisAriaRoleDescription,\n    yAxisAriaRoleDescription,\n    detailPopoverDismissAriaLabel,\n  } = {},\n  fitHeight,\n  hasFilters,\n  minHeight,\n  xTickFormatter = deprecatedXTickFormatter,\n  yTickFormatter = deprecatedYTickFormatter,\n  detailTotalFormatter = deprecatedDetailTotalFormatter,\n  isRTL,\n}: ChartContainerProps<T>) {\n  const [inlineStartLabelsWidth, setInlineStartLabelsWidth] = useState(0);\n  const [containerWidth, containerWidthRef] = useContainerWidth(DEFAULT_CHART_WIDTH);\n  const maxInlineStartLabelsWidth = Math.round(containerWidth / 2);\n\n  const blockEndLabelsProps = useBLockEndLabels({\n    ticks: model.computed.xTicks,\n    scale: model.computed.xScale,\n    tickFormatter: xTickFormatter as TickFormatter,\n  });\n\n  // Calculate the width of the plot area and tell it to the parent.\n  const plotWidth = Math.max(0, containerWidth - inlineStartLabelsWidth - INLINE_START_LABELS_MARGIN);\n  useEffect(() => {\n    autoWidth(plotWidth);\n  }, [autoWidth, plotWidth]);\n\n  const highlightDetails = useHighlightDetails({\n    model,\n    xTickFormatter,\n    yTickFormatter,\n    detailTotalFormatter,\n    detailTotalLabel,\n  });\n\n  const highlightedPointRef = useRef<SVGGElement>(null);\n\n  const mergedRef = useMergeRefs(containerWidthRef, model.refs.container);\n\n  const isPointHighlighted = model.interactions.get().highlightedPoint !== null;\n\n  const highlightedX = useSelector(model.interactions, state => state.highlightedX);\n\n  const detailPopoverFooterContent = useMemo(\n    () => (detailPopoverFooter && highlightedX ? detailPopoverFooter(highlightedX[0].x) : null),\n    [detailPopoverFooter, highlightedX]\n  );\n\n  return (\n    <CartesianChartContainer\n      ref={mergedRef}\n      minHeight={minHeight + blockEndLabelsProps.height}\n      fitHeight={!!fitHeight}\n      hasFilters={hasFilters}\n      leftAxisLabel={<AxisLabel axis=\"y\" position=\"left\" title={yTitle} />}\n      leftAxisLabelMeasure={\n        <LabelsMeasure\n          scale={model.computed.yScale}\n          ticks={model.computed.yTicks}\n          tickFormatter={yTickFormatter as TickFormatter}\n          autoWidth={setInlineStartLabelsWidth}\n          maxLabelsWidth={maxInlineStartLabelsWidth}\n        />\n      }\n      bottomAxisLabel={<AxisLabel axis=\"x\" position=\"bottom\" title={xTitle} />}\n      chartPlot={\n        <ChartPlot\n          ref={model.refs.plot}\n          width=\"100%\"\n          height={fitHeight ? `calc(100% - ${blockEndLabelsProps.height}px)` : model.height}\n          offsetBottom={blockEndLabelsProps.height}\n          ariaLabel={ariaLabel}\n          ariaLabelledby={ariaLabelledby}\n          ariaDescription={ariaDescription}\n          ariaRoleDescription={chartAriaRoleDescription}\n          activeElementKey={!highlightDetails?.isPopoverPinned && highlightDetails?.activeLabel}\n          activeElementRef={isPointHighlighted ? highlightedPointRef : model.refs.verticalMarker}\n          activeElementFocusOffset={isPointHighlighted ? 3 : { x: 8, y: 0 }}\n          isClickable={!highlightDetails?.isPopoverPinned}\n          onMouseMove={model.handlers.onSVGMouseMove}\n          onMouseOut={model.handlers.onSVGMouseOut}\n          onClick={model.handlers.onSVGMouseDown}\n          onKeyDown={model.handlers.onSVGKeyDown}\n          onApplicationFocus={model.handlers.onApplicationFocus}\n          onApplicationBlur={model.handlers.onApplicationBlur}\n        >\n          <line\n            ref={model.refs.plotMeasure}\n            x1=\"0\"\n            x2=\"0\"\n            y1=\"0\"\n            y2=\"100%\"\n            stroke=\"transparent\"\n            strokeWidth={1}\n            style={{ pointerEvents: 'none' }}\n          />\n\n          <InlineStartLabels\n            plotWidth={model.width}\n            plotHeight={model.height}\n            scale={model.computed.yScale}\n            ticks={model.computed.yTicks}\n            tickFormatter={yTickFormatter}\n            title={yTitle}\n            ariaRoleDescription={yAxisAriaRoleDescription}\n            maxLabelsWidth={maxInlineStartLabelsWidth}\n          />\n\n          <AreaDataSeries model={model} />\n\n          <BlockEndLabels\n            {...blockEndLabelsProps}\n            width={model.width}\n            height={model.height}\n            scale={model.computed.xScale}\n            title={xTitle}\n            ariaRoleDescription={xAxisAriaRoleDescription}\n            offsetLeft={inlineStartLabelsWidth + BLOCK_END_LABELS_OFFSET}\n            offsetRight={BLOCK_END_LABELS_OFFSET}\n            isRTL={isRTL}\n          />\n\n          <EmphasizedBaseline width={model.width} height={model.height} scale={model.computed.yScale} />\n\n          <AreaVerticalMarker model={model} />\n\n          <AreaHighlightedPoint ref={highlightedPointRef} model={model} ariaLabel={highlightDetails?.activeLabel} />\n        </ChartPlot>\n      }\n      popover={\n        <AreaChartPopover\n          model={model}\n          highlightDetails={highlightDetails}\n          dismissAriaLabel={detailPopoverDismissAriaLabel}\n          size={detailPopoverSize}\n          footer={detailPopoverFooterContent}\n          onBlur={model.handlers.onApplicationBlur}\n        />\n      }\n    />\n  );\n}\n"]}