// src/App.jsx
import { useState, useEffect } from 'react';
import { AppLayout, TopNavigation } from "@cloudscape-design/components";
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { applyMode, Mode, applyDensity, Density } from '@cloudscape-design/global-styles';
import Navigation from "./components/Navigation";
import Content from "./components/Content";
import Tools from "./components/Tools";
import Footer from "./components/Footer";
import { routes } from './routes/routes';
import Introduction from './pages/Introduction';
import '@cloudscape-design/global-styles/index.css';
import logo from "./assets/images/logo.png";



function AppContent() {
  const [navigationOpen, setNavigationOpen] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(() => {
    return localStorage.getItem('theme') === 'dark';
  });
  const [isCompact, setIsCompact] = useState(() => {
    return localStorage.getItem('density') === 'compact';
  });

  useEffect(() => {
    applyMode(isDarkMode ? Mode.Dark : Mode.Light);
    localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');
  }, [isDarkMode]);

  useEffect(() => {
    applyDensity(isCompact ? Density.Compact : Density.Comfortable);
    localStorage.setItem('density', isCompact ? 'compact' : 'comfortable');
  }, [isCompact]);

  const handleThemeChange = () => {
    setIsDarkMode(prevMode => !prevMode);
  };

  const handleDensityChange = () => {
    setIsCompact(prevDensity => !prevDensity);
  };

  return (
    <BrowserRouter>
      <div style={{ 
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column"
      }}>
        <TopNavigation
          identity={{
            href: "/",
            logo: {
              src: logo,
              alt: "Amazonians Workshop Portal Logo",
              title: "Amazonians Workshop Portal"
            }
          }}
          utilities={[
            {
              type: "button",
              variant: "link",
              iconName: isCompact ? "view-full" : "view-vertical",
              text: isCompact ? "Comfortable" : "Compact",
              onClick: handleDensityChange,
              title: isCompact 
                ? "Switch to comfortable density with relaxed spacing" 
                : "Switch to compact density with tighter spacing"
            },
            {
              type: "button",
              variant: "link",
              iconName: isDarkMode ? "sun" : "moon",
              text: isDarkMode ? "Light Mode" : "Dark Mode",
              onClick: handleThemeChange,
              title: isDarkMode 
                ? "Switch to light color theme" 
                : "Switch to dark color theme"
            },
          
            {
              type: "menu-dropdown",
              text: "Account",
              description: "<EMAIL>",
              iconName: "user-profile",
              items: [
                { id: "profile", text: "Profile" },
                { id: "preferences", text: "Preferences" },
                { id: "security", text: "Security" },
                { id: "signout", text: "Sign out" }
              ]
            }
          ]}
          i18nStrings={{
            searchIconAriaLabel: "Search",
            searchDismissIconAriaLabel: "Close search",
            overflowMenuTriggerText: "More"
          }}
        />
        <div style={{ flex: 1 }}>
          <AppLayout
            navigation={<Navigation />}
            navigationOpen={navigationOpen}
            onNavigationChange={({ detail }) => setNavigationOpen(detail.open)}
            content={
              <Routes>
                <Route 
                  path="/" 
                  element={<Introduction />}
                />
                {routes.map((route) => (
                  <Route
                    key={route.id}
                    path={route.path}
                    element={<route.component />}
                  />
                ))}
              </Routes>
            }
            tools={<Tools />}
            toolsHide={false}
            contentType="default"
            headerSelector="#header"
            stickyNotifications
            notifications={<div id="notifications" />}
          />
        </div>
        <Footer />
      </div>
    </BrowserRouter>
  );
}

function App() {
  return <AppContent />;
}

export default App;
