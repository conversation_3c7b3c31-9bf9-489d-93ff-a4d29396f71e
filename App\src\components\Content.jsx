// src/components/Content.jsx
import { 
  Container, 
  Header, 
  ContentLayout, 
  BreadcrumbGroup,
  SpaceBetween,
  Box
} from "@cloudscape-design/components";
import { useLocation } from 'react-router-dom';

function Content({ title }) {
  const location = useLocation();

  const createBreadcrumbs = () => {
    const paths = location.pathname.split('/').filter(Boolean);
    return paths.map((path, index) => ({
      text: path.split('-').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' '),
      href: '/' + paths.slice(0, index + 1).join('/')
    }));
  };

  const breadcrumbs = [
    { text: 'Home', href: '/' },
    ...createBreadcrumbs()
  ];

  return (
    <ContentLayout
      header={
        <SpaceBetween size="m">
          <BreadcrumbGroup items={breadcrumbs} />
          <Header
            variant="h1"
            description={`Path: ${location.pathname}`}
            actions={
              <SpaceBetween direction="horizontal" size="xs">
                {/* Add your action buttons here */}
              </SpaceBetween>
            }
          >
            {title}
          </Header>
        </SpaceBetween>
      }
    >
      <SpaceBetween size="l">
        <Container>
          <Box padding={{ vertical: "l", horizontal: "l" }}>
            <SpaceBetween size="l">
              <div>Content for {location.pathname}</div>
            </SpaceBetween>
          </Box>
        </Container>
      </SpaceBetween>
    </ContentLayout>
  );
}

export default Content;
