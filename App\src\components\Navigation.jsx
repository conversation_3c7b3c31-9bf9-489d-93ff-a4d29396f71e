// src/components/Navigation.jsx
import { SideNavigation } from "@cloudscape-design/components";
import { useNavigate, useLocation } from 'react-router-dom';
import { useNavigation } from '../hooks/useNavigation';
import { useEffect } from 'react';

function Navigation() {
  const { 
    items, 
    loading, 
    error, 
    lastSelectedItem, 
    saveLastSelected 
  } = useNavigation();
  
  const navigate = useNavigate();
  const location = useLocation();

  // Navigate to last selected item on initial load
  useEffect(() => {
    if (lastSelectedItem && location.pathname === '/') {
      navigate(lastSelectedItem.href);
    }
  }, [lastSelectedItem, navigate, location.pathname]);

  const handleFollow = (event) => {
    event.preventDefault();
    
    // Find the selected item from the items array
    const findItem = (items, href) => {
      for (const item of items) {
        if (item.href === href) {
          return item;
        }
        if (item.items) {
          const found = findItem(item.items, href);
          if (found) return found;
        }
      }
      return null;
    };

    // Don't save or navigate if it's an external link
    const selectedItem = findItem(items, event.detail.href);
    if (selectedItem && !selectedItem.external) {
      saveLastSelected(selectedItem);
      navigate(event.detail.href);
    } else if (selectedItem && selectedItem.external) {
      window.open(event.detail.href, '_blank');
    }
  };

  if (loading) {
    return <div>Loading navigation...</div>;
  }

  if (error) {
    return <div>Error loading navigation: {error}</div>;
  }

  // Add divider and documentation link to the items
  const navigationItems = [
    ...items,
    {
      type: "divider"
    },
    {
      type: "link",
      text: "Documentation",
      href: "https://docs.aws.amazon.com",
      external: true
    }
  ];

  return (
    <SideNavigation
      activeHref={location.pathname}
      header={{ 
        href: "/",
        text: "Workshop Navigation" 
      }}
      items={navigationItems}
      onFollow={handleFollow}
    />
  );
}

export default Navigation;
