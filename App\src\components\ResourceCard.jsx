// src/components/ResourceCard.jsx
import {
    Box,
    SpaceBetween,
    StatusIndicator,
    Popover,
    Button,
    Link
} from "@cloudscape-design/components";

function ResourceCard({ item, handleCardAction, showMetrics, ResourceMetrics, getStatusType }) {
    return (
        <Box>
            <Link href="#" onFollow={() => handleCardAction('view', item)}>
                {item.name}
            </Link>
            <Box margin={{ top: "s" }}>
                <SpaceBetween size="xs">
                    <div>Type: {item.type}</div>
                    <div>Description: {item.description}</div>
                    <div>
                        Status:
                        <StatusIndicator type={getStatusType(item.status)}>
                            {item.status}
                        </StatusIndicator>
                    </div>
                    <div>Region: {item.region}</div>
                    <div>Tags: {item.tags.join(", ")}</div>
                    <div>
                        Cost:
                        <Popover
                            content={
                                <Box padding="s">
                                    <SpaceBetween size="s">
                                        <div>Last 30 days average</div>
                                        <div>Projected: {item.cost}</div>
                                    </SpaceBetween>
                                </Box>
                            }
                        >
                            {item.cost}
                        </Popover>
                    </div>
                    {showMetrics && <ResourceMetrics metrics={item.metrics} />}
                    <div>
                        <SpaceBetween size="xs" direction="horizontal">
                            <Button onClick={() => handleCardAction('view', item)}>
                                View
                            </Button>
                            <Button onClick={() => handleCardAction('edit', item)}>
                                Edit
                            </Button>
                            <Button onClick={() => handleCardAction('delete', item)}>
                                Delete
                            </Button>
                        </SpaceBetween>
                    </div>
                </SpaceBetween>
            </Box>
        </Box>
    );
}

export default ResourceCard;
