// src/components/ResourceCardActions.jsx
import React from 'react';
import { Button, SpaceBetween } from "@cloudscape-design/components";

// Define props interface (if using TypeScript) or use PropTypes
// interface ResourceCardActionsProps {
//   item: any; // Replace with specific type
//   onAction: (action: 'view' | 'edit' | 'delete', item: any) => void;
// }

function ResourceCardActions({ item, onAction }) {
    return (
        <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}> {/* Add flexbox container */}
            <div style={{ flex: 1 }} /> {/* Push buttons to bottom */}
            <SpaceBetween size="xs" direction="horizontal">
                <Button
                    onClick={() => onAction('view', item)}
                    ariaLabel={`View details for ${item.name}`}
                >
                    View
                </Button>
                <Button
                    onClick={() => onAction('edit', item)}
                    ariaLabel={`Edit resource ${item.name}`}
                >
                    Edit
                </Button>
                <Button
                    onClick={() => onAction('delete', item)}
                    ariaLabel={`Delete resource ${item.name}`}
                >
                    Delete
                </Button>
            </SpaceBetween>
        </div>
    );
}

export default ResourceCardActions;