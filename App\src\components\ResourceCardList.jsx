// src/components/ResourceCardList.jsx
import React from 'react';
import {
    Cards,
    Box,
    Button,
    Header,
    Link,
    SpaceBetween,
    StatusIndicator,
    Pagination,
    CollectionPreferences,
    Popover,
} from "@cloudscape-design/components";
import ResourceMetrics from './ResourceMetrics';
import ResourceCardActions from './ResourceCardActions';
import { getStatusIndicatorType, formatCurrency } from '../utils/helpers';
import { PAGE_SIZE_OPTIONS, VISIBLE_CONTENT_OPTIONS } from '../config/constants';

const ResourceCardList = ({
    items,
    loading = false,
    preferences,
    onPreferencesChange,
    pagination,
    onPaginationChange,
    totalPages,
    totalFilteredCount,
    showMetrics,
    onAction,
    onSelectionChange = () => {}, // Default no-op
}) => {
    const { currentPage, pageSize } = pagination;
    const { visibleContent, wrapLines } = preferences;

    const cardDefinition = {
        header: (item) => (
            <Link href="#" onFollow={() => onAction('view', item)} ariaLabel={`View details for ${item.name}`}>
                {item.name}
            </Link>
        ),
        sections: [
            { id: "type", header: "Type", content: (item) => item.type, isVisible: visibleContent.includes("type") },
            {
                id: "description",
                header: "Description",
                content: (item) => item.description,
                isVisible: visibleContent.includes("description"),
            },
            {
                id: "status",
                header: "Status",
                content: (item) => (
                    <StatusIndicator type={getStatusIndicatorType(item.status)}>{item.status}</StatusIndicator>
                ),
                isVisible: visibleContent.includes("status"),
            },
            { id: "region", header: "Region", content: (item) => item.region, isVisible: visibleContent.includes("region") },
            {
                id: "tags",
                header: "Tags",
                content: (item) => item.tags?.join(", ") || '-',
                isVisible: visibleContent.includes("tags"),
            },
            { id: "owner", header: "Owner", content: (item) => item.owner || '-', isVisible: visibleContent.includes("owner") },
            {
                id: "created",
                header: "Created",
                content: (item) => new Date(item.created).toLocaleDateString(),
                isVisible: visibleContent.includes("created"),
            },
            {
                id: "lastModified",
                header: "Last modified",
                content: (item) => new Date(item.lastModified).toLocaleDateString(),
                isVisible: visibleContent.includes("lastModified"),
            },
            { id: "metrics", header: "Metrics", content: (item) => showMetrics && <ResourceMetrics metrics={item.metrics} />, isVisible: showMetrics },
            { id: "actions", header: "Actions", content: (item) => <ResourceCardActions item={item} onAction={onAction} />, isVisible: true },
        ].filter((section) => section.isVisible),
    };
    

    const headerActions = (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <SpaceBetween size="xs" direction="horizontal">
                <Button onClick={() => onAction('create-new', null)}>Create resource</Button>
                <Button variant="primary">Get started</Button>
            </SpaceBetween>
            <SpaceBetween size="xs" direction="horizontal">
                <Pagination
                    currentPageIndex={currentPage}
                    onChange={({ detail }) => onPaginationChange(detail.currentPageIndex)}
                    pagesCount={totalPages}
                    ariaLabels={{
                        nextPageLabel: "Next page",
                        previousPageLabel: "Previous page",
                        pageLabel: (pageNumber) => `Page ${pageNumber} of ${totalPages || 1}`,
                    }}
                    disabled={loading}
                />
                <span style={{ margin: '0 1rem', borderLeft: '1px solid #ccc' }} ></span>
                <CollectionPreferences
                    title="Preferences"
                    confirmLabel="Confirm"
                    cancelLabel="Cancel"
                    preferences={preferences}
                    onConfirm={({ detail }) => onPreferencesChange(detail)}
                    pageSizePreference={{ title: "Page size", options: PAGE_SIZE_OPTIONS }}
                    visibleContentPreference={{ title: "Select visible card sections", options: VISIBLE_CONTENT_OPTIONS }}
                    wrapLinesPreference={{ label: "Wrap lines", description: "Check to see all the text and wrap the lines" }}
                />
            </SpaceBetween>
        </div>
    );

    const emptyState = (
        <Box textAlign="center" color="inherit" padding={{ vertical: 'xxl' }}>
            <SpaceBetween size="m">
                <b>No resources found</b>
                <Button onClick={() => onAction('create-new', null)}>Create resource</Button>
            </SpaceBetween>
        </Box>
    );

    return (
        <>
           
                <Header counter={!loading ? `(${totalFilteredCount})` : undefined} actions={headerActions}>
                    Resources
                </Header>
           
            <Box margin={{ top: 'l' }}> {/* Add a top margin */}
                <Cards
                    items={items}
                    loading={loading}
                    loadingText="Loading resources..."
                    cardDefinition={cardDefinition}
                    cardsPerRow={[
                        { cards: 1 },
                        { cards: 2, minWidth: 500 },
                        { cards: 3, minWidth: 992 },
                    ]}
                    trackBy="id"
                    visibleSections={cardDefinition.sections.map((s) => s.id)}
                    wrapLines={wrapLines}
                    ariaLabels={{ itemSelectionLabel: (e, n) => `select ${n.name}`, selectionGroupLabel: "Item selection" }}
                    empty={emptyState}
                />
            </Box>
        </>
    );
};

export default ResourceCardList;
