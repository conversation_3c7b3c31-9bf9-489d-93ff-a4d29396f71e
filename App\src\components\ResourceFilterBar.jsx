import React from 'react';
import {
    Input,
    Select,
    FormField,
    ColumnLayout,
    Toggle,
    SpaceBetween
} from "@cloudscape-design/components";
import { SORTABLE_FIELDS } from '../config/constants';

function ResourceFilterBar({
    filters,
    onFilterChange,
    sorting,
    onSortChange,
    showMetrics,
    onShowMetricsChange,
    filterOptions
}) {
    const { searchQuery, selectedType, selectedRegion, selectedStatus } = filters;
    const { sortBy, sortAscending } = sorting;

    return (
        <div style={{ display: 'flex', flexDirection: 'column' }}>
            <ColumnLayout columns={4} direction="horizontal">
                <FormField label="Search">
                    <Input
                        value={searchQuery}
                        onChange={({ detail }) => onFilterChange('searchQuery', detail.value)}
                        placeholder="Search resources..."
                        ariaLabel="Search resources by name, description, or tags"
                    />
                </FormField>
                <FormField label="Resource type">
                    <Select
                        selectedOption={selectedType}
                        onChange={({ detail }) => onFilterChange('selectedType', detail.selectedOption)}
                        options={filterOptions.types}
                        ariaLabel="Filter by resource type"
                    />
                </FormField>
                <FormField label="Region">
                    <Select
                        selectedOption={selectedRegion}
                        onChange={({ detail }) => onFilterChange('selectedRegion', detail.selectedOption)}
                        options={filterOptions.regions}
                        ariaLabel="Filter by AWS region"
                    />
                </FormField>
                <FormField label="Status">
                    <Select
                        selectedOption={selectedStatus}
                        onChange={({ detail }) => onFilterChange('selectedStatus', detail.selectedOption)}
                        options={filterOptions.statuses}
                        ariaLabel="Filter by resource status"
                    />
                </FormField>
            </ColumnLayout>
            <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '1rem' }}>
                <SpaceBetween size="s" direction="vertical">
                    <Toggle
                        onChange={({ detail }) => onSortChange({ ascending: detail.checked })}
                        checked={sortAscending}
                        ariaLabel={`Sort order: ${sortAscending ? 'Ascending' : 'Descending'}`}
                    >
                        Sort ascending
                    </Toggle>
                    <Toggle
                        onChange={({ detail }) => onShowMetricsChange(detail.checked)}
                        checked={showMetrics}
                        ariaLabel="Toggle visibility of resource metrics charts"
                    >
                        Show resource metrics
                    </Toggle>
                </SpaceBetween>
            </div>
        </div>
    );
}

export default ResourceFilterBar;
