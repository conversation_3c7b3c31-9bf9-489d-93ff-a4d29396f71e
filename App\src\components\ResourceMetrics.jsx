import { Bar<PERSON><PERSON>, Box } from "@cloudscape-design/components";
import PropTypes from 'prop-types';
import { useMemo } from 'react'; // Import useMemo here

// Default values and scaling factors
const DEFAULT_METRICS = { 
  cpu: 0, 
  memory: 0, 
  network: 0, 
  storage: 0 
};

const SCALE_FACTORS = {
  cpu: 1,
  memory: 1,
  network: 10,    // Assuming max network is 1000 (1000/10 = 100)
  storage: 5      // Assuming max storage is 500 (500/5 = 100)
};

const METRIC_LABELS = {
  cpu: "CPU Usage",
  memory: "Memory Usage",
  network: "Network Throughput",
  storage: "Storage Utilization"
};

function ResourceMetrics({ 
  metrics = DEFAULT_METRICS,
  height = 100,
  showLegend = false,
  showValues = true,
  theme = 'light'
}) {
  // Validate metrics input
  const validatedMetrics = useMemo(() => {
    const result = { ...DEFAULT_METRICS };
    for (const key in DEFAULT_METRICS) {
      const value = metrics[key];
      result[key] = typeof value === 'number' && !isNaN(value) ? value : 0;
    }
    return result;
  }, [metrics]);

  // Normalize data with scaling
  const chartData = useMemo(() => {
    return Object.entries(validatedMetrics).map(([key, value]) => ({
      x: METRIC_LABELS[key] || key.toUpperCase(),
      y: Math.min(100, Math.max(0, value / SCALE_FACTORS[key]))
    }));
  }, [validatedMetrics]);

  // Handle empty or zero metrics
  const isEmpty = useMemo(() => {
    return Object.values(validatedMetrics).every(val => val === 0);
  }, [validatedMetrics]);

  if (isEmpty) {
    return (
      <Box textAlign="center" color="text-status-inactive">
        No metrics data available
      </Box>
    );
  }

  return (
    <BarChart
      series={[{
        title: "Resource Utilization",
        type: "bar",
        data: chartData,
        valueFormatter: value => `${value.toFixed(1)}%`
      }]}
      height={height}
      hideFilter
      hideLegend={!showLegend}
      xScaleType="categorical"
      yDomain={[0, 100]}
      ariaLabel="Resource utilization metrics"
      ariaDescription={`Showing ${chartData.map(d => d.x).join(', ')} utilization`}
      i18nStrings={{
        filterLabel: "Filter metrics",
        filterPlaceholder: "Filter metrics",
        legendAriaLabel: "Legend",
        chartAriaRoleDescription: "bar chart",
        xAxisAriaRoleDescription: "x axis",
        yAxisAriaRoleDescription: "y axis"
      }}
      noMatch={
        <Box textAlign="center" color="text-status-inactive">
          No metrics data available
        </Box>
      }
      detailPopoverContent={(datum, sum) => [
        { key: "Actual value", value: `${validatedMetrics[datum.x.toLowerCase().split(' ')[0]]}` },
        { key: "Percentage", value: `${datum.y.toFixed(1)}%` }
      ]}
    />
  );
}

ResourceMetrics.propTypes = {
  metrics: PropTypes.shape({
    cpu: PropTypes.number,
    memory: PropTypes.number,
    network: PropTypes.number,
    storage: PropTypes.number
  }),
  height: PropTypes.number,
  showLegend: PropTypes.bool,
  showValues: PropTypes.bool,
  theme: PropTypes.oneOf(['light', 'dark'])
};

export default ResourceMetrics;
