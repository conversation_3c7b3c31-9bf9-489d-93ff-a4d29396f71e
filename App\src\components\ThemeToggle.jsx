// src/components/ThemeToggle.jsx
import { Button } from "@cloudscape-design/components";
import { useContext } from 'react';
import { ThemeContext } from '../context/ThemeContext';

function ThemeToggle() {
  const { isDarkMode, toggleTheme } = useContext(ThemeContext);

  return (
    <Button
      variant="icon"
      iconName={isDarkMode ? "sun" : "moon"}
      onClick={toggleTheme}
      ariaLabel={isDarkMode ? "Switch to light mode" : "Switch to dark mode"}
    />
  );
}

export default ThemeToggle;
