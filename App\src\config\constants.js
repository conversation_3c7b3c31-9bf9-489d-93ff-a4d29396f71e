// src/config/constants.js

// Remove the import statement
// import {
//     ValueLabelOption,
//     VisibleContentOptionsGroup,
//     SortableFieldOption,
//     ResourceItem // Import ResourceItem to use its keys
// } from '../types';

export const DEFAULT_PAGE_SIZE = 20;
export const DEFAULT_VISIBLE_CONTENT = [
    "type", "description", "status", "region", "tags", "cost", "created"
];

export const PAGE_SIZE_OPTIONS = [
    { value: 10, label: "10 resources" },
    { value: 20, label: "20 resources" },
    { value: 50, label: "50 resources" }
];

export const VISIBLE_CONTENT_OPTIONS = [
    {
        label: "Resource properties",
        options: [
            { id: "type", label: "Type" },
            { id: "description", label: "Description" },
            { id: "status", label: "Status" },
            { id: "region", label: "Region" },
            { id: "tags", label: "Tags" },
            { id: "cost", label: "Monthly cost" },
            { id: "owner", label: "Owner" },
            { id: "created", label: "Created" },
            { id: "lastModified", label: "Last modified" }
        ]
    }
];

export const SORTABLE_FIELDS = [
    { value: "created", label: "Creation date" },
    { value: "name", label: "Name" },
    { value: "type", label: "Type" },
    { value: "status", label: "Status" },
    { value: "cost", label: "Cost" }
];

export const DEFAULT_SORT_STATE = {
    field: "created",
    ascending: false,
};

export const DEFAULT_FILTER_TYPE_OPTION = { value: "all", label: "All types" };
export const DEFAULT_FILTER_REGION_OPTION = { value: "all", label: "All regions" };
export const DEFAULT_FILTER_STATUS_OPTION = { value: "all", label: "All statuses" };
