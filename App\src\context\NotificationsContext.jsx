// src/context/NotificationsContext.jsx
import { createContext, useContext, useState } from 'react';
import { Flashbar } from '@cloudscape-design/components';

const NotificationsContext = createContext();

export function NotificationsProvider({ children }) {
  const [notifications, setNotifications] = useState([]);

  const addNotification = (notification) => {
    const id = Date.now().toString();
    setNotifications(prev => [...prev, { id, ...notification }]);
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  return (
    <NotificationsContext.Provider value={{ addNotification, removeNotification }}>
      {children}
      <div id="notifications">
        <Flashbar items={notifications} onDismiss={({ detail }) => removeNotification(detail.id)} />
      </div>
    </NotificationsContext.Provider>
  );
}

export const useNotifications = () => useContext(NotificationsContext);
