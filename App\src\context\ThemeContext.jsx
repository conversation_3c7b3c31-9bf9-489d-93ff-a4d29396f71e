// src/context/ThemeContext.jsx
import { createContext, useState, useEffect } from 'react';
import { applyMode, Mode } from '@cloudscape-design/global-styles';

export const ThemeContext = createContext(null);

export function ThemeProvider({ children }) {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    const savedTheme = localStorage.getItem('theme');
    return savedTheme === 'dark';
  });

  useEffect(() => {
    try {
      applyMode(isDarkMode ? Mode.Dark : Mode.Light);
      localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');
      console.log('Theme changed to:', isDarkMode ? 'dark' : 'light');
    } catch (error) {
      console.error('Error applying theme:', error);
    }
  }, [isDarkMode]);

  const toggleTheme = () => {
    setIsDarkMode(prev => !prev);
  };

  return (
    <ThemeContext.Provider value={{ 
      isDarkMode, 
      toggleTheme
    }}>
      {children}
    </ThemeContext.Provider>
  );
}
