// src/hooks/useNavigation.js
import { useState, useEffect } from 'react';

const LAST_SELECTED_MENU = 'last_selected_menu';

export function useNavigation() {
  const [items, setItems] = useState([]);
  const [routes, setRoutes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastSelectedItem, setLastSelectedItem] = useState(() => {
    // Initialize from localStorage
    const saved = localStorage.getItem(LAST_SELECTED_MENU);
    return saved ? JSON.parse(saved) : null;
  });

  function transformNavigationData(data) {
    const items = typeof data.body === 'string' ? JSON.parse(data.body) : data.body;
    
    // First, let's separate the Documentation item
    const documentationItem = items.find(item => item.text === "Documentation");
    const regularItems = items.filter(item => item.text !== "Documentation");
    
    const buildNavigationTree = (parentId = null) => {
      const children = regularItems
        .filter(item => item.parentId === parentId)
        .sort((a, b) => a.orderIndex - b.orderIndex);
  
      // Create the main navigation items
      let navigationItems = children.map(item => {
        const subItems = buildNavigationTree(item.id);
        
        if (subItems.length > 0) {
          return {
            type: "expandable-link-group",
            text: item.text,
            href: item.href,
            id: item.id,
            items: subItems
          };
        }
        
        return {
          type: "link",
          text: item.text,
          href: item.href,
          id: item.id
        };
      });
  
      return navigationItems;
    };
  
    // Build the main navigation tree
    let navigationItems = buildNavigationTree(null);
  
    // Add divider and Documentation link at the very end
    if (documentationItem) {
      navigationItems.push(
        {
          type: "divider"
        },
        {
          type: "link",
          text: documentationItem.text,
          href: documentationItem.href,
          id: documentationItem.id
        }
      );
    }
  
    const extractRoutes = (items) => {
      return items.reduce((acc, item) => {
        acc.push({
          path: item.href,
          title: item.text,
          id: item.id
        });
        return acc;
      }, []);
    };
  
    return {
      navigationItems: navigationItems,
      routes: extractRoutes(items)
    };
  }

  // Function to find parent IDs of an item
  const findParentIds = (items, targetId) => {
    const parentIds = [];
    
    const findParent = (items, targetId) => {
      for (const item of items) {
        if (item.items) {
          if (item.items.some(subItem => subItem.id === targetId)) {
            parentIds.push(item.id);
          }
          findParent(item.items, targetId);
        }
      }
    };

    findParent(items, targetId);
    return parentIds;
  };

  // Save last selected item
  const saveLastSelected = (item) => {
    setLastSelectedItem(item);
    localStorage.setItem(LAST_SELECTED_MENU, JSON.stringify(item));
  };

  useEffect(() => {
    const fetchNavigation = async () => {
      try {
        setLoading(true);
        const response = await fetch('https://9dn7gxcgyf.execute-api.us-east-1.amazonaws.com/dev/Navigation');
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        const data = await response.json();
        const { navigationItems, routes } = transformNavigationData(data);
        setItems(navigationItems);
        setRoutes(routes);
      } catch (err) {
        setError(err.message);
        console.error('Error fetching navigation:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchNavigation();
  }, []);

  return { 
    items, 
    routes, 
    loading, 
    error, 
    lastSelectedItem,
    saveLastSelected,
    findParentIds 
  };
}
