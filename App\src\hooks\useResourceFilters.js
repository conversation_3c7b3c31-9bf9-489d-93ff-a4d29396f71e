// src/hooks/useResourceFilters.js
import { useState, useMemo, useCallback } from 'react';
import { getUniqueValues } from '../utils/mockData';

// Default filter options
const DEFAULT_FILTERS = {
  searchQuery: '',
  selectedType: { value: 'all', label: 'All types' },
  selectedRegion: { value: 'all', label: 'All regions' },
  selectedStatus: { value: 'all', label: 'All statuses' },
  dateRange: null
};

export function useResourceFilters(allItems = []) {
  // State management
  const [searchQuery, setSearchQuery] = useState(DEFAULT_FILTERS.searchQuery);
  const [selectedType, setSelectedType] = useState(DEFAULT_FILTERS.selectedType);
  const [selectedRegion, setSelectedRegion] = useState(DEFAULT_FILTERS.selectedRegion);
  const [selectedStatus, setSelectedStatus] = useState(DEFAULT_FILTERS.selectedStatus);
  const [dateRange, setDateRange] = useState(DEFAULT_FILTERS.dateRange);

  // Memoized filter options
  const filterOptions = useMemo(() => {
    try {
      const types = getUniqueValues(allItems, 'type') || [];
      const regions = getUniqueValues(allItems, 'region') || [];
      const statuses = getUniqueValues(allItems, 'status') || [];

      return {
        types: [DEFAULT_FILTERS.selectedType, ...types.map(t => ({ value: t, label: t }))],
        regions: [DEFAULT_FILTERS.selectedRegion, ...regions.map(r => ({ value: r, label: r }))],
        statuses: [DEFAULT_FILTERS.selectedStatus, ...statuses.map(s => ({ value: s, label: s }))]
      };
    } catch (error) {
      console.error('Error generating filter options:', error);
      return {
        types: [DEFAULT_FILTERS.selectedType],
        regions: [DEFAULT_FILTERS.selectedRegion],
        statuses: [DEFAULT_FILTERS.selectedStatus]
      };
    }
  }, [allItems]);

  // Reset all filters to default
  const resetFilters = useCallback(() => {
    setSearchQuery(DEFAULT_FILTERS.searchQuery);
    setSelectedType(DEFAULT_FILTERS.selectedType);
    setSelectedRegion(DEFAULT_FILTERS.selectedRegion);
    setSelectedStatus(DEFAULT_FILTERS.selectedStatus);
    setDateRange(DEFAULT_FILTERS.dateRange);
  }, []);

  // Derived active filter states
  const activeFilters = useMemo(() => ({
    search: searchQuery.trim().length > 0,
    type: selectedType.value !== 'all',
    region: selectedRegion.value !== 'all',
    status: selectedStatus.value !== 'all',
    date: dateRange !== null,
    anyActive: function() {
      return this.search || this.type || this.region || this.status || this.date;
    }
  }), [searchQuery, selectedType, selectedRegion, selectedStatus, dateRange]);

  // Return filter state and controls
  return {
    // State values
    searchQuery,
    selectedType,
    selectedRegion,
    selectedStatus,
    dateRange,
    filterOptions,
    activeFilters,
    
    // State setters
    setSearchQuery,
    setSelectedType,
    setSelectedRegion,
    setSelectedStatus,
    setDateRange,
    
    // Actions
    resetFilters,
    
    // Convenience methods
    getFilters: () => ({
      searchQuery,
      type: selectedType.value !== 'all' ? selectedType.value : null,
      region: selectedRegion.value !== 'all' ? selectedRegion.value : null,
      status: selectedStatus.value !== 'all' ? selectedStatus.value : null,
      dateRange
    })
  };
}