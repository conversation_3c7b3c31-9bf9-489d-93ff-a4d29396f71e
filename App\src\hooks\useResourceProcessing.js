// src/hooks/useResourceProcessing.js
import { useMemo } from 'react';

// Helper function for safe date comparison
const safeDateCompare = (dateString) => {
  try {
    return new Date(dateString).getTime();
  } catch {
    return 0; // Fallback for invalid dates
  }
};

// Helper function for text search
const matchesSearch = (item, searchTerm) => {
  const searchFields = [
    item.name?.toLowerCase(),
    item.description?.toLowerCase(),
    ...(item.tags || []).map(tag => tag.toLowerCase())
  ];
  return searchFields.some(field => field?.includes(searchTerm));
};

export function useResourceProcessing(
  allItems = [],
  filters = {},
  sortBy = { field: 'created', ascending: false },
  pagination = { currentPage: 1, pageSize: 20 }
) {
  // Destructure with defaults
  const {
    searchQuery = '',
    selectedType = { value: 'all' },
    selectedRegion = { value: 'all' },
    selectedStatus = { value: 'all' },
    dateRange = null
  } = filters;

  const { currentPage = 1, pageSize = 20 } = pagination;

  // Memoized filtered items
  const filteredItems = useMemo(() => {
    if (!Array.isArray(allItems)) {
      console.error('Invalid items array provided');
      return [];
    }

    const searchTerm = searchQuery.toLowerCase();
    const typeFilter = selectedType.value;
    const regionFilter = selectedRegion.value;
    const statusFilter = selectedStatus.value;

    return allItems.filter(item => {
      // Search filter
      if (searchTerm && !matchesSearch(item, searchTerm)) {
        return false;
      }

      // Type filter
      if (typeFilter !== 'all' && item.type !== typeFilter) {
        return false;
      }

      // Region filter
      if (regionFilter !== 'all' && item.region !== regionFilter) {
        return false;
      }

      // Status filter
      if (statusFilter !== 'all' && item.status !== statusFilter) {
        return false;
      }

      // Date range filter
      if (dateRange?.type === 'absolute' && dateRange.startDate && dateRange.endDate) {
        const itemDate = safeDateCompare(item.created);
        const startDate = safeDateCompare(dateRange.startDate);
        const endDate = safeDateCompare(dateRange.endDate) + 86400000; // Add 1 day for inclusive range

        if (itemDate < startDate || itemDate > endDate) {
          return false;
        }
      }

      return true;
    });
  }, [allItems, searchQuery, selectedType, selectedRegion, selectedStatus, dateRange]);

  // Memoized sorted items
  const sortedItems = useMemo(() => {
    if (!filteredItems.length) return filteredItems;

    const sortField = sortBy.field;
    const sortDirection = sortBy.ascending ? 1 : -1;

    return [...filteredItems].sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Handle different field types
      if (['created', 'lastModified'].includes(sortField)) {
        aValue = safeDateCompare(aValue);
        bValue = safeDateCompare(bValue);
      } else if (sortField === 'cost') {
        aValue = Number(aValue) || 0;
        bValue = Number(bValue) || 0;
      } else if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      return (aValue > bValue ? 1 : -1) * sortDirection;
    });
  }, [filteredItems, sortBy]);

  // Memoized pagination
  const paginatedItems = useMemo(() => {
    const startIndex = (Math.max(1, currentPage) - 1) * pageSize;
    return sortedItems.slice(startIndex, startIndex + Math.max(1, pageSize));
  }, [sortedItems, currentPage, pageSize]);

  // Calculate total pages
  const totalPages = useMemo(() => {
    return Math.max(1, Math.ceil(sortedItems.length / Math.max(1, pageSize)));
  }, [sortedItems.length, pageSize]);

  return {
    filteredItems: sortedItems,
    paginatedItems,
    totalPages,
    totalFilteredCount: sortedItems.length,
    isEmpty: sortedItems.length === 0
  };
}