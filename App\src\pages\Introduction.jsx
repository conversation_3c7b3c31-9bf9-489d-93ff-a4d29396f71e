// src/pages/Introduction.jsx
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import {
    Box,
    SpaceBetween,
    TextContent
} from "@cloudscape-design/components";

// Data and Utils
import { generateItems } from '../utils/mockData';
import { DEFAULT_PAGE_SIZE, DEFAULT_VISIBLE_CONTENT } from '../config/constants';

// Hooks
import { useResourceFilters } from '../hooks/useResourceFilters';
import { useResourceProcessing } from '../hooks/useResourceProcessing';

// Components
import ResourceFilterBar from '../components/ResourceFilterBar';
import ResourceCardList from '../components/ResourceCardList';

// Generate mock data once
const ALL_ITEMS = generateItems(150);

function Introduction() {
    // --- State Management ---

    // Raw Data (fetched or generated once)
    const [allItems] = useState(ALL_ITEMS); // In real app, replace with data fetching hook

    // UI Preferences State
    const [preferences, setPreferences] = useState({
        pageSize: DEFAULT_PAGE_SIZE,
        visibleContent: DEFAULT_VISIBLE_CONTENT,
        wrapLines: true
    });
    const [showMetrics, setShowMetrics] = useState(false);

    // Pagination State
    const [currentPage, setCurrentPage] = useState(1);

    // Sorting State
    const [sortBy, setSortBy] = useState({ field: "created", ascending: false });

    // Filtering State (using custom hook)
    const { filterOptions, ...filterProps } = useResourceFilters(allItems);

    // --- Derived State & Logic (using custom hook) ---
    const {
        filteredItems, // Use this if needed elsewhere, e.g., for charts outside the list
        paginatedItems,
        totalPages,
        totalFilteredCount,
    } = useResourceProcessing(
        allItems,
        { // Pass filter state values
            searchQuery: filterProps.searchQuery,
            selectedType: filterProps.selectedType,
            selectedRegion: filterProps.selectedRegion,
            selectedStatus: filterProps.selectedStatus,
            dateRange: filterProps.dateRange,
        },
        sortBy,
        { currentPage, pageSize: preferences.pageSize }
    );

    // --- Event Handlers ---

    // Reset page to 1 when filters, sorting, or page size change
    useEffect(() => {
        setCurrentPage(1);
    }, [
        filterProps.searchQuery,
        filterProps.selectedType,
        filterProps.selectedRegion,
        filterProps.selectedStatus,
        filterProps.dateRange,
        sortBy,
        preferences.pageSize
    ]);

    const handleFilterChange = useCallback((filterName, value) => {
        // Update specific filter state using setters from useResourceFilters hook
        const setterName = `set${filterName.charAt(0).toUpperCase() + filterName.slice(1)}`;
        if (filterProps[setterName]) {
            filterProps[setterName](value);
        } else {
            console.warn(`No setter found for filter: ${filterName}`);
        }
    }, [filterProps]); // Dependency array includes the whole filterProps object

    const handleSortChange = useCallback((sortDetail) => {
        setSortBy(prev => ({ ...prev, ...sortDetail }));
    }, []);

    const handlePreferencesChange = useCallback((detail) => {
        // Ensure visibleContent is always an array
        const newVisibleContent = Array.isArray(detail.visibleContent)
            ? detail.visibleContent
            : DEFAULT_VISIBLE_CONTENT;

        setPreferences({
            pageSize: detail.pageSize || DEFAULT_PAGE_SIZE,
            visibleContent: newVisibleContent,
            wrapLines: detail.wrapLines === undefined ? true : detail.wrapLines,
        });
    }, []);

    const handleCardAction = useCallback((action, item) => {
        // Keep original actions or implement real logic
        switch (action) {
            case 'view':
                console.log('Viewing details for:', item?.name);
                // Navigate to detail page, open modal, etc.
                break;
            case 'edit':
                console.log('Editing:', item?.name);
                // Navigate to edit form, open modal, etc.
                break;
            case 'delete':
                console.log('Deleting:', item?.name);
                // Show confirmation modal, call API, etc.
                break;
            case 'create-new':
                console.log('Create new resource action triggered');
                // Navigate to creation form, open modal, etc.
                break;
            default:
                console.warn('Unknown action:', action);
                break;
        }
    }, []);

    // --- Rendering ---
    return (
        <Box padding={{ horizontal: "l", vertical: "l" }}>
            <SpaceBetween size="l">
                <TextContent>
                    <h1>AWS Resource Management</h1>
                    <p>
                        View and manage your AWS resources. Monitor metrics and manage resource lifecycle.
                    </p>
                </TextContent>

                {/* Filter Bar Component */}
                <ResourceFilterBar
                    filters={{ // Pass filter state
                        searchQuery: filterProps.searchQuery,
                        selectedType: filterProps.selectedType,
                        selectedRegion: filterProps.selectedRegion,
                        selectedStatus: filterProps.selectedStatus,
                        dateRange: filterProps.dateRange,
                    }}
                    onFilterChange={handleFilterChange}
                    sorting={{ sortBy, sortAscending: sortBy.ascending }}
                    onSortChange={handleSortChange}
                    showMetrics={showMetrics}
                    onShowMetricsChange={setShowMetrics}
                    filterOptions={filterOptions}
                />

                {/* Resource List Component */}
                <ResourceCardList
                    items={paginatedItems}
                    // loading={isLoading} // Pass loading state if data fetching is async
                    preferences={preferences}
                    onPreferencesChange={handlePreferencesChange}
                    pagination={{ currentPage, pageSize: preferences.pageSize }}
                    onPaginationChange={setCurrentPage}
                    totalPages={totalPages}
                    totalFilteredCount={totalFilteredCount}
                    showMetrics={showMetrics}
                    onAction={handleCardAction}
                    // onSelectionChange={handleSelectionChange} // Optional
                />
            </SpaceBetween>
        </Box>
    );
}

export default Introduction;