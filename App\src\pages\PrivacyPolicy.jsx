// src/pages/PrivacyPolicy.jsx
import { ContentLayout, Header, Container, SpaceBetween } from "@cloudscape-design/components";

function PrivacyPolicy() {
  return (
    <ContentLayout
      header={
        <Header variant="h1">Privacy Policy</Header>
      }
    >
      <Container>
        <SpaceBetween size="l">
          <div>
            <h2>Privacy Policy</h2>
            <p>Last updated: {new Date().toLocaleDateString()}</p>
            
            <h3>1. Information We Collect</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>

            <h3>2. How We Use Your Information</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>

            <h3>3. Information Sharing and Disclosure</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>
          </div>
        </SpaceBetween>
      </Container>
    </ContentLayout>
  );
}

export default PrivacyPolicy;
