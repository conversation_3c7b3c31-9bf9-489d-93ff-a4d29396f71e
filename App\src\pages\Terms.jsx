// src/pages/Terms.jsx
import { ContentLayout, Header, Container, SpaceBetween } from "@cloudscape-design/components";

function Terms() {
  return (
    <ContentLayout
      header={
        <Header variant="h1">Terms of Use</Header>
      }
    >
      <Container>
        <SpaceBetween size="l">
          <div>
            <h2>Terms of Use</h2>
            <p>Last updated: {new Date().toLocaleDateString()}</p>
            
            <h3>1. Acceptance of Terms</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>

            <h3>2. Use License</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>

            <h3>3. Disclaimer</h3>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>
          </div>
        </SpaceBetween>
      </Container>
    </ContentLayout>
  );
}

export default Terms;
