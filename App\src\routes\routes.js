// src/routes/routes.js
import Introduction from '../pages/Introduction';

export const routes = [
  {
    id: "introduction",
    path: "/introduction",
    href: "/introduction",
    title: "Introduction",
    description: "Get started with AWS Workshop Portal",
    component: Introduction,
    items: [] // for nested routes if needed
  },
  {
    id: "compute",
    path: "/compute",
    href: "/compute",
    title: "Compute Services",
    description: "Learn about EC2, Lambda, and more",
    component: () => <div>Compute Services Page</div>, // placeholder
    items: []
  },
  {
    id: "storage",
    path: "/storage",
    href: "/storage",
    title: "Storage Services",
    description: "Explore S3, EBS, and EFS",
    component: () => <div>Storage Services Page</div>, // placeholder
    items: []
  },
  {
    id: "database",
    path: "/database",
    href: "/database",
    title: "Database Services",
    description: "Discover RDS, DynamoDB, and more",
    component: () => <div>Database Services Page</div>, // placeholder
    items: []
  },
  {
    id: "networking",
    path: "/networking",
    href: "/networking",
    title: "Networking & Content Delivery",
    description: "Learn about VPC, Route 53, and CloudFront",
    component: () => <div>Networking Services Page</div>, // placeholder
    items: []
  }
];

// Helper function to get route by path
export const getRouteByPath = (path) => {
  return routes.find(route => route.path === path);
};

// Helper function to get all navigation items
export const getNavigationItems = () => {
  return routes.map(route => ({
    type: "link",
    text: route.title,
    href: route.href,
    description: route.description,
    items: route.items
  }));
};

// Helper function to get breadcrumb items
export const getBreadcrumbItems = (path) => {
  const route = getRouteByPath(path);
  if (!route) return [];

  return [
    { text: "Home", href: "/" },
    { text: route.title, href: route.href }
  ];
};
