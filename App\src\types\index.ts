// src/types/index.ts

import { DateRangePickerProps } from "@cloudscape-design/components/date-range-picker";

// Represents the core data structure for a resource item
export interface ResourceItem {
  id: number;
  name: string;
  type: ResourceType; // Use a specific union type if possible
  description: string;
  imageUrl: string; // Placeholder
  created: string; // ISO Date string
  status: ResourceStatus; // Use a specific union type
  region: AwsRegion; // Use a specific union type
  tags: string[];
  cost: number; // Store as number
  owner: string;
  lastModified: string; // ISO Date string
  metrics: ResourceMetricsData;
}

// Type for the metrics data associated with a resource
export interface ResourceMetricsData {
  cpu: number;
  memory: number;
  network: number;
  storage: number;
}

// Define known resource types (expand as needed)
export type ResourceType =
  | "EC2 instance"
  | "S3 Bucket"
  | "RDS Instance"
  | "Lambda Function";

// Define known resource statuses (expand as needed)
export type ResourceStatus =
  // EC2
  | "Running"
  | "Stopped"
  | "Pending"
  | "Terminated"
  // S3
  | "Active"
  | "Suspended"
  | "Archived"
  // RDS
  | "Available"
  | "Creating"
  | "Backing-up"
  | "Maintenance"
  // Lambda & others
  | "Updating"
  | "Inactive";

// Define known AWS regions (expand as needed)
export type AwsRegion =
  | "us-east-1"
  | "us-west-2"
  | "eu-west-1"
  | "ap-southeast-1";

// Option type for Cloudscape Select components
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

// Option type used in CollectionPreferences
export interface ValueLabelOption {
  value: number | string;
  label: string;
}

// Structure for visible content options in CollectionPreferences
export interface VisibleContentOption {
  id: string;
  label: string;
  editable?: boolean;
}

export interface VisibleContentOptionsGroup {
  label: string;
  options: VisibleContentOption[];
}

// Structure for sortable fields options
export interface SortableFieldOption extends ValueLabelOption {
  // Restrict value to only the keys of ResourceItem that are sortable
  value: keyof Pick<
    ResourceItem,
    "created" | "name" | "type" | "status" | "cost"
  >;
}

// State managed by CollectionPreferences
export interface CollectionPreferencesState {
  pageSize?: number;
  visibleContent?: string[];
  wrapLines?: boolean;
  // Add other preferences like custom fields if needed
}

// State for sorting
export interface SortState {
  field: SortableFieldOption["value"];
  ascending: boolean;
}

// State for pagination
export interface PaginationState {
  currentPage: number;
  pageSize: number;
}

// State managed by the useResourceFilters hook
export interface FiltersState {
  searchQuery: string;
  selectedType: SelectOption;
  selectedRegion: SelectOption;
  selectedStatus: SelectOption;
  // Use the specific type from Cloudscape components
  dateRange: DateRangePickerProps.Value | null;
}