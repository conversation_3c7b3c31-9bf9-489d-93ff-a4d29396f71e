// src/utils/helpers.js
import { StatusIndicatorProps } from "@cloudscape-design/components";

// Status mapping for Cloudscape StatusIndicator
const STATUS_TYPE_MAP = {
    Running: 'success',
    Available: 'success',
    Active: 'success',
    Stopped: 'error',
    Terminated: 'error',
    Inactive: 'error',
    Pending: 'pending',
    Creating: 'in-progress',
    Updating: 'in-progress',
    'Backing-up': 'in-progress',
    Maintenance: 'warning',
    Suspended: 'warning',
    Archived: 'info'
};

export const getStatusIndicatorType = (status) => {
    return STATUS_TYPE_MAP[status] || 'info';
};

// Format cost for display
export const formatCurrency = (value) => {
    if (typeof value === 'number') {
        return `$${value.toFixed(2)}`;
    }
    return typeof value === 'string' ? value : '$--.--';
};
