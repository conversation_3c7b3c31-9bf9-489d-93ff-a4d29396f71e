// src/utils/mockData.js
function generateRandomDate(start, end) {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).toISOString();
}

const RESOURCE_TYPES_CONFIG = [
    {
        type: "EC2 instance",
        prefix: "i-",
        descriptionPrefix: "Amazon EC2 instance for",
        status: ["Running", "Stopped", "Pending", "Terminated"],
        region: ["us-east-1", "us-west-2", "eu-west-1", "ap-southeast-1"],
        tags: ["web", "app", "database", "cache"]
    },
    // ... (rest of your config remains the same)
];

const PURPOSES = ["production", "development", "testing", "staging", "qa", "backup", "analytics", "processing"];
const ENVIRONMENTS = ["prod", "dev", "test", "stage", "qa"];
const OWNERS = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"];

export function generateItems(count) {
    const items = [];
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 6);

    for (let i = 0; i < count; i++) {
        const resourceTypeConfig = RESOURCE_TYPES_CONFIG[Math.floor(Math.random() * RESOURCE_TYPES_CONFIG.length)];
        const purpose = PURPOSES[Math.floor(Math.random() * PURPOSES.length)];
        const env = ENVIRONMENTS[Math.floor(Math.random() * ENVIRONMENTS.length)];
        const randomId = Math.random().toString(36).substring(2, 10);
        const status = resourceTypeConfig.status[Math.floor(Math.random() * resourceTypeConfig.status.length)];
        const region = resourceTypeConfig.region[Math.floor(Math.random() * resourceTypeConfig.region.length)];
        const tags = [resourceTypeConfig.tags[Math.floor(Math.random() * resourceTypeConfig.tags.length)], env, purpose];
        const cost = parseFloat((Math.random() * 100).toFixed(2));
        const owner = OWNERS[Math.floor(Math.random() * OWNERS.length)];

        const metrics = {
            cpu: Math.random() * 100,
            memory: Math.random() * 100,
            network: Math.random() * 1000,
            storage: Math.random() * 500
        };

        items.push({
            id: i + 1,
            name: `${resourceTypeConfig.prefix}${randomId}-${env}`,
            type: resourceTypeConfig.type,
            description: `${resourceTypeConfig.descriptionPrefix} ${purpose} in ${env} environment`,
            imageUrl: `https://example.com/${resourceTypeConfig.type.toLowerCase().replace(' ', '-')}.png`,
            created: generateRandomDate(startDate, endDate),
            status,
            region,
            tags,
            cost,
            owner,
            lastModified: generateRandomDate(startDate, endDate),
            metrics
        });
    }

    return items.sort((a, b) => new Date(b.created).getTime() - new Date(a.created).getTime());
}

export const getUniqueValues = (items, key) => {
    if (!items || items.length === 0) {
        return [];
    }
    const uniqueValues = Array.from(new Set(items.map(item => item[key])));
    return uniqueValues.sort((a, b) => {
        if (a < b) return -1;
        if (a > b) return 1;
        return 0;
    });
};
