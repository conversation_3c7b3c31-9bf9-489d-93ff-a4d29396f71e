# AWS CI/CD Pipeline Summary for Amazonians App

This document summarizes the AWS resources and commands used to set up the CI/CD pipeline for the React application.

**Region:** `us-east-1`

## 1. AWS CodeArtifact

*   **Domain Name:** `amazonians-domain` (Note: AWS enforces lowercase for operations like login)
*   **Repository Name:** `Amazonians-repo`
*   **Domain Owner Account ID:** `************`
*   **Commands:**
    ```bash
    # Create Domain (AWS likely created it as lowercase 'amazonians-domain')
    aws codeartifact create-domain --domain Amazonians-domain --region us-east-1 

    # Create Repository
    aws codeartifact create-repository --domain amazonians-domain --repository Amazonians-repo --region us-east-1

    # Associate npmjs external connection
    aws codeartifact associate-external-connection --domain amazonians-domain --repository Amazonians-repo --external-connection npmjs --region us-east-1
    ```
*   **Buildspec Update (`App/buildspec.yml`):** Added CodeArtifact login command (corrected to lowercase domain).
    ```yaml
    # ... inside install phase commands:
    - aws codeartifact login --tool npm --domain amazonians-domain --domain-owner ************ --repository Amazonians-repo --region us-east-1
    - npm install
    ```

## 2. AWS CodeStar Connection (GitHub)

*   **Connection ARN:** `arn:aws:codeconnections:us-east-1:************:connection/7287ac6d-e114-44de-a4cb-f8b31e9766cc`
*   **GitHub Repository:** `Amazonians-click/App`
*   **Branch:** `main`

## 3. IAM Roles

*   **CodeBuild Service Role:**
    *   Name: `CodeBuildServiceRole_amazoniansapp`
    *   ARN: `arn:aws:iam::************:role/CodeBuildServiceRole_amazoniansapp`
*   **CodePipeline Service Role:**
    *   Name: `CodePipelineServiceRole_AmazoniansApp`
    *   ARN: `arn:aws:iam::************:role/CodePipelineServiceRole_AmazoniansApp`

## 4. AWS CodeBuild

*   **Project Name:** `amazonians-app-build`
*   **Project ARN:** `arn:aws:codebuild:us-east-1:************:project/amazonians-app-build`
*   **Command:**
    ```bash
    aws codebuild create-project --region us-east-1 --cli-input-json "{ \"name\": \"amazonians-app-build\", \"source\": { \"type\": \"GITHUB\", \"location\": \"https://github.com/Amazonians-click/App.git\", \"gitCloneDepth\": 1, \"buildspec\": \"App/buildspec.yml\", \"auth\": { \"type\": \"OAUTH\", \"resource\": \"arn:aws:codeconnections:us-east-1:************:connection/7287ac6d-e114-44de-a4cb-f8b31e9766cc\" }, \"reportBuildStatus\": false, \"insecureSsl\": false }, \"artifacts\": { \"type\": \"NO_ARTIFACTS\" }, \"environment\": { \"type\": \"LINUX_CONTAINER\", \"image\": \"aws/codebuild/standard:7.0\", \"computeType\": \"BUILD_GENERAL1_SMALL\", \"privilegedMode\": false, \"environmentVariables\": [] }, \"serviceRole\": \"arn:aws:iam::************:role/CodeBuildServiceRole_amazoniansapp\", \"logsConfig\": { \"cloudWatchLogs\": { \"status\": \"ENABLED\" } } }"
    ```

## 5. AWS CodePipeline

*   **Pipeline Name:** `amazonians-app-pipeline`
*   **Artifact S3 Bucket:** `codepipeline-us-east-1-amazonians-artifacts`
*   **Deployment S3 Bucket:** `amazonians-app` (Configured for static website hosting)
*   **Command:**
    ```bash
    aws codepipeline create-pipeline --region us-east-1 --cli-input-json "{ \"pipeline\": { \"name\": \"amazonians-app-pipeline\", \"roleArn\": \"arn:aws:iam::************:role/CodePipelineServiceRole_AmazoniansApp\", \"artifactStore\": { \"type\": \"S3\", \"location\": \"codepipeline-us-east-1-amazonians-artifacts\" }, \"stages\": [ { \"name\": \"Source\", \"actions\": [ { \"name\": \"SourceAction\", \"actionTypeId\": { \"category\": \"Source\", \"owner\": \"AWS\", \"provider\": \"CodeStarSourceConnection\", \"version\": \"1\" }, \"runOrder\": 1, \"configuration\": { \"ConnectionArn\": \"arn:aws:codeconnections:us-east-1:************:connection/7287ac6d-e114-44de-a4cb-f8b31e9766cc\", \"FullRepositoryId\": \"Amazonians-click/App\", \"BranchName\": \"main\", \"OutputArtifactFormat\": \"CODE_ZIP\" }, \"outputArtifacts\": [ { \"name\": \"SourceOutput\" } ], \"region\": \"us-east-1\" } ] }, { \"name\": \"Build\", \"actions\": [ { \"name\": \"BuildAction\", \"actionTypeId\": { \"category\": \"Build\", \"owner\": \"AWS\", \"provider\": \"CodeBuild\", \"version\": \"1\" }, \"runOrder\": 1, \"configuration\": { \"ProjectName\": \"amazonians-app-build\" }, \"inputArtifacts\": [ { \"name\": \"SourceOutput\" } ], \"outputArtifacts\": [ { \"name\": \"BuildOutput\" } ], \"region\": \"us-east-1\" } ] }, { \"name\": \"Deploy\", \"actions\": [ { \"name\": \"DeployAction\", \"actionTypeId\": { \"category\": \"Deploy\", \"owner\": \"AWS\", \"provider\": \"S3\", \"version\": \"1\" }, \"runOrder\": 1, \"configuration\": { \"BucketName\": \"amazonians-app\", \"Extract\": \"true\" }, \"inputArtifacts\": [ { \"name\": \"BuildOutput\" } ], \"region\": \"us-east-1\" } ] } ], \"version\": 1 } }"
